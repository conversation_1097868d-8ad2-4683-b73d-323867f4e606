[INFO] Scanning for projects...
[WARNING] 
[WARNING] Some problems were encountered while building the effective model for cn.ijiami.detection:detection-service-impl:jar:1.0-SNAPSHOT
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: cn.ijiami.framework:ijiami-framework-utils:jar -> duplicate declaration of version (?) @ line 113, column 15
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: org.springframework:spring-test:jar -> duplicate declaration of version (?) @ line 179, column 21
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: cn.ijiami.framework:ijiami-framework-utils:jar -> duplicate declaration of version (?) @ line 183, column 21
[WARNING] 
[WARNING] It is highly recommended to fix these problems because they threaten the stability of your build.
[WARNING] 
[WARNING] For this reason, future Maven versions might no longer support building such malformed projects.
[WARNING] 
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Build Order:
[INFO] 
[INFO] privacy-detection-web                                              [pom]
[INFO] detection-service-api                                              [jar]
[INFO] detection-core                                                     [jar]
[INFO] detection-service-impl                                             [jar]
[INFO] detection-rest                                                     [jar]
[INFO] detection-web                                                      [war]
[INFO] 
[INFO] -------------< cn.ijiami.detection:privacy-detection-web >--------------
[INFO] Building privacy-detection-web 1.0-SNAPSHOT                        [1/6]
[INFO]   from pom.xml
[INFO] --------------------------------[ pom ]---------------------------------
[INFO] 
[INFO] --- dependency:3.2.0:tree (default-cli) @ privacy-detection-web ---
[INFO] cn.ijiami.detection:privacy-detection-web:pom:1.0-SNAPSHOT
[INFO] +- cn.ijiami.ai:ijiami-ai-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-web:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-web:jar:2.6.15:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-json:jar:2.6.15:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.14.2:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.14.2:compile
[INFO] |  |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.14.2:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot-starter-tomcat:jar:2.6.15:compile
[INFO] |  |  |     +- org.apache.tomcat.embed:tomcat-embed-core:jar:9.0.74:compile
[INFO] |  |  |     \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:9.0.74:compile
[INFO] |  |  \- com.alibaba.fastjson2:fastjson2:jar:2.0.38:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-swagger:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  +- com.github.xiaoymin:knife4j-spring-boot-starter:jar:3.0.3:compile
[INFO] |  |  |  +- com.github.xiaoymin:knife4j-spring-boot-autoconfigure:jar:3.0.3:compile
[INFO] |  |  |  |  +- com.github.xiaoymin:knife4j-spring:jar:3.0.3:compile
[INFO] |  |  |  |  |  +- com.github.xiaoymin:knife4j-annotations:jar:3.0.3:compile
[INFO] |  |  |  |  |  |  \- io.swagger.core.v3:swagger-annotations:jar:2.1.2:compile
[INFO] |  |  |  |  |  +- com.github.xiaoymin:knife4j-core:jar:3.0.3:compile
[INFO] |  |  |  |  |  +- org.javassist:javassist:jar:3.25.0-GA:compile
[INFO] |  |  |  |  |  +- io.springfox:springfox-swagger2:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-spi:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-schema:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-swagger-common:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-spring-web:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  |  \- io.github.classgraph:classgraph:jar:4.8.83:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-spring-webflux:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  \- org.mapstruct:mapstruct:jar:1.3.1.Final:runtime
[INFO] |  |  |  |  |  +- io.springfox:springfox-spring-webmvc:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  \- io.springfox:springfox-core:jar:3.0.0:compile
[INFO] |  |  |  |  |  |     \- net.bytebuddy:byte-buddy:jar:1.11.22:compile
[INFO] |  |  |  |  |  +- io.springfox:springfox-oas:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  \- io.swagger.core.v3:swagger-models:jar:2.1.2:compile
[INFO] |  |  |  |  |  +- io.springfox:springfox-bean-validators:jar:3.0.0:compile
[INFO] |  |  |  |  |  +- io.swagger:swagger-models:jar:1.5.22:compile
[INFO] |  |  |  |  |  \- io.swagger:swagger-core:jar:1.5.22:compile
[INFO] |  |  |  |  |     \- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.14.2:compile
[INFO] |  |  |  |  \- io.springfox:springfox-boot-starter:jar:3.0.0:compile
[INFO] |  |  |  |     +- io.springfox:springfox-data-rest:jar:3.0.0:compile
[INFO] |  |  |  |     +- org.springframework.plugin:spring-plugin-core:jar:2.0.0.RELEASE:compile
[INFO] |  |  |  |     \- org.springframework.plugin:spring-plugin-metadata:jar:2.0.0.RELEASE:compile
[INFO] |  |  |  \- com.github.xiaoymin:knife4j-spring-ui:jar:3.0.3:compile
[INFO] |  |  \- io.swagger:swagger-annotations:jar:1.5.22:compile
[INFO] |  +- javax.validation:validation-api:jar:2.0.1.Final:compile
[INFO] |  \- cn.ijiami.ai:ijiami-ai-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |     +- cn.ijiami.ai:ijiami-ai-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |     \- cn.ijiami.framework:ijiami-framework-ai:jar:2.6.8-SNAPSHOT:compile
[INFO] |        \- com.squareup.okhttp3:okhttp:jar:4.9.3:compile
[INFO] |           +- com.squareup.okio:okio:jar:2.8.0:compile
[INFO] |           |  \- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.6.21:compile
[INFO] |           \- org.jetbrains.kotlin:kotlin-stdlib:jar:1.6.21:compile
[INFO] |              \- org.jetbrains:annotations:jar:13.0:compile
[INFO] +- cn.ijiami.manager:ijiami-manager-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.base:ijiami-base-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.base:ijiami-base-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter-validation:jar:2.6.15:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter:jar:2.6.15:compile
[INFO] |     |  +- org.springframework.boot:spring-boot-starter-logging:jar:2.6.15:compile
[INFO] |     |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.18.0:compile
[INFO] |     |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.18.0:compile
[INFO] |     |  |  \- org.slf4j:jul-to-slf4j:jar:1.7.36:compile
[INFO] |     |  +- jakarta.annotation:jakarta.annotation-api:jar:1.3.5:compile
[INFO] |     |  \- org.yaml:snakeyaml:jar:1.33:compile
[INFO] |     +- org.apache.tomcat.embed:tomcat-embed-el:jar:9.0.74:compile
[INFO] |     \- org.hibernate.validator:hibernate-validator:jar:6.2.5.Final:compile
[INFO] |        +- jakarta.validation:jakarta.validation-api:jar:2.0.2:compile
[INFO] |        +- org.jboss.logging:jboss-logging:jar:3.4.3.Final:compile
[INFO] |        \- com.fasterxml:classmate:jar:1.5.1:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-i18n:jar:2.6.8-SNAPSHOT:compile
[INFO] |  \- org.springframework:spring-webmvc:jar:5.3.39:compile
[INFO] |     +- org.springframework:spring-aop:jar:5.3.39:compile
[INFO] |     +- org.springframework:spring-context:jar:5.3.39:compile
[INFO] |     \- org.springframework:spring-expression:jar:5.3.39:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-file:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:2.6.15:compile
[INFO] |  |  \- org.springframework.boot:spring-boot:jar:2.6.15:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-common:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.14.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.14.2:compile
[INFO] |  |     \- com.fasterxml.jackson.core:jackson-core:jar:2.14.2:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-file-fdfs:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  \- com.github.tobato:fastdfs-client:jar:1.27.2:compile
[INFO] |  |     +- org.slf4j:jcl-over-slf4j:jar:1.7.36:compile
[INFO] |  |     +- ch.qos.logback:logback-classic:jar:1.2.13:compile
[INFO] |  |     |  \- ch.qos.logback:logback-core:jar:1.2.13:compile
[INFO] |  |     +- org.apache.commons:commons-pool2:jar:2.11.1:compile
[INFO] |  |     \- net.coobird:thumbnailator:jar:0.4.8:compile
[INFO] |  \- commons-codec:commons-codec:jar:1.15:compile
[INFO] +- cn.ijiami.organ:ijiami-organ-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.organ:ijiami-organ-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  |  \- cn.ijiami.framework:ijiami-framework-mybatis:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |     +- com.mysql:mysql-connector-j:jar:8.4.0:compile
[INFO] |  |     +- org.mybatis.spring.boot:mybatis-spring-boot-starter:jar:2.2.2:compile
[INFO] |  |     |  +- org.springframework.boot:spring-boot-starter-***********************
[INFO] |  |     |  |  +- com.zaxxer:HikariCP:jar:4.0.3:compile
[INFO] |  |     |  |  \- org.springframework:spring-***********************
[INFO] |  |     |  +- org.mybatis.spring.boot:mybatis-spring-boot-autoconfigure:jar:2.2.2:compile
[INFO] |  |     |  +- org.mybatis:mybatis:jar:3.5.9:compile
[INFO] |  |     |  \- org.mybatis:mybatis-spring:jar:2.0.7:compile
[INFO] |  |     +- tk.mybatis:mapper-spring-boot-starter:jar:4.2.2:compile
[INFO] |  |     |  +- tk.mybatis:mapper-core:jar:4.2.2:compile
[INFO] |  |     |  |  \- javax.persistence:javax.persistence-api:jar:2.2:compile
[INFO] |  |     |  +- tk.mybatis:mapper-base:jar:4.2.2:compile
[INFO] |  |     |  +- tk.mybatis:mapper-weekend:jar:4.2.2:compile
[INFO] |  |     |  +- tk.mybatis:mapper-spring:jar:4.2.2:compile
[INFO] |  |     |  +- tk.mybatis:mapper-extra:jar:4.2.2:compile
[INFO] |  |     |  \- tk.mybatis:mapper-spring-boot-autoconfigure:jar:4.2.2:compile
[INFO] |  |     +- com.baomidou:mybatis-plus-boot-starter:jar:3.5.3.1:compile
[INFO] |  |     |  \- com.baomidou:mybatis-plus:jar:3.5.3.1:compile
[INFO] |  |     |     \- com.baomidou:mybatis-plus-extension:jar:3.5.3.1:compile
[INFO] |  |     |        \- com.baomidou:mybatis-plus-core:jar:3.5.3.1:compile
[INFO] |  |     |           \- com.baomidou:mybatis-plus-annotation:jar:3.5.3.1:compile
[INFO] |  |     \- com.alibaba:druid:jar:1.2.23:compile
[INFO] |  \- cn.ijiami.base:ijiami-base-common:jar:2.6.11-SNAPSHOT:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-security:jar:2.6.15:compile
[INFO] |     |  +- org.springframework.security:spring-security-config:jar:5.8.16:compile
[INFO] |     |  \- org.springframework.security:spring-security-web:jar:5.8.16:compile
[INFO] |     \- org.apache.commons:commons-jexl:jar:2.1.1:compile
[INFO] +- cn.ijiami.organ:ijiami-organ-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] +- org.springframework.data:spring-data-commons:jar:2.6.10:compile
[INFO] |  +- org.springframework:spring-core:jar:5.3.39:compile
[INFO] |  |  \- org.springframework:spring-jcl:jar:5.3.39:compile
[INFO] |  \- org.springframework:spring-beans:jar:5.3.39:compile
[INFO] +- cn.ijiami.base:ijiami-base-email:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- commons-fileupload:commons-fileupload:jar:1.5:compile
[INFO] |  \- cn.ijiami.framework:ijiami-framework-email:jar:2.6.8-SNAPSHOT:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-mail:jar:2.6.15:compile
[INFO] |     |  +- org.springframework:spring-context-support:jar:5.3.39:compile
[INFO] |     |  \- com.sun.mail:jakarta.mail:jar:1.6.7:compile
[INFO] |     |     \- com.sun.activation:jakarta.activation:jar:1.2.2:compile
[INFO] |     \- org.springframework.boot:spring-boot-starter-freemarker:jar:2.6.15:compile
[INFO] |        \- org.freemarker:freemarker:jar:2.3.32:compile
[INFO] +- com.github.oshi:oshi-core:jar:6.4.0:compile
[INFO] +- net.java.dev.jna:jna:jar:5.12.0:compile
[INFO] +- net.java.dev.jna:jna-platform:jar:5.12.0:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-utils:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- cn.hutool:hutool-all:jar:5.8.36:compile
[INFO] |  +- javax.servlet:javax.servlet-api:jar:4.0.1:compile
[INFO] |  +- commons-io:commons-io:jar:2.11.0:compile
[INFO] |  \- com.googlecode.aviator:aviator:jar:5.4.1:compile
[INFO] +- net.sf.json-lib:json-lib:jar:jdk15:2.4:compile (optional) 
[INFO] |  +- commons-beanutils:commons-beanutils:jar:1.8.0:compile
[INFO] |  +- commons-collections:commons-collections:jar:3.2.1:compile (optional) 
[INFO] |  +- commons-lang:commons-lang:jar:2.5:compile
[INFO] |  +- commons-logging:commons-logging:jar:1.1.1:compile
[INFO] |  \- net.sf.ezmorph:ezmorph:jar:1.0.6:compile (optional) 
[INFO] +- org.projectlombok:lombok:jar:1.18.24:compile
[INFO] +- com.alibaba:fastjson:jar:1.2.83:compile
[INFO] +- org.apache.poi:poi:jar:4.1.2:compile
[INFO] |  +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] |  \- com.zaxxer:SparseBitSet:jar:1.2:compile
[INFO] +- org.apache.commons:commons-collections4:jar:4.1:compile
[INFO] +- org.apache.poi:poi-ooxml:jar:4.1.2:compile
[INFO] |  +- org.apache.poi:poi-ooxml-schemas:jar:4.1.2:compile
[INFO] |  +- org.apache.commons:commons-compress:jar:1.19:compile
[INFO] |  \- com.github.virtuald:curvesapi:jar:1.06:compile
[INFO] +- org.apache.xmlbeans:xmlbeans:jar:3.1.0:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-mongodb:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-data-mongodb:jar:2.6.15:compile
[INFO] |  |  \- org.mongodb:mongodb-driver-sync:jar:4.4.2:compile
[INFO] |  |     \- org.mongodb:bson:jar:4.4.2:compile
[INFO] |  +- org.springframework.data:spring-data-mongodb:jar:3.3.10:compile
[INFO] |  |  +- org.springframework:spring-tx:jar:5.3.39:compile
[INFO] |  |  \- org.mongodb:mongodb-driver-core:jar:4.4.2:compile
[INFO] |  \- com.github.pagehelper:pagehelper:jar:5.3.3:compile
[INFO] |     \- com.github.jsqlparser:jsqlparser:jar:4.5:compile
[INFO] +- com.google.guava:guava:jar:23.0:compile
[INFO] |  +- com.google.code.findbugs:jsr305:jar:1.3.9:compile
[INFO] |  +- com.google.errorprone:error_prone_annotations:jar:2.0.18:compile
[INFO] |  +- com.google.j2objc:j2objc-annotations:jar:1.1:compile
[INFO] |  \- org.codehaus.mojo:animal-sniffer-annotations:jar:1.14:compile
[INFO] +- org.springframework.cloud:spring-cloud-starter-openfeign:jar:2.1.3.RELEASE:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-starter:jar:3.1.4:compile
[INFO] |  |  +- org.springframework.cloud:spring-cloud-context:jar:3.1.4:compile
[INFO] |  |  \- org.springframework.security:spring-security-rsa:jar:1.0.11.RELEASE:compile
[INFO] |  |     \- org.bouncycastle:bcpkix-jdk15on:jar:1.69:compile
[INFO] |  |        +- org.bouncycastle:bcprov-jdk15on:jar:1.69:compile
[INFO] |  |        \- org.bouncycastle:bcutil-jdk15on:jar:1.69:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-openfeign-core:jar:3.1.4:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-aop:jar:2.6.15:compile
[INFO] |  |  \- io.github.openfeign.form:feign-form-spring:jar:3.8.0:compile
[INFO] |  |     \- io.github.openfeign.form:feign-form:jar:3.8.0:compile
[INFO] |  +- org.springframework:spring-web:jar:5.3.39:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-commons:jar:3.1.4:compile
[INFO] |  |  \- org.springframework.security:spring-security-crypto:jar:5.8.16:compile
[INFO] |  +- io.github.openfeign:feign-core:jar:11.8:compile
[INFO] |  +- io.github.openfeign:feign-slf4j:jar:11.8:compile
[INFO] |  \- io.github.openfeign:feign-hystrix:jar:11.8:compile
[INFO] |     \- com.netflix.hystrix:hystrix-core:jar:1.5.18:compile
[INFO] |        +- com.netflix.archaius:archaius-core:jar:0.4.1:compile
[INFO] |        |  \- commons-configuration:commons-configuration:jar:1.8:compile
[INFO] |        +- io.reactivex:rxjava:jar:1.3.8:compile
[INFO] |        \- org.hdrhistogram:HdrHistogram:jar:2.1.9:compile
[INFO] +- cn.ijiami.authentication:authentication-rest:jar:2.6.10-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.authentication:authentication-service-impl:jar:2.6.10-SNAPSHOT:compile
[INFO] |  |  +- cn.ijiami.authentication:authentication-service-api:jar:2.6.10-SNAPSHOT:compile
[INFO] |  |  +- org.apache.httpcomponents:httpclient:jar:4.5.14:compile
[INFO] |  |  +- org.apache.httpcomponents:httpcore:jar:4.4.16:compile
[INFO] |  |  \- commons-httpclient:commons-httpclient:jar:3.1:compile
[INFO] |  +- cn.ijiami.manager:ijiami-manager-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.manager:ijiami-manager-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  |  \- cn.ijiami.framework:ijiami-framework-redis:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |     +- org.springframework.boot:spring-boot-starter-data-redis:jar:2.6.15:compile
[INFO] |  |     |  +- org.springframework.data:spring-data-redis:jar:2.6.10:compile
[INFO] |  |     |  |  +- org.springframework.data:spring-data-keyvalue:jar:2.6.10:compile
[INFO] |  |     |  |  \- org.springframework:spring-oxm:jar:5.3.39:compile
[INFO] |  |     |  \- io.lettuce:lettuce-core:jar:6.1.10.RELEASE:compile
[INFO] |  |     |     +- io.netty:netty-common:jar:4.1.92.Final:compile
[INFO] |  |     |     +- io.netty:netty-handler:jar:4.1.92.Final:compile
[INFO] |  |     |     |  +- io.netty:netty-resolver:jar:4.1.92.Final:compile
[INFO] |  |     |     |  +- io.netty:netty-buffer:jar:4.1.92.Final:compile
[INFO] |  |     |     |  +- io.netty:netty-transport-native-unix-common:jar:4.1.92.Final:compile
[INFO] |  |     |     |  \- io.netty:netty-codec:jar:4.1.92.Final:compile
[INFO] |  |     |     +- io.netty:netty-transport:jar:4.1.92.Final:compile
[INFO] |  |     |     \- io.projectreactor:reactor-core:jar:3.4.29:compile
[INFO] |  |     |        \- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |     \- org.springframework.boot:spring-boot-starter-cache:jar:2.6.15:compile
[INFO] |  +- cn.ijiami.manager:ijiami-manager-security:jar:2.6.11-SNAPSHOT:compile
[INFO] |  |  +- org.springframework.security:spring-security-jwt:jar:1.1.1.RELEASE:compile
[INFO] |  |  +- org.springframework.security.oauth:spring-security-oauth2:jar:2.3.8.RELEASE:compile
[INFO] |  |  |  \- org.springframework.security:spring-security-core:jar:5.8.16:compile
[INFO] |  |  +- org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13-atlassian-1:compile
[INFO] |  |  |  \- org.codehaus.jackson:jackson-core-asl:jar:1.9.13-atlassian-1:compile
[INFO] |  |  +- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:jar:2.14.2:compile
[INFO] |  |  |  +- jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.3:compile
[INFO] |  |  |  \- jakarta.activation:jakarta.activation-api:jar:1.2.2:compile
[INFO] |  |  +- org.apache.commons:commons-text:jar:1.10.0:compile
[INFO] |  |  +- cn.ijiami.framework:ijiami-framework-ldap:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot-starter-data-ldap:jar:2.6.15:compile
[INFO] |  |  |     \- org.springframework.data:spring-data-ldap:jar:2.6.10:compile
[INFO] |  |  |        \- org.springframework.ldap:spring-ldap-core:jar:2.3.8.RELEASE:compile
[INFO] |  |  \- cn.ijiami.framework:ijiami-framework-websocket:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |     \- org.springframework.boot:spring-boot-starter-websocket:jar:2.6.15:compile
[INFO] |  |        +- org.springframework:spring-messaging:jar:5.3.39:compile
[INFO] |  |        \- org.springframework:spring-websocket:jar:5.3.39:compile
[INFO] |  +- io.jsonwebtoken:jjwt:jar:0.9.0:compile
[INFO] |  +- org.aspectj:aspectjrt:jar:1.9.7:compile
[INFO] |  \- org.aspectj:aspectjweaver:jar:1.9.7:compile
[INFO] +- mysql:mysql-connector-java:jar:8.0.16:compile
[INFO] +- com.dm:DmJdbcDriver18:jar:1.8:compile
[INFO] +- cn.ijiami.detection:privacy-android-server-client:jar:1.0-SNAPSHOT:compile
[INFO] |  \- cn.ijiami.detection:privacy-server-client-base:jar:1.0-SNAPSHOT:compile
[INFO] +- cn.ijiami.detection:privacy-ios-server-client:jar:1.0-SNAPSHOT:compile
[INFO] +- org.slf4j:slf4j-api:jar:1.7.36:compile
[INFO] \- org.apache.commons:commons-lang3:jar:3.12.0:compile
[INFO] 
[INFO] -------------< cn.ijiami.detection:detection-service-api >--------------
[INFO] Building detection-service-api 1.0-SNAPSHOT                        [2/6]
[INFO]   from detection-service-api\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.2.0:tree (default-cli) @ detection-service-api ---
[INFO] cn.ijiami.detection:detection-service-api:jar:1.0-SNAPSHOT
[INFO] +- cn.ijiami.framework:ijiami-framework-mongodb:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-data-mongodb:jar:2.6.15:compile
[INFO] |  |  \- org.mongodb:mongodb-driver-sync:jar:4.4.2:compile
[INFO] |  |     \- org.mongodb:bson:jar:4.4.2:compile
[INFO] |  +- org.springframework.data:spring-data-mongodb:jar:3.3.10:compile
[INFO] |  |  +- org.springframework:spring-tx:jar:5.3.39:compile
[INFO] |  |  +- org.springframework:spring-context:jar:5.3.39:compile
[INFO] |  |  +- org.springframework:spring-expression:jar:5.3.39:compile
[INFO] |  |  \- org.mongodb:mongodb-driver-core:jar:4.4.2:compile
[INFO] |  \- com.github.pagehelper:pagehelper:jar:5.3.3:compile
[INFO] |     \- com.github.jsqlparser:jsqlparser:jar:4.5:compile
[INFO] +- cn.ijiami.message:ijiami-message-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-mybatis:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  +- com.mysql:mysql-connector-j:jar:8.4.0:compile
[INFO] |  |  +- org.mybatis.spring.boot:mybatis-spring-boot-starter:jar:2.2.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-***********************
[INFO] |  |  |  |  +- com.zaxxer:HikariCP:jar:4.0.3:compile
[INFO] |  |  |  |  \- org.springframework:spring-***********************
[INFO] |  |  |  +- org.mybatis.spring.boot:mybatis-spring-boot-autoconfigure:jar:2.2.2:compile
[INFO] |  |  |  +- org.mybatis:mybatis:jar:3.5.9:compile
[INFO] |  |  |  \- org.mybatis:mybatis-spring:jar:2.0.7:compile
[INFO] |  |  +- tk.mybatis:mapper-spring-boot-starter:jar:4.2.2:compile
[INFO] |  |  |  +- tk.mybatis:mapper-core:jar:4.2.2:compile
[INFO] |  |  |  |  \- javax.persistence:javax.persistence-api:jar:2.2:compile
[INFO] |  |  |  +- tk.mybatis:mapper-base:jar:4.2.2:compile
[INFO] |  |  |  +- tk.mybatis:mapper-weekend:jar:4.2.2:compile
[INFO] |  |  |  +- tk.mybatis:mapper-spring:jar:4.2.2:compile
[INFO] |  |  |  +- tk.mybatis:mapper-extra:jar:4.2.2:compile
[INFO] |  |  |  \- tk.mybatis:mapper-spring-boot-autoconfigure:jar:4.2.2:compile
[INFO] |  |  +- com.baomidou:mybatis-plus-boot-starter:jar:3.5.3.1:compile
[INFO] |  |  |  \- com.baomidou:mybatis-plus:jar:3.5.3.1:compile
[INFO] |  |  |     \- com.baomidou:mybatis-plus-extension:jar:3.5.3.1:compile
[INFO] |  |  |        \- com.baomidou:mybatis-plus-core:jar:3.5.3.1:compile
[INFO] |  |  |           \- com.baomidou:mybatis-plus-annotation:jar:3.5.3.1:compile
[INFO] |  |  \- com.alibaba:druid:jar:1.2.23:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:2.6.15:compile
[INFO] |  |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:9.0.74:compile
[INFO] |  |  \- org.hibernate.validator:hibernate-validator:jar:6.2.5.Final:compile
[INFO] |  |     +- jakarta.validation:jakarta.validation-api:jar:2.0.2:compile
[INFO] |  |     +- org.jboss.logging:jboss-logging:jar:3.4.3.Final:compile
[INFO] |  |     \- com.fasterxml:classmate:jar:1.5.1:compile
[INFO] |  \- cn.ijiami.framework:ijiami-framework-swagger:jar:2.6.8-SNAPSHOT:compile
[INFO] |     +- com.github.xiaoymin:knife4j-spring-boot-starter:jar:3.0.3:compile
[INFO] |     |  +- com.github.xiaoymin:knife4j-spring-boot-autoconfigure:jar:3.0.3:compile
[INFO] |     |  |  +- com.github.xiaoymin:knife4j-spring:jar:3.0.3:compile
[INFO] |     |  |  |  +- com.github.xiaoymin:knife4j-annotations:jar:3.0.3:compile
[INFO] |     |  |  |  |  \- io.swagger.core.v3:swagger-annotations:jar:2.1.2:compile
[INFO] |     |  |  |  +- com.github.xiaoymin:knife4j-core:jar:3.0.3:compile
[INFO] |     |  |  |  +- org.javassist:javassist:jar:3.25.0-GA:compile
[INFO] |     |  |  |  +- io.springfox:springfox-swagger2:jar:3.0.0:compile
[INFO] |     |  |  |  |  +- io.springfox:springfox-spi:jar:3.0.0:compile
[INFO] |     |  |  |  |  +- io.springfox:springfox-schema:jar:3.0.0:compile
[INFO] |     |  |  |  |  +- io.springfox:springfox-swagger-common:jar:3.0.0:compile
[INFO] |     |  |  |  |  +- io.springfox:springfox-spring-web:jar:3.0.0:compile
[INFO] |     |  |  |  |  |  \- io.github.classgraph:classgraph:jar:4.8.83:compile
[INFO] |     |  |  |  |  +- io.springfox:springfox-spring-webflux:jar:3.0.0:compile
[INFO] |     |  |  |  |  \- org.mapstruct:mapstruct:jar:1.3.1.Final:runtime
[INFO] |     |  |  |  +- io.springfox:springfox-spring-webmvc:jar:3.0.0:compile
[INFO] |     |  |  |  |  \- io.springfox:springfox-core:jar:3.0.0:compile
[INFO] |     |  |  |  |     \- net.bytebuddy:byte-buddy:jar:1.11.22:compile
[INFO] |     |  |  |  +- io.springfox:springfox-oas:jar:3.0.0:compile
[INFO] |     |  |  |  |  \- io.swagger.core.v3:swagger-models:jar:2.1.2:compile
[INFO] |     |  |  |  +- io.springfox:springfox-bean-validators:jar:3.0.0:compile
[INFO] |     |  |  |  +- io.swagger:swagger-models:jar:1.5.22:compile
[INFO] |     |  |  |  \- io.swagger:swagger-core:jar:1.5.22:compile
[INFO] |     |  |  |     \- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.14.2:compile
[INFO] |     |  |  \- io.springfox:springfox-boot-starter:jar:3.0.0:compile
[INFO] |     |  |     +- io.springfox:springfox-data-rest:jar:3.0.0:compile
[INFO] |     |  |     +- org.springframework.plugin:spring-plugin-core:jar:2.0.0.RELEASE:compile
[INFO] |     |  |     \- org.springframework.plugin:spring-plugin-metadata:jar:2.0.0.RELEASE:compile
[INFO] |     |  \- com.github.xiaoymin:knife4j-spring-ui:jar:3.0.3:compile
[INFO] |     \- io.swagger:swagger-annotations:jar:1.5.22:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-openfeign:jar:2.6.8-SNAPSHOT:compile
[INFO] |  \- org.springframework.cloud:spring-cloud-starter-loadbalancer:jar:3.1.4:compile
[INFO] |     +- org.springframework.cloud:spring-cloud-loadbalancer:jar:3.1.4:compile
[INFO] |     |  +- io.projectreactor:reactor-core:jar:3.4.29:compile
[INFO] |     |  |  \- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |     |  \- io.projectreactor.addons:reactor-extra:jar:3.4.10:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-cache:jar:2.6.15:compile
[INFO] |     |  \- org.springframework:spring-context-support:jar:5.3.39:compile
[INFO] |     \- com.stoyanr:evictor:jar:1.0.0:compile
[INFO] +- com.ijm.ios:RuntimeDetection:jar:0.0.2-SNAPSHOT:compile
[INFO] |  +- commons-codec:commons-codec:jar:1.15:compile
[INFO] |  +- commons-io:commons-io:jar:2.5:compile
[INFO] |  +- org.apache.commons:commons-compress:jar:1.20:compile
[INFO] |  \- commons-logging:commons-logging-api:jar:1.1:compile
[INFO] +- com.ocpframework.ocp:ocp-sdk-detect-enginer:jar:1.0-SNAPSHOT:compile
[INFO] |  +- org.xerial:sqlite-jdbc:jar:********:compile
[INFO] |  +- commons-lang:commons-lang:jar:2.6:compile
[INFO] |  +- com.ocpframework.ocp:ocp-sdk-detect-common:jar:1.0-SNAPSHOT:compile
[INFO] |  +- cn.hutool:hutool-all:jar:5.8.36:compile
[INFO] |  +- org.springframework:spring-core:jar:5.3.39:compile
[INFO] |  |  \- org.springframework:spring-jcl:jar:5.3.39:compile
[INFO] |  \- org.eclipse.jgit:org.eclipse.jgit:jar:5.13.1.202206130422-r:compile
[INFO] |     \- com.googlecode.javaewah:JavaEWAH:jar:1.1.13:compile
[INFO] +- org.springframework.boot:spring-boot-starter-aop:jar:2.6.15:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter:jar:2.6.15:compile
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:2.6.15:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:2.6.15:compile
[INFO] |  |  |  +- ch.qos.logback:logback-classic:jar:1.2.13:compile
[INFO] |  |  |  |  \- ch.qos.logback:logback-core:jar:1.2.13:compile
[INFO] |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.18.0:compile
[INFO] |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.18.0:compile
[INFO] |  |  |  \- org.slf4j:jul-to-slf4j:jar:1.7.36:compile
[INFO] |  |  +- jakarta.annotation:jakarta.annotation-api:jar:1.3.5:compile
[INFO] |  |  \- org.yaml:snakeyaml:jar:1.33:compile
[INFO] |  +- org.springframework:spring-aop:jar:5.3.39:compile
[INFO] |  \- org.aspectj:aspectjweaver:jar:1.9.7:compile
[INFO] +- com.google.code.gson:gson:jar:2.8.9:compile
[INFO] +- cn.ijiami.ai:ijiami-ai-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-web:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-web:jar:2.6.15:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-json:jar:2.6.15:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.14.2:compile
[INFO] |  |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.14.2:compile
[INFO] |  |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.14.2:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot-starter-tomcat:jar:2.6.15:compile
[INFO] |  |  |     +- org.apache.tomcat.embed:tomcat-embed-core:jar:9.0.74:compile
[INFO] |  |  |     \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:9.0.74:compile
[INFO] |  |  \- com.alibaba.fastjson2:fastjson2:jar:2.0.38:compile
[INFO] |  +- javax.validation:validation-api:jar:2.0.1.Final:compile
[INFO] |  \- cn.ijiami.ai:ijiami-ai-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |     +- cn.ijiami.ai:ijiami-ai-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |     \- cn.ijiami.framework:ijiami-framework-ai:jar:2.6.8-SNAPSHOT:compile
[INFO] |        \- com.squareup.okhttp3:okhttp:jar:4.9.3:compile
[INFO] |           +- com.squareup.okio:okio:jar:2.8.0:compile
[INFO] |           |  \- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.6.21:compile
[INFO] |           \- org.jetbrains.kotlin:kotlin-stdlib:jar:1.6.21:compile
[INFO] |              \- org.jetbrains:annotations:jar:13.0:compile
[INFO] +- cn.ijiami.manager:ijiami-manager-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.base:ijiami-base-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  \- cn.ijiami.base:ijiami-base-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-i18n:jar:2.6.8-SNAPSHOT:compile
[INFO] |  \- org.springframework:spring-webmvc:jar:5.3.39:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-file:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:2.6.15:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-common:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.14.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.14.2:compile
[INFO] |  |     \- com.fasterxml.jackson.core:jackson-core:jar:2.14.2:compile
[INFO] |  \- cn.ijiami.framework:ijiami-framework-file-fdfs:jar:2.6.8-SNAPSHOT:compile
[INFO] |     \- com.github.tobato:fastdfs-client:jar:1.27.2:compile
[INFO] |        +- org.slf4j:jcl-over-slf4j:jar:1.7.36:compile
[INFO] |        +- org.apache.commons:commons-pool2:jar:2.11.1:compile
[INFO] |        \- net.coobird:thumbnailator:jar:0.4.8:compile
[INFO] +- cn.ijiami.organ:ijiami-organ-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.organ:ijiami-organ-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  \- cn.ijiami.base:ijiami-base-common:jar:2.6.11-SNAPSHOT:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-security:jar:2.6.15:compile
[INFO] |     |  +- org.springframework.security:spring-security-config:jar:5.8.16:compile
[INFO] |     |  \- org.springframework.security:spring-security-web:jar:5.8.16:compile
[INFO] |     \- org.apache.commons:commons-jexl:jar:2.1.1:compile
[INFO] +- cn.ijiami.organ:ijiami-organ-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] +- org.springframework.data:spring-data-commons:jar:2.6.10:compile
[INFO] |  \- org.springframework:spring-beans:jar:5.3.39:compile
[INFO] +- cn.ijiami.base:ijiami-base-email:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- commons-fileupload:commons-fileupload:jar:1.5:compile
[INFO] |  \- cn.ijiami.framework:ijiami-framework-email:jar:2.6.8-SNAPSHOT:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-mail:jar:2.6.15:compile
[INFO] |     |  \- com.sun.mail:jakarta.mail:jar:1.6.7:compile
[INFO] |     |     \- com.sun.activation:jakarta.activation:jar:1.2.2:compile
[INFO] |     \- org.springframework.boot:spring-boot-starter-freemarker:jar:2.6.15:compile
[INFO] |        \- org.freemarker:freemarker:jar:2.3.32:compile
[INFO] +- com.github.oshi:oshi-core:jar:6.4.0:compile
[INFO] +- net.java.dev.jna:jna:jar:5.12.0:compile
[INFO] +- net.java.dev.jna:jna-platform:jar:5.12.0:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-utils:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- javax.servlet:javax.servlet-api:jar:4.0.1:compile
[INFO] |  \- com.googlecode.aviator:aviator:jar:5.4.1:compile
[INFO] +- net.sf.json-lib:json-lib:jar:jdk15:2.4:compile (optional) 
[INFO] |  +- commons-beanutils:commons-beanutils:jar:1.8.0:compile
[INFO] |  +- commons-collections:commons-collections:jar:3.2.1:compile (optional) 
[INFO] |  +- commons-logging:commons-logging:jar:1.1.1:compile
[INFO] |  \- net.sf.ezmorph:ezmorph:jar:1.0.6:compile (optional) 
[INFO] +- org.projectlombok:lombok:jar:1.18.24:compile
[INFO] +- com.alibaba:fastjson:jar:1.2.83:compile
[INFO] +- org.apache.poi:poi:jar:4.1.2:compile
[INFO] |  +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] |  \- com.zaxxer:SparseBitSet:jar:1.2:compile
[INFO] +- org.apache.commons:commons-collections4:jar:4.1:compile
[INFO] +- org.apache.poi:poi-ooxml:jar:4.1.2:compile
[INFO] |  +- org.apache.poi:poi-ooxml-schemas:jar:4.1.2:compile
[INFO] |  \- com.github.virtuald:curvesapi:jar:1.06:compile
[INFO] +- org.apache.xmlbeans:xmlbeans:jar:3.1.0:compile
[INFO] +- com.google.guava:guava:jar:23.0:compile
[INFO] |  +- com.google.code.findbugs:jsr305:jar:1.3.9:compile
[INFO] |  +- com.google.errorprone:error_prone_annotations:jar:2.0.18:compile
[INFO] |  +- com.google.j2objc:j2objc-annotations:jar:1.1:compile
[INFO] |  \- org.codehaus.mojo:animal-sniffer-annotations:jar:1.14:compile
[INFO] +- org.springframework.cloud:spring-cloud-starter-openfeign:jar:2.1.3.RELEASE:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-starter:jar:3.1.4:compile
[INFO] |  |  +- org.springframework.cloud:spring-cloud-context:jar:3.1.4:compile
[INFO] |  |  \- org.springframework.security:spring-security-rsa:jar:1.0.11.RELEASE:compile
[INFO] |  |     \- org.bouncycastle:bcpkix-jdk15on:jar:1.69:compile
[INFO] |  |        +- org.bouncycastle:bcprov-jdk15on:jar:1.69:compile
[INFO] |  |        \- org.bouncycastle:bcutil-jdk15on:jar:1.69:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-openfeign-core:jar:3.1.4:compile
[INFO] |  |  \- io.github.openfeign.form:feign-form-spring:jar:3.8.0:compile
[INFO] |  |     \- io.github.openfeign.form:feign-form:jar:3.8.0:compile
[INFO] |  +- org.springframework:spring-web:jar:5.3.39:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-commons:jar:3.1.4:compile
[INFO] |  |  \- org.springframework.security:spring-security-crypto:jar:5.8.16:compile
[INFO] |  +- io.github.openfeign:feign-core:jar:11.8:compile
[INFO] |  +- io.github.openfeign:feign-slf4j:jar:11.8:compile
[INFO] |  \- io.github.openfeign:feign-hystrix:jar:11.8:compile
[INFO] |     \- com.netflix.hystrix:hystrix-core:jar:1.5.18:compile
[INFO] |        +- com.netflix.archaius:archaius-core:jar:0.4.1:compile
[INFO] |        |  \- commons-configuration:commons-configuration:jar:1.8:compile
[INFO] |        +- io.reactivex:rxjava:jar:1.3.8:compile
[INFO] |        \- org.hdrhistogram:HdrHistogram:jar:2.1.9:compile
[INFO] +- cn.ijiami.authentication:authentication-rest:jar:2.6.10-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.authentication:authentication-service-impl:jar:2.6.10-SNAPSHOT:compile
[INFO] |  |  +- cn.ijiami.authentication:authentication-service-api:jar:2.6.10-SNAPSHOT:compile
[INFO] |  |  +- org.apache.httpcomponents:httpclient:jar:4.5.14:compile
[INFO] |  |  +- org.apache.httpcomponents:httpcore:jar:4.4.16:compile
[INFO] |  |  \- commons-httpclient:commons-httpclient:jar:3.1:compile
[INFO] |  +- cn.ijiami.manager:ijiami-manager-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.manager:ijiami-manager-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  |  \- cn.ijiami.framework:ijiami-framework-redis:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |     \- org.springframework.boot:spring-boot-starter-data-redis:jar:2.6.15:compile
[INFO] |  |        +- org.springframework.data:spring-data-redis:jar:2.6.10:compile
[INFO] |  |        |  +- org.springframework.data:spring-data-keyvalue:jar:2.6.10:compile
[INFO] |  |        |  \- org.springframework:spring-oxm:jar:5.3.39:compile
[INFO] |  |        \- io.lettuce:lettuce-core:jar:6.1.10.RELEASE:compile
[INFO] |  |           +- io.netty:netty-common:jar:4.1.92.Final:compile
[INFO] |  |           +- io.netty:netty-handler:jar:4.1.92.Final:compile
[INFO] |  |           |  +- io.netty:netty-resolver:jar:4.1.92.Final:compile
[INFO] |  |           |  +- io.netty:netty-buffer:jar:4.1.92.Final:compile
[INFO] |  |           |  +- io.netty:netty-transport-native-unix-common:jar:4.1.92.Final:compile
[INFO] |  |           |  \- io.netty:netty-codec:jar:4.1.92.Final:compile
[INFO] |  |           \- io.netty:netty-transport:jar:4.1.92.Final:compile
[INFO] |  +- cn.ijiami.manager:ijiami-manager-security:jar:2.6.11-SNAPSHOT:compile
[INFO] |  |  +- org.springframework.security:spring-security-jwt:jar:1.1.1.RELEASE:compile
[INFO] |  |  +- org.springframework.security.oauth:spring-security-oauth2:jar:2.3.8.RELEASE:compile
[INFO] |  |  |  \- org.springframework.security:spring-security-core:jar:5.8.16:compile
[INFO] |  |  +- org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13-atlassian-1:compile
[INFO] |  |  |  \- org.codehaus.jackson:jackson-core-asl:jar:1.9.13-atlassian-1:compile
[INFO] |  |  +- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:jar:2.14.2:compile
[INFO] |  |  |  +- jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.3:compile
[INFO] |  |  |  \- jakarta.activation:jakarta.activation-api:jar:1.2.2:compile
[INFO] |  |  +- org.apache.commons:commons-text:jar:1.10.0:compile
[INFO] |  |  +- cn.ijiami.framework:ijiami-framework-ldap:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot-starter-data-ldap:jar:2.6.15:compile
[INFO] |  |  |     \- org.springframework.data:spring-data-ldap:jar:2.6.10:compile
[INFO] |  |  |        \- org.springframework.ldap:spring-ldap-core:jar:2.3.8.RELEASE:compile
[INFO] |  |  \- cn.ijiami.framework:ijiami-framework-websocket:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |     \- org.springframework.boot:spring-boot-starter-websocket:jar:2.6.15:compile
[INFO] |  |        +- org.springframework:spring-messaging:jar:5.3.39:compile
[INFO] |  |        \- org.springframework:spring-websocket:jar:5.3.39:compile
[INFO] |  +- io.jsonwebtoken:jjwt:jar:0.9.0:compile
[INFO] |  \- org.aspectj:aspectjrt:jar:1.9.7:compile
[INFO] +- mysql:mysql-connector-java:jar:8.0.16:compile
[INFO] +- com.dm:DmJdbcDriver18:jar:1.8:compile
[INFO] +- cn.ijiami.detection:privacy-android-server-client:jar:1.0-SNAPSHOT:compile
[INFO] |  \- cn.ijiami.detection:privacy-server-client-base:jar:1.0-SNAPSHOT:compile
[INFO] +- cn.ijiami.detection:privacy-ios-server-client:jar:1.0-SNAPSHOT:compile
[INFO] +- org.slf4j:slf4j-api:jar:1.7.36:compile
[INFO] \- org.apache.commons:commons-lang3:jar:3.12.0:compile
[INFO] 
[INFO] -----------------< cn.ijiami.detection:detection-core >-----------------
[INFO] Building detection-core 1.0-SNAPSHOT                               [3/6]
[INFO]   from detection-core\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.2.0:tree (default-cli) @ detection-core ---
[INFO] cn.ijiami.detection:detection-core:jar:1.0-SNAPSHOT
[INFO] +- org.csource:fastdfs-client-java:jar:1.27-SNAPSHOT:compile
[INFO] +- org.springframework.boot:spring-boot-starter-websocket:jar:2.6.15:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-web:jar:2.6.15:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:2.6.15:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:2.6.15:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.18.0:compile
[INFO] |  |  |  |  |  \- org.apache.logging.log4j:log4j-api:jar:2.18.0:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:1.7.36:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:1.3.5:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:1.33:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-json:jar:2.6.15:compile
[INFO] |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.14.2:compile
[INFO] |  |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.14.2:compile
[INFO] |  |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.14.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-tomcat:jar:2.6.15:compile
[INFO] |  |     +- org.apache.tomcat.embed:tomcat-embed-core:jar:9.0.74:compile
[INFO] |  |     \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:9.0.74:compile
[INFO] |  +- org.springframework:spring-messaging:jar:5.3.39:compile
[INFO] |  \- org.springframework:spring-websocket:jar:5.3.39:compile
[INFO] |     \- org.springframework:spring-context:jar:5.3.39:compile
[INFO] +- cn.ijiami.ai:ijiami-ai-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-web:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  \- com.alibaba.fastjson2:fastjson2:jar:2.0.38:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-swagger:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  +- com.github.xiaoymin:knife4j-spring-boot-starter:jar:3.0.3:compile
[INFO] |  |  |  +- com.github.xiaoymin:knife4j-spring-boot-autoconfigure:jar:3.0.3:compile
[INFO] |  |  |  |  +- com.github.xiaoymin:knife4j-spring:jar:3.0.3:compile
[INFO] |  |  |  |  |  +- com.github.xiaoymin:knife4j-annotations:jar:3.0.3:compile
[INFO] |  |  |  |  |  |  \- io.swagger.core.v3:swagger-annotations:jar:2.1.2:compile
[INFO] |  |  |  |  |  +- com.github.xiaoymin:knife4j-core:jar:3.0.3:compile
[INFO] |  |  |  |  |  +- org.javassist:javassist:jar:3.25.0-GA:compile
[INFO] |  |  |  |  |  +- io.springfox:springfox-swagger2:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-spi:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-schema:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-swagger-common:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-spring-web:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  |  \- io.github.classgraph:classgraph:jar:4.8.83:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-spring-webflux:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  \- org.mapstruct:mapstruct:jar:1.3.1.Final:runtime
[INFO] |  |  |  |  |  +- io.springfox:springfox-spring-webmvc:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  \- io.springfox:springfox-core:jar:3.0.0:compile
[INFO] |  |  |  |  |  |     \- net.bytebuddy:byte-buddy:jar:1.11.22:compile
[INFO] |  |  |  |  |  +- io.springfox:springfox-oas:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  \- io.swagger.core.v3:swagger-models:jar:2.1.2:compile
[INFO] |  |  |  |  |  +- io.springfox:springfox-bean-validators:jar:3.0.0:compile
[INFO] |  |  |  |  |  +- io.swagger:swagger-models:jar:1.5.22:compile
[INFO] |  |  |  |  |  \- io.swagger:swagger-core:jar:1.5.22:compile
[INFO] |  |  |  |  |     \- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.14.2:compile
[INFO] |  |  |  |  \- io.springfox:springfox-boot-starter:jar:3.0.0:compile
[INFO] |  |  |  |     +- io.springfox:springfox-data-rest:jar:3.0.0:compile
[INFO] |  |  |  |     +- org.springframework.plugin:spring-plugin-core:jar:2.0.0.RELEASE:compile
[INFO] |  |  |  |     \- org.springframework.plugin:spring-plugin-metadata:jar:2.0.0.RELEASE:compile
[INFO] |  |  |  \- com.github.xiaoymin:knife4j-spring-ui:jar:3.0.3:compile
[INFO] |  |  \- io.swagger:swagger-annotations:jar:1.5.22:compile
[INFO] |  +- javax.validation:validation-api:jar:2.0.1.Final:compile
[INFO] |  \- cn.ijiami.ai:ijiami-ai-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |     +- cn.ijiami.ai:ijiami-ai-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |     \- cn.ijiami.framework:ijiami-framework-ai:jar:2.6.8-SNAPSHOT:compile
[INFO] |        \- com.squareup.okhttp3:okhttp:jar:4.9.3:compile
[INFO] |           +- com.squareup.okio:okio:jar:2.8.0:compile
[INFO] |           |  \- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.6.21:compile
[INFO] |           \- org.jetbrains.kotlin:kotlin-stdlib:jar:1.6.21:compile
[INFO] |              \- org.jetbrains:annotations:jar:13.0:compile
[INFO] +- cn.ijiami.manager:ijiami-manager-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.base:ijiami-base-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.base:ijiami-base-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter-validation:jar:2.6.15:compile
[INFO] |     +- org.apache.tomcat.embed:tomcat-embed-el:jar:9.0.74:compile
[INFO] |     \- org.hibernate.validator:hibernate-validator:jar:6.2.5.Final:compile
[INFO] |        +- jakarta.validation:jakarta.validation-api:jar:2.0.2:compile
[INFO] |        +- org.jboss.logging:jboss-logging:jar:3.4.3.Final:compile
[INFO] |        \- com.fasterxml:classmate:jar:1.5.1:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-i18n:jar:2.6.8-SNAPSHOT:compile
[INFO] |  \- org.springframework:spring-webmvc:jar:5.3.39:compile
[INFO] |     +- org.springframework:spring-aop:jar:5.3.39:compile
[INFO] |     \- org.springframework:spring-expression:jar:5.3.39:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-file:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:2.6.15:compile
[INFO] |  |  \- org.springframework.boot:spring-boot:jar:2.6.15:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-common:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.14.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-databind:jar:2.14.2:compile
[INFO] |  |     \- com.fasterxml.jackson.core:jackson-core:jar:2.14.2:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-file-fdfs:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  \- com.github.tobato:fastdfs-client:jar:1.27.2:compile
[INFO] |  |     +- org.slf4j:jcl-over-slf4j:jar:1.7.36:compile
[INFO] |  |     +- ch.qos.logback:logback-classic:jar:1.2.13:compile
[INFO] |  |     |  \- ch.qos.logback:logback-core:jar:1.2.13:compile
[INFO] |  |     +- org.apache.commons:commons-pool2:jar:2.11.1:compile
[INFO] |  |     \- net.coobird:thumbnailator:jar:0.4.8:compile
[INFO] |  \- commons-codec:commons-codec:jar:1.15:compile
[INFO] +- cn.ijiami.organ:ijiami-organ-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.organ:ijiami-organ-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  |  \- cn.ijiami.framework:ijiami-framework-mybatis:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |     +- com.mysql:mysql-connector-j:jar:8.4.0:compile
[INFO] |  |     +- org.mybatis.spring.boot:mybatis-spring-boot-starter:jar:2.2.2:compile
[INFO] |  |     |  +- org.springframework.boot:spring-boot-starter-***********************
[INFO] |  |     |  |  +- com.zaxxer:HikariCP:jar:4.0.3:compile
[INFO] |  |     |  |  \- org.springframework:spring-***********************
[INFO] |  |     |  +- org.mybatis.spring.boot:mybatis-spring-boot-autoconfigure:jar:2.2.2:compile
[INFO] |  |     |  +- org.mybatis:mybatis:jar:3.5.9:compile
[INFO] |  |     |  \- org.mybatis:mybatis-spring:jar:2.0.7:compile
[INFO] |  |     +- tk.mybatis:mapper-spring-boot-starter:jar:4.2.2:compile
[INFO] |  |     |  +- tk.mybatis:mapper-core:jar:4.2.2:compile
[INFO] |  |     |  |  \- javax.persistence:javax.persistence-api:jar:2.2:compile
[INFO] |  |     |  +- tk.mybatis:mapper-base:jar:4.2.2:compile
[INFO] |  |     |  +- tk.mybatis:mapper-weekend:jar:4.2.2:compile
[INFO] |  |     |  +- tk.mybatis:mapper-spring:jar:4.2.2:compile
[INFO] |  |     |  +- tk.mybatis:mapper-extra:jar:4.2.2:compile
[INFO] |  |     |  \- tk.mybatis:mapper-spring-boot-autoconfigure:jar:4.2.2:compile
[INFO] |  |     +- com.baomidou:mybatis-plus-boot-starter:jar:3.5.3.1:compile
[INFO] |  |     |  \- com.baomidou:mybatis-plus:jar:3.5.3.1:compile
[INFO] |  |     |     \- com.baomidou:mybatis-plus-extension:jar:3.5.3.1:compile
[INFO] |  |     |        \- com.baomidou:mybatis-plus-core:jar:3.5.3.1:compile
[INFO] |  |     |           \- com.baomidou:mybatis-plus-annotation:jar:3.5.3.1:compile
[INFO] |  |     \- com.alibaba:druid:jar:1.2.23:compile
[INFO] |  \- cn.ijiami.base:ijiami-base-common:jar:2.6.11-SNAPSHOT:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-security:jar:2.6.15:compile
[INFO] |     |  +- org.springframework.security:spring-security-config:jar:5.8.16:compile
[INFO] |     |  \- org.springframework.security:spring-security-web:jar:5.8.16:compile
[INFO] |     \- org.apache.commons:commons-jexl:jar:2.1.1:compile
[INFO] +- cn.ijiami.organ:ijiami-organ-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] +- org.springframework.data:spring-data-commons:jar:2.6.10:compile
[INFO] |  +- org.springframework:spring-core:jar:5.3.39:compile
[INFO] |  |  \- org.springframework:spring-jcl:jar:5.3.39:compile
[INFO] |  \- org.springframework:spring-beans:jar:5.3.39:compile
[INFO] +- cn.ijiami.base:ijiami-base-email:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- commons-fileupload:commons-fileupload:jar:1.5:compile
[INFO] |  \- cn.ijiami.framework:ijiami-framework-email:jar:2.6.8-SNAPSHOT:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-mail:jar:2.6.15:compile
[INFO] |     |  +- org.springframework:spring-context-support:jar:5.3.39:compile
[INFO] |     |  \- com.sun.mail:jakarta.mail:jar:1.6.7:compile
[INFO] |     |     \- com.sun.activation:jakarta.activation:jar:1.2.2:compile
[INFO] |     \- org.springframework.boot:spring-boot-starter-freemarker:jar:2.6.15:compile
[INFO] |        \- org.freemarker:freemarker:jar:2.3.32:compile
[INFO] +- com.github.oshi:oshi-core:jar:6.4.0:compile
[INFO] +- net.java.dev.jna:jna:jar:5.12.0:compile
[INFO] +- net.java.dev.jna:jna-platform:jar:5.12.0:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-utils:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- cn.hutool:hutool-all:jar:5.8.36:compile
[INFO] |  +- javax.servlet:javax.servlet-api:jar:4.0.1:compile
[INFO] |  +- commons-io:commons-io:jar:2.11.0:compile
[INFO] |  \- com.googlecode.aviator:aviator:jar:5.4.1:compile
[INFO] +- net.sf.json-lib:json-lib:jar:jdk15:2.4:compile (optional) 
[INFO] |  +- commons-beanutils:commons-beanutils:jar:1.8.0:compile
[INFO] |  +- commons-collections:commons-collections:jar:3.2.1:compile (optional) 
[INFO] |  +- commons-lang:commons-lang:jar:2.5:compile
[INFO] |  +- commons-logging:commons-logging:jar:1.1.1:compile
[INFO] |  \- net.sf.ezmorph:ezmorph:jar:1.0.6:compile (optional) 
[INFO] +- org.projectlombok:lombok:jar:1.18.24:compile
[INFO] +- com.alibaba:fastjson:jar:1.2.83:compile
[INFO] +- org.apache.poi:poi:jar:4.1.2:compile
[INFO] |  +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] |  \- com.zaxxer:SparseBitSet:jar:1.2:compile
[INFO] +- org.apache.commons:commons-collections4:jar:4.1:compile
[INFO] +- org.apache.poi:poi-ooxml:jar:4.1.2:compile
[INFO] |  +- org.apache.poi:poi-ooxml-schemas:jar:4.1.2:compile
[INFO] |  +- org.apache.commons:commons-compress:jar:1.19:compile
[INFO] |  \- com.github.virtuald:curvesapi:jar:1.06:compile
[INFO] +- org.apache.xmlbeans:xmlbeans:jar:3.1.0:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-mongodb:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-data-mongodb:jar:2.6.15:compile
[INFO] |  |  \- org.mongodb:mongodb-driver-sync:jar:4.4.2:compile
[INFO] |  |     \- org.mongodb:bson:jar:4.4.2:compile
[INFO] |  +- org.springframework.data:spring-data-mongodb:jar:3.3.10:compile
[INFO] |  |  +- org.springframework:spring-tx:jar:5.3.39:compile
[INFO] |  |  \- org.mongodb:mongodb-driver-core:jar:4.4.2:compile
[INFO] |  \- com.github.pagehelper:pagehelper:jar:5.3.3:compile
[INFO] |     \- com.github.jsqlparser:jsqlparser:jar:4.5:compile
[INFO] +- com.google.guava:guava:jar:23.0:compile
[INFO] |  +- com.google.code.findbugs:jsr305:jar:1.3.9:compile
[INFO] |  +- com.google.errorprone:error_prone_annotations:jar:2.0.18:compile
[INFO] |  +- com.google.j2objc:j2objc-annotations:jar:1.1:compile
[INFO] |  \- org.codehaus.mojo:animal-sniffer-annotations:jar:1.14:compile
[INFO] +- org.springframework.cloud:spring-cloud-starter-openfeign:jar:2.1.3.RELEASE:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-starter:jar:3.1.4:compile
[INFO] |  |  +- org.springframework.cloud:spring-cloud-context:jar:3.1.4:compile
[INFO] |  |  \- org.springframework.security:spring-security-rsa:jar:1.0.11.RELEASE:compile
[INFO] |  |     \- org.bouncycastle:bcpkix-jdk15on:jar:1.69:compile
[INFO] |  |        +- org.bouncycastle:bcprov-jdk15on:jar:1.69:compile
[INFO] |  |        \- org.bouncycastle:bcutil-jdk15on:jar:1.69:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-openfeign-core:jar:3.1.4:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-aop:jar:2.6.15:compile
[INFO] |  |  \- io.github.openfeign.form:feign-form-spring:jar:3.8.0:compile
[INFO] |  |     \- io.github.openfeign.form:feign-form:jar:3.8.0:compile
[INFO] |  +- org.springframework:spring-web:jar:5.3.39:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-commons:jar:3.1.4:compile
[INFO] |  |  \- org.springframework.security:spring-security-crypto:jar:5.8.16:compile
[INFO] |  +- io.github.openfeign:feign-core:jar:11.8:compile
[INFO] |  +- io.github.openfeign:feign-slf4j:jar:11.8:compile
[INFO] |  \- io.github.openfeign:feign-hystrix:jar:11.8:compile
[INFO] |     \- com.netflix.hystrix:hystrix-core:jar:1.5.18:compile
[INFO] |        +- com.netflix.archaius:archaius-core:jar:0.4.1:compile
[INFO] |        |  \- commons-configuration:commons-configuration:jar:1.8:compile
[INFO] |        +- io.reactivex:rxjava:jar:1.3.8:compile
[INFO] |        \- org.hdrhistogram:HdrHistogram:jar:2.1.9:compile
[INFO] +- cn.ijiami.authentication:authentication-rest:jar:2.6.10-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.authentication:authentication-service-impl:jar:2.6.10-SNAPSHOT:compile
[INFO] |  |  +- cn.ijiami.authentication:authentication-service-api:jar:2.6.10-SNAPSHOT:compile
[INFO] |  |  +- org.apache.httpcomponents:httpclient:jar:4.5.14:compile
[INFO] |  |  +- org.apache.httpcomponents:httpcore:jar:4.4.16:compile
[INFO] |  |  \- commons-httpclient:commons-httpclient:jar:3.1:compile
[INFO] |  +- cn.ijiami.manager:ijiami-manager-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.manager:ijiami-manager-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  |  \- cn.ijiami.framework:ijiami-framework-redis:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |     +- org.springframework.boot:spring-boot-starter-data-redis:jar:2.6.15:compile
[INFO] |  |     |  +- org.springframework.data:spring-data-redis:jar:2.6.10:compile
[INFO] |  |     |  |  +- org.springframework.data:spring-data-keyvalue:jar:2.6.10:compile
[INFO] |  |     |  |  \- org.springframework:spring-oxm:jar:5.3.39:compile
[INFO] |  |     |  \- io.lettuce:lettuce-core:jar:6.1.10.RELEASE:compile
[INFO] |  |     |     +- io.netty:netty-common:jar:4.1.92.Final:compile
[INFO] |  |     |     +- io.netty:netty-handler:jar:4.1.92.Final:compile
[INFO] |  |     |     |  +- io.netty:netty-resolver:jar:4.1.92.Final:compile
[INFO] |  |     |     |  +- io.netty:netty-buffer:jar:4.1.92.Final:compile
[INFO] |  |     |     |  +- io.netty:netty-transport-native-unix-common:jar:4.1.92.Final:compile
[INFO] |  |     |     |  \- io.netty:netty-codec:jar:4.1.92.Final:compile
[INFO] |  |     |     +- io.netty:netty-transport:jar:4.1.92.Final:compile
[INFO] |  |     |     \- io.projectreactor:reactor-core:jar:3.4.29:compile
[INFO] |  |     |        \- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |     \- org.springframework.boot:spring-boot-starter-cache:jar:2.6.15:compile
[INFO] |  +- cn.ijiami.manager:ijiami-manager-security:jar:2.6.11-SNAPSHOT:compile
[INFO] |  |  +- org.springframework.security:spring-security-jwt:jar:1.1.1.RELEASE:compile
[INFO] |  |  +- org.springframework.security.oauth:spring-security-oauth2:jar:2.3.8.RELEASE:compile
[INFO] |  |  |  \- org.springframework.security:spring-security-core:jar:5.8.16:compile
[INFO] |  |  +- org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13-atlassian-1:compile
[INFO] |  |  |  \- org.codehaus.jackson:jackson-core-asl:jar:1.9.13-atlassian-1:compile
[INFO] |  |  +- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:jar:2.14.2:compile
[INFO] |  |  |  +- jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.3:compile
[INFO] |  |  |  \- jakarta.activation:jakarta.activation-api:jar:1.2.2:compile
[INFO] |  |  +- org.apache.commons:commons-text:jar:1.10.0:compile
[INFO] |  |  +- cn.ijiami.framework:ijiami-framework-ldap:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  |  \- org.springframework.boot:spring-boot-starter-data-ldap:jar:2.6.15:compile
[INFO] |  |  |     \- org.springframework.data:spring-data-ldap:jar:2.6.10:compile
[INFO] |  |  |        \- org.springframework.ldap:spring-ldap-core:jar:2.3.8.RELEASE:compile
[INFO] |  |  \- cn.ijiami.framework:ijiami-framework-websocket:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- io.jsonwebtoken:jjwt:jar:0.9.0:compile
[INFO] |  +- org.aspectj:aspectjrt:jar:1.9.7:compile
[INFO] |  \- org.aspectj:aspectjweaver:jar:1.9.7:compile
[INFO] +- mysql:mysql-connector-java:jar:8.0.16:compile
[INFO] +- com.dm:DmJdbcDriver18:jar:1.8:compile
[INFO] +- cn.ijiami.detection:privacy-android-server-client:jar:1.0-SNAPSHOT:compile
[INFO] |  \- cn.ijiami.detection:privacy-server-client-base:jar:1.0-SNAPSHOT:compile
[INFO] +- cn.ijiami.detection:privacy-ios-server-client:jar:1.0-SNAPSHOT:compile
[INFO] +- org.slf4j:slf4j-api:jar:1.7.36:compile
[INFO] \- org.apache.commons:commons-lang3:jar:3.12.0:compile
[INFO] 
[INFO] -------------< cn.ijiami.detection:detection-service-impl >-------------
[INFO] Building detection-service-impl 1.0-SNAPSHOT                       [4/6]
[INFO]   from detection-service-impl\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[WARNING] The POM for mysql:mysql-connector-java:jar:8.4.0 is missing, no dependency information available
[INFO] 
[INFO] --- dependency:3.2.0:tree (default-cli) @ detection-service-impl ---
[INFO] cn.ijiami.detection:detection-service-impl:jar:1.0-SNAPSHOT
[INFO] +- org.csource:fastdfs-client-java:jar:1.27-SNAPSHOT:compile
[INFO] +- commons-io:commons-io:jar:2.5:compile
[INFO] +- com.github.tobato:fastdfs-client:jar:1.26.6:compile
[INFO] |  +- org.slf4j:jcl-over-slf4j:jar:1.7.36:compile
[INFO] |  +- ch.qos.logback:logback-classic:jar:1.2.13:compile
[INFO] |  |  \- ch.qos.logback:logback-core:jar:1.2.13:compile
[INFO] |  +- commons-beanutils:commons-beanutils:jar:1.9.3:compile
[INFO] |  +- org.apache.commons:commons-pool2:jar:2.11.1:compile
[INFO] |  +- org.springframework:spring-core:jar:5.3.39:compile
[INFO] |  |  \- org.springframework:spring-jcl:jar:5.3.39:compile
[INFO] |  +- org.hibernate.validator:hibernate-validator:jar:6.2.5.Final:compile
[INFO] |  |  +- jakarta.validation:jakarta.validation-api:jar:2.0.2:compile
[INFO] |  |  +- org.jboss.logging:jboss-logging:jar:3.4.3.Final:compile
[INFO] |  |  \- com.fasterxml:classmate:jar:1.5.1:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:2.6.15:compile
[INFO] |  |  \- org.springframework.boot:spring-boot:jar:2.6.15:compile
[INFO] |  +- org.springframework:spring-context:jar:5.3.39:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:5.3.39:compile
[INFO] |  |  \- org.springframework:spring-expression:jar:5.3.39:compile
[INFO] |  \- net.coobird:thumbnailator:jar:0.4.8:compile
[INFO] +- cn.ijiami.message:ijiami-message-core:jar:2.6.8-SNAPSHOT:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter-websocket:jar:2.6.15:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-web:jar:2.6.15:compile
[INFO] |     |  +- org.springframework.boot:spring-boot-starter-json:jar:2.6.15:compile
[INFO] |     |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.14.2:compile
[INFO] |     |  |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.14.2:compile
[INFO] |     |  |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.14.2:compile
[INFO] |     |  \- org.springframework.boot:spring-boot-starter-tomcat:jar:2.6.15:compile
[INFO] |     |     +- org.apache.tomcat.embed:tomcat-embed-core:jar:9.0.74:compile
[INFO] |     |     \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:9.0.74:compile
[INFO] |     \- org.springframework:spring-websocket:jar:5.3.39:compile
[INFO] +- cn.ijiami.base:ijiami-base-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.base:ijiami-base-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  \- com.alibaba.fastjson2:fastjson2:jar:2.0.38:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-utils:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- cn.hutool:hutool-all:jar:5.8.36:compile
[INFO] |  +- javax.servlet:javax.servlet-api:jar:4.0.1:compile
[INFO] |  +- commons-codec:commons-codec:jar:1.15:compile
[INFO] |  +- com.googlecode.aviator:aviator:jar:5.4.1:compile
[INFO] |  \- cn.ijiami.framework:ijiami-framework-common:jar:2.6.8-SNAPSHOT:compile
[INFO] |     \- io.swagger:swagger-annotations:jar:1.5.22:compile
[INFO] +- cn.ijiami.message:ijiami-message-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-mybatis:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  +- com.mysql:mysql-connector-j:jar:8.4.0:compile
[INFO] |  |  +- org.mybatis.spring.boot:mybatis-spring-boot-starter:jar:2.2.2:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-***********************
[INFO] |  |  |  |  +- com.zaxxer:HikariCP:jar:4.0.3:compile
[INFO] |  |  |  |  \- org.springframework:spring-***********************
[INFO] |  |  |  +- org.mybatis.spring.boot:mybatis-spring-boot-autoconfigure:jar:2.2.2:compile
[INFO] |  |  |  +- org.mybatis:mybatis:jar:3.5.9:compile
[INFO] |  |  |  \- org.mybatis:mybatis-spring:jar:2.0.7:compile
[INFO] |  |  +- tk.mybatis:mapper-spring-boot-starter:jar:4.2.2:compile
[INFO] |  |  |  +- tk.mybatis:mapper-core:jar:4.2.2:compile
[INFO] |  |  |  |  \- javax.persistence:javax.persistence-api:jar:2.2:compile
[INFO] |  |  |  +- tk.mybatis:mapper-base:jar:4.2.2:compile
[INFO] |  |  |  +- tk.mybatis:mapper-weekend:jar:4.2.2:compile
[INFO] |  |  |  +- tk.mybatis:mapper-spring:jar:4.2.2:compile
[INFO] |  |  |  +- tk.mybatis:mapper-extra:jar:4.2.2:compile
[INFO] |  |  |  \- tk.mybatis:mapper-spring-boot-autoconfigure:jar:4.2.2:compile
[INFO] |  |  +- com.baomidou:mybatis-plus-boot-starter:jar:3.5.3.1:compile
[INFO] |  |  |  \- com.baomidou:mybatis-plus:jar:3.5.3.1:compile
[INFO] |  |  |     \- com.baomidou:mybatis-plus-extension:jar:3.5.3.1:compile
[INFO] |  |  |        \- com.baomidou:mybatis-plus-core:jar:3.5.3.1:compile
[INFO] |  |  |           \- com.baomidou:mybatis-plus-annotation:jar:3.5.3.1:compile
[INFO] |  |  \- com.alibaba:druid:jar:1.2.23:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-validation:jar:2.6.15:compile
[INFO] |  |  \- org.apache.tomcat.embed:tomcat-embed-el:jar:9.0.74:compile
[INFO] |  \- cn.ijiami.framework:ijiami-framework-swagger:jar:2.6.8-SNAPSHOT:compile
[INFO] |     \- com.github.xiaoymin:knife4j-spring-boot-starter:jar:3.0.3:compile
[INFO] |        +- com.github.xiaoymin:knife4j-spring-boot-autoconfigure:jar:3.0.3:compile
[INFO] |        |  +- com.github.xiaoymin:knife4j-spring:jar:3.0.3:compile
[INFO] |        |  |  +- com.github.xiaoymin:knife4j-annotations:jar:3.0.3:compile
[INFO] |        |  |  |  \- io.swagger.core.v3:swagger-annotations:jar:2.1.2:compile
[INFO] |        |  |  +- com.github.xiaoymin:knife4j-core:jar:3.0.3:compile
[INFO] |        |  |  +- org.javassist:javassist:jar:3.25.0-GA:compile
[INFO] |        |  |  +- io.springfox:springfox-swagger2:jar:3.0.0:compile
[INFO] |        |  |  |  +- io.springfox:springfox-spi:jar:3.0.0:compile
[INFO] |        |  |  |  +- io.springfox:springfox-schema:jar:3.0.0:compile
[INFO] |        |  |  |  +- io.springfox:springfox-swagger-common:jar:3.0.0:compile
[INFO] |        |  |  |  +- io.springfox:springfox-spring-web:jar:3.0.0:compile
[INFO] |        |  |  |  |  \- io.github.classgraph:classgraph:jar:4.8.83:compile
[INFO] |        |  |  |  +- io.springfox:springfox-spring-webflux:jar:3.0.0:compile
[INFO] |        |  |  |  \- org.mapstruct:mapstruct:jar:1.3.1.Final:runtime
[INFO] |        |  |  +- io.springfox:springfox-spring-webmvc:jar:3.0.0:compile
[INFO] |        |  |  |  \- io.springfox:springfox-core:jar:3.0.0:compile
[INFO] |        |  |  +- io.springfox:springfox-oas:jar:3.0.0:compile
[INFO] |        |  |  |  \- io.swagger.core.v3:swagger-models:jar:2.1.2:compile
[INFO] |        |  |  +- io.springfox:springfox-bean-validators:jar:3.0.0:compile
[INFO] |        |  |  +- io.swagger:swagger-models:jar:1.5.22:compile
[INFO] |        |  |  \- io.swagger:swagger-core:jar:1.5.22:compile
[INFO] |        |  |     \- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.14.2:compile
[INFO] |        |  \- io.springfox:springfox-boot-starter:jar:3.0.0:compile
[INFO] |        |     +- io.springfox:springfox-data-rest:jar:3.0.0:compile
[INFO] |        |     +- org.springframework.plugin:spring-plugin-core:jar:2.0.0.RELEASE:compile
[INFO] |        |     \- org.springframework.plugin:spring-plugin-metadata:jar:2.0.0.RELEASE:compile
[INFO] |        \- com.github.xiaoymin:knife4j-spring-ui:jar:3.0.3:compile
[INFO] +- cn.ijiami.message:ijiami-message-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  \- cn.ijiami.base:ijiami-base-common:jar:2.6.11-SNAPSHOT:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-security:jar:2.6.15:compile
[INFO] |     |  +- org.springframework.security:spring-security-config:jar:5.8.16:compile
[INFO] |     |  \- org.springframework.security:spring-security-web:jar:5.8.16:compile
[INFO] |     \- org.apache.commons:commons-jexl:jar:2.1.1:compile
[INFO] +- org.springframework.kafka:spring-kafka:jar:2.9.13:compile
[INFO] |  +- org.springframework:spring-messaging:jar:5.3.39:compile
[INFO] |  +- org.springframework:spring-tx:jar:5.3.39:compile
[INFO] |  +- org.springframework.retry:spring-retry:jar:1.3.4:compile
[INFO] |  +- org.apache.kafka:kafka-clients:jar:3.0.2:compile
[INFO] |  |  +- com.github.luben:zstd-jni:jar:1.5.0-2:runtime
[INFO] |  |  +- org.lz4:lz4-java:jar:1.7.1:runtime
[INFO] |  |  \- org.xerial.snappy:snappy-java:jar:1.1.8.1:runtime
[INFO] |  \- com.google.code.findbugs:jsr305:jar:3.0.2:runtime
[INFO] +- cn.ijiami.framework:ijiami-framework-websocket:jar:2.6.8-SNAPSHOT:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-redis:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:2.6.15:compile
[INFO] |  |  +- org.springframework.data:spring-data-redis:jar:2.6.10:compile
[INFO] |  |  |  +- org.springframework.data:spring-data-keyvalue:jar:2.6.10:compile
[INFO] |  |  |  \- org.springframework:spring-oxm:jar:5.3.39:compile
[INFO] |  |  \- io.lettuce:lettuce-core:jar:6.1.10.RELEASE:compile
[INFO] |  |     +- io.netty:netty-common:jar:4.1.92.Final:compile
[INFO] |  |     +- io.netty:netty-handler:jar:4.1.92.Final:compile
[INFO] |  |     |  +- io.netty:netty-resolver:jar:4.1.92.Final:compile
[INFO] |  |     |  +- io.netty:netty-buffer:jar:4.1.92.Final:compile
[INFO] |  |     |  +- io.netty:netty-transport-native-unix-common:jar:4.1.92.Final:compile
[INFO] |  |     |  \- io.netty:netty-codec:jar:4.1.92.Final:compile
[INFO] |  |     +- io.netty:netty-transport:jar:4.1.92.Final:compile
[INFO] |  |     \- io.projectreactor:reactor-core:jar:3.4.29:compile
[INFO] |  |        \- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.14.2:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter-cache:jar:2.6.15:compile
[INFO] |     \- org.springframework:spring-context-support:jar:5.3.39:compile
[INFO] +- cn.ijiami.detection:detection-service-api:jar:1.0-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-openfeign:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  \- org.springframework.cloud:spring-cloud-starter-loadbalancer:jar:3.1.4:compile
[INFO] |  |     +- org.springframework.cloud:spring-cloud-loadbalancer:jar:3.1.4:compile
[INFO] |  |     |  \- io.projectreactor.addons:reactor-extra:jar:3.4.10:compile
[INFO] |  |     \- com.stoyanr:evictor:jar:1.0.0:compile
[INFO] |  +- com.ijm.ios:RuntimeDetection:jar:0.0.2-SNAPSHOT:compile
[INFO] |  |  \- commons-logging:commons-logging-api:jar:1.1:compile
[INFO] |  +- com.ocpframework.ocp:ocp-sdk-detect-enginer:jar:1.0-SNAPSHOT:compile
[INFO] |  |  +- com.ocpframework.ocp:ocp-sdk-detect-common:jar:1.0-SNAPSHOT:compile
[INFO] |  |  \- org.eclipse.jgit:org.eclipse.jgit:jar:5.13.1.202206130422-r:compile
[INFO] |  |     \- com.googlecode.javaewah:JavaEWAH:jar:1.1.13:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:2.6.15:compile
[INFO] |  \- com.google.code.gson:gson:jar:2.8.9:compile
[INFO] +- cn.ijiami.detection:detection-core:jar:1.0-SNAPSHOT:compile
[INFO] +- cn.ijiami.report:ijiami-report-service-api:jar:2.6.10-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-web:jar:2.6.8-SNAPSHOT:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter-freemarker:jar:2.6.15:compile
[INFO] +- cn.ijiami.report:ijiami-report-service-impl:jar:2.6.10-SNAPSHOT:compile
[INFO] |  \- com.aspose:aspose.words:jar:15.8.0:compile
[INFO] +- org.apache.ant:ant:jar:1.10.5:compile
[INFO] |  \- org.apache.ant:ant-launcher:jar:1.10.5:compile
[INFO] +- org.apache.httpcomponents:httpclient:jar:4.5.14:compile
[INFO] +- org.apache.httpcomponents:httpcore:jar:4.4.16:compile
[INFO] +- org.apache.httpcomponents:httpmime:jar:4.5.14:compile
[INFO] +- com.squareup.okhttp3:okhttp:jar:4.9.3:compile
[INFO] |  +- com.squareup.okio:okio:jar:2.8.0:compile
[INFO] |  |  \- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.6.21:compile
[INFO] |  \- org.jetbrains.kotlin:kotlin-stdlib:jar:1.6.21:compile
[INFO] |     \- org.jetbrains:annotations:jar:13.0:compile
[INFO] +- org.springframework:spring-test:jar:5.3.39:compile
[INFO] +- com.maxmind.geoip2:geoip2:jar:2.12.0:compile
[INFO] |  +- com.maxmind.db:maxmind-db:jar:1.2.2:compile
[INFO] |  +- com.fasterxml.jackson.core:jackson-core:jar:2.14.2:compile
[INFO] |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.14.2:compile
[INFO] +- org.freemarker:freemarker:jar:2.3.32:compile
[INFO] +- cn.ijiami.msmp:msmp-ios-detection-tool:jar:2.1.0-SNAPSHOT:compile
[INFO] |  +- org.apache.logging.log4j:log4j-api:jar:2.18.0:compile
[INFO] |  +- org.apache.logging.log4j:log4j-core:jar:2.18.0:compile
[INFO] |  +- org.xerial:sqlite-jdbc:jar:********:compile
[INFO] |  +- org.apache.commons:commons-compress:jar:1.20:compile
[INFO] |  +- cn.ijiami.ios:XibDump:jar:1.0.0-SNAPSHOT:compile
[INFO] |  +- com.dd.plist:dd-plist:jar:1.0:compile
[INFO] |  +- net.lingala.zip4j:zip4j:jar:1.3.2:compile
[INFO] |  \- com.jcraft:jzlib:jar:1.1.3:compile
[INFO] +- com.googlecode.plist:dd-plist:jar:1.16:compile
[INFO] +- cn.ijiami:z-csp-device:jar:1.0.0:compile
[INFO] |  \- joda-time:joda-time:jar:2.10.6:compile
[INFO] +- dom4j:dom4j:jar:1.6.1:compile
[INFO] |  \- xml-apis:xml-apis:jar:1.0.b2:compile
[INFO] +- com.jcraft:jsch:jar:0.1.54:compile
[INFO] +- net.dongliu:apk-parser:jar:2.6.10:compile
[INFO] +- com.google.zxing:core:jar:3.3.0:compile
[INFO] +- com.google.zxing:javase:jar:3.3.0:compile
[INFO] |  +- com.beust:jcommander:jar:1.48:compile
[INFO] |  \- com.github.jai-imageio:jai-imageio-core:jar:1.3.1:compile
[INFO] +- org.java-websocket:Java-WebSocket:jar:1.5.2:compile
[INFO] +- com.qcloud:cos_api:jar:5.6.8:compile
[INFO] |  \- org.bouncycastle:bcprov-jdk15on:jar:1.59:compile
[INFO] +- org.jsoup:jsoup:jar:1.11.3:compile
[INFO] +- com.github.houbb:opencc4j:jar:1.6.1:compile
[INFO] |  +- com.github.houbb:heaven:jar:0.1.129:compile
[INFO] |  |  \- org.apiguardian:apiguardian-api:jar:1.0.0:compile
[INFO] |  \- com.github.houbb:nlp-common:jar:0.0.4:compile
[INFO] +- org.tensorflow:tensorflow:jar:1.15.0:compile
[INFO] |  +- org.tensorflow:libtensorflow:jar:1.15.0:compile
[INFO] |  \- org.tensorflow:libtensorflow_jni:jar:1.15.0:compile
[INFO] +- com.itextpdf:itextpdf:jar:5.5.13:compile
[INFO] +- org.lionsoul:ip2region:jar:2.6.5:compile
[INFO] +- com.android.apksig:apksigner:jar:1.1:compile
[INFO] +- commons-httpclient:commons-httpclient:jar:3.1:compile
[INFO] |  \- commons-logging:commons-logging:jar:1.0.4:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-apk:jar:2.6.8-SNAPSHOT:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-ipa:jar:2.6.8-SNAPSHOT:compile
[INFO] |  \- hashfile:hashfile:jar:1.0:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-email:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-mail:jar:2.6.15:compile
[INFO] |  |  \- com.sun.mail:jakarta.mail:jar:1.6.7:compile
[INFO] |  |     \- com.sun.activation:jakarta.activation:jar:1.2.2:compile
[INFO] |  \- org.springframework:spring-webmvc:jar:5.3.39:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-file:jar:2.6.8-SNAPSHOT:compile
[INFO] |  \- cn.ijiami.framework:ijiami-framework-file-fdfs:jar:2.6.8-SNAPSHOT:compile
[INFO] +- org.springframework.boot:spring-boot-starter-test:jar:2.6.15:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter:jar:2.6.15:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:2.6.15:compile
[INFO] |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.18.0:compile
[INFO] |  |  |  \- org.slf4j:jul-to-slf4j:jar:1.7.36:compile
[INFO] |  |  +- jakarta.annotation:jakarta.annotation-api:jar:1.3.5:compile
[INFO] |  |  \- org.yaml:snakeyaml:jar:1.33:compile
[INFO] |  +- org.springframework.boot:spring-boot-test:jar:2.6.15:compile
[INFO] |  +- org.springframework.boot:spring-boot-test-autoconfigure:jar:2.6.15:compile
[INFO] |  +- com.jayway.jsonpath:json-path:jar:2.6.0:compile
[INFO] |  |  \- net.minidev:json-smart:jar:2.4.10:compile
[INFO] |  |     \- net.minidev:accessors-smart:jar:2.4.9:compile
[INFO] |  |        \- org.ow2.asm:asm:jar:9.3:compile
[INFO] |  +- jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.3:compile
[INFO] |  |  \- jakarta.activation:jakarta.activation-api:jar:1.2.2:compile
[INFO] |  +- org.assertj:assertj-core:jar:3.21.0:compile
[INFO] |  +- org.hamcrest:hamcrest:jar:2.2:compile
[INFO] |  +- org.junit.jupiter:junit-jupiter:jar:5.8.2:compile
[INFO] |  |  +- org.junit.jupiter:junit-jupiter-api:jar:5.8.2:compile
[INFO] |  |  |  +- org.opentest4j:opentest4j:jar:1.2.0:compile
[INFO] |  |  |  \- org.junit.platform:junit-platform-commons:jar:1.8.2:compile
[INFO] |  |  +- org.junit.jupiter:junit-jupiter-params:jar:5.8.2:compile
[INFO] |  |  \- org.junit.jupiter:junit-jupiter-engine:jar:5.8.2:runtime
[INFO] |  |     \- org.junit.platform:junit-platform-engine:jar:1.8.2:runtime
[INFO] |  +- org.mockito:mockito-core:jar:4.0.0:compile
[INFO] |  |  +- net.bytebuddy:byte-buddy:jar:1.11.22:compile
[INFO] |  |  +- net.bytebuddy:byte-buddy-agent:jar:1.11.22:compile
[INFO] |  |  \- org.objenesis:objenesis:jar:3.2:runtime
[INFO] |  +- org.mockito:mockito-junit-jupiter:jar:4.0.0:compile
[INFO] |  +- org.skyscreamer:jsonassert:jar:1.5.1:compile
[INFO] |  |  \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:compile
[INFO] |  \- org.xmlunit:xmlunit-core:jar:2.8.4:compile
[INFO] +- commons-net:commons-net:jar:3.5:compile
[INFO] +- com.google.guava:guava:jar:20.0:compile
[INFO] +- org.apktool:apktool-lib:jar:2.6.0:compile
[INFO] |  +- org.apktool:brut.j.dir:jar:2.6.0:compile
[INFO] |  +- org.apktool:brut.j.util:jar:2.6.0:compile
[INFO] |  +- org.apktool:brut.j.common:jar:2.6.0:compile
[INFO] |  +- org.smali:baksmali:jar:2.5.2:runtime
[INFO] |  |  +- org.smali:dexlib2:jar:2.5.2:runtime
[INFO] |  |  \- org.smali:util:jar:2.5.2:runtime
[INFO] |  +- org.smali:smali:jar:2.5.2:runtime
[INFO] |  |  +- org.antlr:antlr:jar:3.5.2:runtime
[INFO] |  |  |  \- org.antlr:ST4:jar:4.0.8:runtime
[INFO] |  |  +- org.antlr:antlr-runtime:jar:3.5.2:runtime
[INFO] |  |  \- org.antlr:stringtemplate:jar:3.2.1:runtime
[INFO] |  |     \- antlr:antlr:jar:2.7.7:runtime
[INFO] |  +- org.yaml:snakeyaml:jar:android:1.29:runtime
[INFO] |  \- xpp3:xpp3:jar:1.1.4c:runtime
[INFO] +- cn.ijiami.detection:android-detection-decompiler:jar:1.7-SNAPSHOT:compile
[INFO] +- cn.ijiami.detection:detection-common-utils:jar:1.7-SNAPSHOT:compile
[INFO] +- android.content.res.APKParser:APKParser-base:jar:2.0.1:compile
[INFO] +- cn.ijiami.ai:ijiami-ai-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- javax.validation:validation-api:jar:2.0.1.Final:compile
[INFO] |  \- cn.ijiami.ai:ijiami-ai-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |     +- cn.ijiami.ai:ijiami-ai-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |     \- cn.ijiami.framework:ijiami-framework-ai:jar:2.6.8-SNAPSHOT:compile
[INFO] +- cn.ijiami.manager:ijiami-manager-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-i18n:jar:2.6.8-SNAPSHOT:compile
[INFO] +- cn.ijiami.organ:ijiami-organ-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  \- cn.ijiami.organ:ijiami-organ-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] +- cn.ijiami.organ:ijiami-organ-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] +- org.springframework.data:spring-data-commons:jar:2.6.10:compile
[INFO] |  \- org.springframework:spring-beans:jar:5.3.39:compile
[INFO] +- cn.ijiami.base:ijiami-base-email:jar:2.6.11-SNAPSHOT:compile
[INFO] |  \- commons-fileupload:commons-fileupload:jar:1.5:compile
[INFO] +- com.github.oshi:oshi-core:jar:6.4.0:compile
[INFO] +- net.java.dev.jna:jna:jar:5.12.0:compile
[INFO] +- net.java.dev.jna:jna-platform:jar:5.12.0:compile
[INFO] +- net.sf.json-lib:json-lib:jar:jdk15:2.4:compile (optional) 
[INFO] |  +- commons-collections:commons-collections:jar:3.2.1:compile
[INFO] |  +- commons-lang:commons-lang:jar:2.5:compile
[INFO] |  \- net.sf.ezmorph:ezmorph:jar:1.0.6:compile (optional) 
[INFO] +- org.projectlombok:lombok:jar:1.18.24:compile
[INFO] +- com.alibaba:fastjson:jar:1.2.83:compile
[INFO] +- org.apache.poi:poi:jar:4.1.2:compile
[INFO] |  +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] |  \- com.zaxxer:SparseBitSet:jar:1.2:compile
[INFO] +- org.apache.commons:commons-collections4:jar:4.1:compile
[INFO] +- org.apache.poi:poi-ooxml:jar:4.1.2:compile
[INFO] |  +- org.apache.poi:poi-ooxml-schemas:jar:4.1.2:compile
[INFO] |  \- com.github.virtuald:curvesapi:jar:1.06:compile
[INFO] +- org.apache.xmlbeans:xmlbeans:jar:3.1.0:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-mongodb:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-data-mongodb:jar:2.6.15:compile
[INFO] |  |  \- org.mongodb:mongodb-driver-sync:jar:4.4.2:compile
[INFO] |  |     \- org.mongodb:bson:jar:4.4.2:compile
[INFO] |  +- org.springframework.data:spring-data-mongodb:jar:3.3.10:compile
[INFO] |  |  \- org.mongodb:mongodb-driver-core:jar:4.4.2:compile
[INFO] |  \- com.github.pagehelper:pagehelper:jar:5.3.3:compile
[INFO] |     \- com.github.jsqlparser:jsqlparser:jar:4.5:compile
[INFO] +- org.springframework.cloud:spring-cloud-starter-openfeign:jar:2.1.3.RELEASE:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-starter:jar:3.1.4:compile
[INFO] |  |  +- org.springframework.cloud:spring-cloud-context:jar:3.1.4:compile
[INFO] |  |  \- org.springframework.security:spring-security-rsa:jar:1.0.11.RELEASE:compile
[INFO] |  |     \- org.bouncycastle:bcpkix-jdk15on:jar:1.69:compile
[INFO] |  |        \- org.bouncycastle:bcutil-jdk15on:jar:1.69:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-openfeign-core:jar:3.1.4:compile
[INFO] |  |  \- io.github.openfeign.form:feign-form-spring:jar:3.8.0:compile
[INFO] |  |     \- io.github.openfeign.form:feign-form:jar:3.8.0:compile
[INFO] |  +- org.springframework:spring-web:jar:5.3.39:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-commons:jar:3.1.4:compile
[INFO] |  |  \- org.springframework.security:spring-security-crypto:jar:5.8.16:compile
[INFO] |  +- io.github.openfeign:feign-core:jar:11.8:compile
[INFO] |  +- io.github.openfeign:feign-slf4j:jar:11.8:compile
[INFO] |  \- io.github.openfeign:feign-hystrix:jar:11.8:compile
[INFO] |     \- com.netflix.hystrix:hystrix-core:jar:1.5.18:compile
[INFO] |        +- com.netflix.archaius:archaius-core:jar:0.4.1:compile
[INFO] |        |  \- commons-configuration:commons-configuration:jar:1.8:compile
[INFO] |        +- io.reactivex:rxjava:jar:1.3.8:compile
[INFO] |        \- org.hdrhistogram:HdrHistogram:jar:2.1.9:compile
[INFO] +- cn.ijiami.authentication:authentication-rest:jar:2.6.10-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.authentication:authentication-service-impl:jar:2.6.10-SNAPSHOT:compile
[INFO] |  |  \- cn.ijiami.authentication:authentication-service-api:jar:2.6.10-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.manager:ijiami-manager-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.manager:ijiami-manager-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.manager:ijiami-manager-security:jar:2.6.11-SNAPSHOT:compile
[INFO] |  |  +- org.springframework.security:spring-security-jwt:jar:1.1.1.RELEASE:compile
[INFO] |  |  +- org.springframework.security.oauth:spring-security-oauth2:jar:2.3.8.RELEASE:compile
[INFO] |  |  |  \- org.springframework.security:spring-security-core:jar:5.8.16:compile
[INFO] |  |  +- org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13-atlassian-1:compile
[INFO] |  |  |  \- org.codehaus.jackson:jackson-core-asl:jar:1.9.13-atlassian-1:compile
[INFO] |  |  +- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:jar:2.14.2:compile
[INFO] |  |  +- org.apache.commons:commons-text:jar:1.10.0:compile
[INFO] |  |  \- cn.ijiami.framework:ijiami-framework-ldap:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |     \- org.springframework.boot:spring-boot-starter-data-ldap:jar:2.6.15:compile
[INFO] |  |        \- org.springframework.data:spring-data-ldap:jar:2.6.10:compile
[INFO] |  |           \- org.springframework.ldap:spring-ldap-core:jar:2.3.8.RELEASE:compile
[INFO] |  +- io.jsonwebtoken:jjwt:jar:0.9.0:compile
[INFO] |  +- org.aspectj:aspectjrt:jar:1.9.7:compile
[INFO] |  \- org.aspectj:aspectjweaver:jar:1.9.7:compile
[INFO] +- mysql:mysql-connector-java:jar:8.0.16:compile
[INFO] +- com.dm:DmJdbcDriver18:jar:1.8:compile
[INFO] +- cn.ijiami.detection:privacy-android-server-client:jar:1.0-SNAPSHOT:compile
[INFO] |  \- cn.ijiami.detection:privacy-server-client-base:jar:1.0-SNAPSHOT:compile
[INFO] +- cn.ijiami.detection:privacy-ios-server-client:jar:1.0-SNAPSHOT:compile
[INFO] +- org.slf4j:slf4j-api:jar:1.7.36:compile
[INFO] \- org.apache.commons:commons-lang3:jar:3.12.0:compile
[INFO] 
[INFO] -----------------< cn.ijiami.detection:detection-rest >-----------------
[INFO] Building detection-rest 1.0-SNAPSHOT                               [5/6]
[INFO]   from detection-rest\pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- dependency:3.2.0:tree (default-cli) @ detection-rest ---
[INFO] cn.ijiami.detection:detection-rest:jar:1.0-SNAPSHOT
[INFO] +- cn.ijiami.detection:detection-service-impl:jar:1.0-SNAPSHOT:compile
[INFO] |  +- org.csource:fastdfs-client-java:jar:1.27-SNAPSHOT:compile
[INFO] |  +- commons-io:commons-io:jar:2.5:compile
[INFO] |  +- com.github.tobato:fastdfs-client:jar:1.26.6:compile
[INFO] |  |  +- org.slf4j:jcl-over-slf4j:jar:1.7.36:compile
[INFO] |  |  +- ch.qos.logback:logback-classic:jar:1.2.13:compile
[INFO] |  |  |  \- ch.qos.logback:logback-core:jar:1.2.13:compile
[INFO] |  |  +- org.apache.commons:commons-pool2:jar:2.11.1:compile
[INFO] |  |  +- org.hibernate.validator:hibernate-validator:jar:6.2.5.Final:compile
[INFO] |  |  |  +- jakarta.validation:jakarta.validation-api:jar:2.0.2:compile
[INFO] |  |  |  +- org.jboss.logging:jboss-logging:jar:3.4.3.Final:compile
[INFO] |  |  |  \- com.fasterxml:classmate:jar:1.5.1:compile
[INFO] |  |  +- org.springframework:spring-context:jar:5.3.39:compile
[INFO] |  |  \- net.coobird:thumbnailator:jar:0.4.8:compile
[INFO] |  +- cn.ijiami.message:ijiami-message-core:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-websocket:jar:2.6.15:compile
[INFO] |  |     \- org.springframework:spring-websocket:jar:5.3.39:compile
[INFO] |  +- cn.ijiami.base:ijiami-base-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  |  \- com.alibaba.fastjson2:fastjson2:jar:2.0.38:compile
[INFO] |  +- cn.ijiami.message:ijiami-message-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  |  \- cn.ijiami.framework:ijiami-framework-mybatis:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |     +- com.mysql:mysql-connector-j:jar:8.4.0:compile
[INFO] |  |     +- org.mybatis.spring.boot:mybatis-spring-boot-starter:jar:2.2.2:compile
[INFO] |  |     |  +- org.springframework.boot:spring-boot-starter-***********************
[INFO] |  |     |  |  +- com.zaxxer:HikariCP:jar:4.0.3:compile
[INFO] |  |     |  |  \- org.springframework:spring-***********************
[INFO] |  |     |  +- org.mybatis.spring.boot:mybatis-spring-boot-autoconfigure:jar:2.2.2:compile
[INFO] |  |     |  +- org.mybatis:mybatis:jar:3.5.9:compile
[INFO] |  |     |  \- org.mybatis:mybatis-spring:jar:2.0.7:compile
[INFO] |  |     +- tk.mybatis:mapper-spring-boot-starter:jar:4.2.2:compile
[INFO] |  |     |  +- tk.mybatis:mapper-core:jar:4.2.2:compile
[INFO] |  |     |  |  \- javax.persistence:javax.persistence-api:jar:2.2:compile
[INFO] |  |     |  +- tk.mybatis:mapper-base:jar:4.2.2:compile
[INFO] |  |     |  +- tk.mybatis:mapper-weekend:jar:4.2.2:compile
[INFO] |  |     |  +- tk.mybatis:mapper-spring:jar:4.2.2:compile
[INFO] |  |     |  +- tk.mybatis:mapper-extra:jar:4.2.2:compile
[INFO] |  |     |  \- tk.mybatis:mapper-spring-boot-autoconfigure:jar:4.2.2:compile
[INFO] |  |     +- com.baomidou:mybatis-plus-boot-starter:jar:3.5.3.1:compile
[INFO] |  |     |  \- com.baomidou:mybatis-plus:jar:3.5.3.1:compile
[INFO] |  |     |     \- com.baomidou:mybatis-plus-extension:jar:3.5.3.1:compile
[INFO] |  |     |        \- com.baomidou:mybatis-plus-core:jar:3.5.3.1:compile
[INFO] |  |     |           \- com.baomidou:mybatis-plus-annotation:jar:3.5.3.1:compile
[INFO] |  |     \- com.alibaba:druid:jar:1.2.23:compile
[INFO] |  +- cn.ijiami.message:ijiami-message-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- org.springframework.kafka:spring-kafka:jar:2.9.13:compile
[INFO] |  |  +- org.springframework:spring-messaging:jar:5.3.39:compile
[INFO] |  |  +- org.springframework:spring-tx:jar:5.3.39:compile
[INFO] |  |  +- org.springframework.retry:spring-retry:jar:1.3.4:compile
[INFO] |  |  \- org.apache.kafka:kafka-clients:jar:3.0.2:compile
[INFO] |  |     +- com.github.luben:zstd-jni:jar:1.5.0-2:runtime
[INFO] |  |     +- org.lz4:lz4-java:jar:1.7.1:runtime
[INFO] |  |     \- org.xerial.snappy:snappy-java:jar:1.1.8.1:runtime
[INFO] |  +- cn.ijiami.framework:ijiami-framework-websocket:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-redis:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-data-redis:jar:2.6.15:compile
[INFO] |  |  |  +- org.springframework.data:spring-data-redis:jar:2.6.10:compile
[INFO] |  |  |  |  +- org.springframework.data:spring-data-keyvalue:jar:2.6.10:compile
[INFO] |  |  |  |  \- org.springframework:spring-oxm:jar:5.3.39:compile
[INFO] |  |  |  \- io.lettuce:lettuce-core:jar:6.1.10.RELEASE:compile
[INFO] |  |  |     +- io.netty:netty-common:jar:4.1.92.Final:compile
[INFO] |  |  |     +- io.netty:netty-handler:jar:4.1.92.Final:compile
[INFO] |  |  |     |  +- io.netty:netty-resolver:jar:4.1.92.Final:compile
[INFO] |  |  |     |  +- io.netty:netty-buffer:jar:4.1.92.Final:compile
[INFO] |  |  |     |  +- io.netty:netty-transport-native-unix-common:jar:4.1.92.Final:compile
[INFO] |  |  |     |  \- io.netty:netty-codec:jar:4.1.92.Final:compile
[INFO] |  |  |     +- io.netty:netty-transport:jar:4.1.92.Final:compile
[INFO] |  |  |     \- io.projectreactor:reactor-core:jar:3.4.29:compile
[INFO] |  |  |        \- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-databind:jar:2.14.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-cache:jar:2.6.15:compile
[INFO] |  |     \- org.springframework:spring-context-support:jar:5.3.39:compile
[INFO] |  +- cn.ijiami.detection:detection-service-api:jar:1.0-SNAPSHOT:compile
[INFO] |  |  +- cn.ijiami.framework:ijiami-framework-openfeign:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  |  \- org.springframework.cloud:spring-cloud-starter-loadbalancer:jar:3.1.4:compile
[INFO] |  |  |     +- org.springframework.cloud:spring-cloud-loadbalancer:jar:3.1.4:compile
[INFO] |  |  |     |  \- io.projectreactor.addons:reactor-extra:jar:3.4.10:compile
[INFO] |  |  |     \- com.stoyanr:evictor:jar:1.0.0:compile
[INFO] |  |  +- com.ijm.ios:RuntimeDetection:jar:0.0.2-SNAPSHOT:compile
[INFO] |  |  |  \- commons-logging:commons-logging-api:jar:1.1:compile
[INFO] |  |  +- com.ocpframework.ocp:ocp-sdk-detect-enginer:jar:1.0-SNAPSHOT:compile
[INFO] |  |  |  +- com.ocpframework.ocp:ocp-sdk-detect-common:jar:1.0-SNAPSHOT:compile
[INFO] |  |  |  \- org.eclipse.jgit:org.eclipse.jgit:jar:5.13.1.202206130422-r:compile
[INFO] |  |  |     \- com.googlecode.javaewah:JavaEWAH:jar:1.1.13:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-aop:jar:2.6.15:compile
[INFO] |  |  \- com.google.code.gson:gson:jar:2.8.9:compile
[INFO] |  +- cn.ijiami.detection:detection-core:jar:1.0-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.report:ijiami-report-service-api:jar:2.6.10-SNAPSHOT:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-freemarker:jar:2.6.15:compile
[INFO] |  +- cn.ijiami.report:ijiami-report-service-impl:jar:2.6.10-SNAPSHOT:compile
[INFO] |  |  \- com.aspose:aspose.words:jar:15.8.0:compile
[INFO] |  +- org.apache.ant:ant:jar:1.10.5:compile
[INFO] |  |  \- org.apache.ant:ant-launcher:jar:1.10.5:compile
[INFO] |  +- org.apache.httpcomponents:httpclient:jar:4.5.14:compile
[INFO] |  +- org.apache.httpcomponents:httpcore:jar:4.4.16:compile
[INFO] |  +- org.apache.httpcomponents:httpmime:jar:4.5.14:compile
[INFO] |  +- com.squareup.okhttp3:okhttp:jar:4.9.3:compile
[INFO] |  |  +- com.squareup.okio:okio:jar:2.8.0:compile
[INFO] |  |  |  \- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.6.21:compile
[INFO] |  |  \- org.jetbrains.kotlin:kotlin-stdlib:jar:1.6.21:compile
[INFO] |  |     \- org.jetbrains:annotations:jar:13.0:compile
[INFO] |  +- org.springframework:spring-test:jar:5.3.39:compile
[INFO] |  +- com.maxmind.geoip2:geoip2:jar:2.12.0:compile
[INFO] |  |  +- com.maxmind.db:maxmind-db:jar:1.2.2:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-core:jar:2.14.2:compile
[INFO] |  |  \- com.fasterxml.jackson.core:jackson-annotations:jar:2.14.2:compile
[INFO] |  +- org.freemarker:freemarker:jar:2.3.32:compile
[INFO] |  +- cn.ijiami.msmp:msmp-ios-detection-tool:jar:2.1.0-SNAPSHOT:compile
[INFO] |  |  +- org.apache.logging.log4j:log4j-api:jar:2.18.0:compile
[INFO] |  |  +- org.apache.logging.log4j:log4j-core:jar:2.18.0:compile
[INFO] |  |  +- org.xerial:sqlite-jdbc:jar:********:compile
[INFO] |  |  +- cn.ijiami.ios:XibDump:jar:1.0.0-SNAPSHOT:compile
[INFO] |  |  +- com.dd.plist:dd-plist:jar:1.0:compile
[INFO] |  |  +- net.lingala.zip4j:zip4j:jar:1.3.2:compile
[INFO] |  |  \- com.jcraft:jzlib:jar:1.1.3:compile
[INFO] |  +- com.googlecode.plist:dd-plist:jar:1.16:compile
[INFO] |  +- cn.ijiami:z-csp-device:jar:1.0.0:compile
[INFO] |  |  \- joda-time:joda-time:jar:2.10.6:compile
[INFO] |  +- dom4j:dom4j:jar:1.6.1:compile
[INFO] |  |  \- xml-apis:xml-apis:jar:1.0.b2:compile
[INFO] |  +- com.jcraft:jsch:jar:0.1.54:compile
[INFO] |  +- net.dongliu:apk-parser:jar:2.6.10:compile
[INFO] |  +- com.google.zxing:core:jar:3.3.0:compile
[INFO] |  +- com.google.zxing:javase:jar:3.3.0:compile
[INFO] |  |  +- com.beust:jcommander:jar:1.48:compile
[INFO] |  |  \- com.github.jai-imageio:jai-imageio-core:jar:1.3.1:compile
[INFO] |  +- org.java-websocket:Java-WebSocket:jar:1.5.2:compile
[INFO] |  +- com.qcloud:cos_api:jar:5.6.8:compile
[INFO] |  |  \- org.bouncycastle:bcprov-jdk15on:jar:1.59:compile
[INFO] |  +- org.jsoup:jsoup:jar:1.11.3:compile
[INFO] |  +- com.github.houbb:opencc4j:jar:1.6.1:compile
[INFO] |  |  +- com.github.houbb:heaven:jar:0.1.129:compile
[INFO] |  |  |  \- org.apiguardian:apiguardian-api:jar:1.0.0:compile
[INFO] |  |  \- com.github.houbb:nlp-common:jar:0.0.4:compile
[INFO] |  +- org.tensorflow:tensorflow:jar:1.15.0:compile
[INFO] |  |  +- org.tensorflow:libtensorflow:jar:1.15.0:compile
[INFO] |  |  \- org.tensorflow:libtensorflow_jni:jar:1.15.0:compile
[INFO] |  +- com.itextpdf:itextpdf:jar:5.5.13:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.6.5:compile
[INFO] |  +- com.android.apksig:apksigner:jar:1.1:compile
[INFO] |  +- commons-httpclient:commons-httpclient:jar:3.1:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-apk:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-ipa:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  \- hashfile:hashfile:jar:1.0:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-email:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-mail:jar:2.6.15:compile
[INFO] |  |     \- com.sun.mail:jakarta.mail:jar:1.6.7:compile
[INFO] |  |        \- com.sun.activation:jakarta.activation:jar:1.2.2:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-test:jar:2.6.15:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter:jar:2.6.15:compile
[INFO] |  |  |  +- org.springframework.boot:spring-boot-starter-logging:jar:2.6.15:compile
[INFO] |  |  |  |  +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.18.0:compile
[INFO] |  |  |  |  \- org.slf4j:jul-to-slf4j:jar:1.7.36:compile
[INFO] |  |  |  +- jakarta.annotation:jakarta.annotation-api:jar:1.3.5:compile
[INFO] |  |  |  \- org.yaml:snakeyaml:jar:1.33:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-test:jar:2.6.15:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-test-autoconfigure:jar:2.6.15:compile
[INFO] |  |  +- com.jayway.jsonpath:json-path:jar:2.6.0:compile
[INFO] |  |  |  \- net.minidev:json-smart:jar:2.4.10:compile
[INFO] |  |  |     \- net.minidev:accessors-smart:jar:2.4.9:compile
[INFO] |  |  |        \- org.ow2.asm:asm:jar:9.3:compile
[INFO] |  |  +- jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.3:compile
[INFO] |  |  |  \- jakarta.activation:jakarta.activation-api:jar:1.2.2:compile
[INFO] |  |  +- org.assertj:assertj-core:jar:3.21.0:compile
[INFO] |  |  +- org.hamcrest:hamcrest:jar:2.2:compile
[INFO] |  |  +- org.junit.jupiter:junit-jupiter:jar:5.8.2:compile
[INFO] |  |  |  +- org.junit.jupiter:junit-jupiter-api:jar:5.8.2:compile
[INFO] |  |  |  |  +- org.opentest4j:opentest4j:jar:1.2.0:compile
[INFO] |  |  |  |  \- org.junit.platform:junit-platform-commons:jar:1.8.2:compile
[INFO] |  |  |  +- org.junit.jupiter:junit-jupiter-params:jar:5.8.2:compile
[INFO] |  |  |  \- org.junit.jupiter:junit-jupiter-engine:jar:5.8.2:runtime
[INFO] |  |  |     \- org.junit.platform:junit-platform-engine:jar:1.8.2:runtime
[INFO] |  |  +- org.mockito:mockito-core:jar:4.0.0:compile
[INFO] |  |  |  +- net.bytebuddy:byte-buddy:jar:1.11.22:compile
[INFO] |  |  |  +- net.bytebuddy:byte-buddy-agent:jar:1.11.22:compile
[INFO] |  |  |  \- org.objenesis:objenesis:jar:3.2:runtime
[INFO] |  |  +- org.mockito:mockito-junit-jupiter:jar:4.0.0:compile
[INFO] |  |  +- org.skyscreamer:jsonassert:jar:1.5.1:compile
[INFO] |  |  |  \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:compile
[INFO] |  |  \- org.xmlunit:xmlunit-core:jar:2.8.4:compile
[INFO] |  +- commons-net:commons-net:jar:3.5:compile
[INFO] |  +- org.apktool:apktool-lib:jar:2.6.0:compile
[INFO] |  |  +- org.apktool:brut.j.dir:jar:2.6.0:compile
[INFO] |  |  +- org.apktool:brut.j.util:jar:2.6.0:compile
[INFO] |  |  +- org.apktool:brut.j.common:jar:2.6.0:compile
[INFO] |  |  +- org.smali:baksmali:jar:2.5.2:runtime
[INFO] |  |  |  +- org.smali:dexlib2:jar:2.5.2:runtime
[INFO] |  |  |  \- org.smali:util:jar:2.5.2:runtime
[INFO] |  |  +- org.smali:smali:jar:2.5.2:runtime
[INFO] |  |  |  +- org.antlr:antlr:jar:3.5.2:runtime
[INFO] |  |  |  |  \- org.antlr:ST4:jar:4.0.8:runtime
[INFO] |  |  |  +- org.antlr:antlr-runtime:jar:3.5.2:runtime
[INFO] |  |  |  \- org.antlr:stringtemplate:jar:3.2.1:runtime
[INFO] |  |  |     \- antlr:antlr:jar:2.7.7:runtime
[INFO] |  |  +- org.yaml:snakeyaml:jar:android:1.29:runtime
[INFO] |  |  \- xpp3:xpp3:jar:1.1.4c:runtime
[INFO] |  +- cn.ijiami.detection:android-detection-decompiler:jar:1.7-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.detection:detection-common-utils:jar:1.7-SNAPSHOT:compile
[INFO] |  \- android.content.res.APKParser:APKParser-base:jar:2.0.1:compile
[INFO] +- cn.ijiami.ai:ijiami-ai-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-web:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-web:jar:2.6.15:compile
[INFO] |  |     +- org.springframework.boot:spring-boot-starter-json:jar:2.6.15:compile
[INFO] |  |     |  +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.14.2:compile
[INFO] |  |     |  +- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.14.2:compile
[INFO] |  |     |  \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.14.2:compile
[INFO] |  |     \- org.springframework.boot:spring-boot-starter-tomcat:jar:2.6.15:compile
[INFO] |  |        +- org.apache.tomcat.embed:tomcat-embed-core:jar:9.0.74:compile
[INFO] |  |        \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:9.0.74:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-swagger:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  +- com.github.xiaoymin:knife4j-spring-boot-starter:jar:3.0.3:compile
[INFO] |  |  |  +- com.github.xiaoymin:knife4j-spring-boot-autoconfigure:jar:3.0.3:compile
[INFO] |  |  |  |  +- com.github.xiaoymin:knife4j-spring:jar:3.0.3:compile
[INFO] |  |  |  |  |  +- com.github.xiaoymin:knife4j-annotations:jar:3.0.3:compile
[INFO] |  |  |  |  |  |  \- io.swagger.core.v3:swagger-annotations:jar:2.1.2:compile
[INFO] |  |  |  |  |  +- com.github.xiaoymin:knife4j-core:jar:3.0.3:compile
[INFO] |  |  |  |  |  +- org.javassist:javassist:jar:3.25.0-GA:compile
[INFO] |  |  |  |  |  +- io.springfox:springfox-swagger2:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-spi:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-schema:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-swagger-common:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-spring-web:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  |  \- io.github.classgraph:classgraph:jar:4.8.83:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-spring-webflux:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  \- org.mapstruct:mapstruct:jar:1.3.1.Final:runtime
[INFO] |  |  |  |  |  +- io.springfox:springfox-spring-webmvc:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  \- io.springfox:springfox-core:jar:3.0.0:compile
[INFO] |  |  |  |  |  +- io.springfox:springfox-oas:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  \- io.swagger.core.v3:swagger-models:jar:2.1.2:compile
[INFO] |  |  |  |  |  +- io.springfox:springfox-bean-validators:jar:3.0.0:compile
[INFO] |  |  |  |  |  +- io.swagger:swagger-models:jar:1.5.22:compile
[INFO] |  |  |  |  |  \- io.swagger:swagger-core:jar:1.5.22:compile
[INFO] |  |  |  |  |     \- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.14.2:compile
[INFO] |  |  |  |  \- io.springfox:springfox-boot-starter:jar:3.0.0:compile
[INFO] |  |  |  |     +- io.springfox:springfox-data-rest:jar:3.0.0:compile
[INFO] |  |  |  |     +- org.springframework.plugin:spring-plugin-core:jar:2.0.0.RELEASE:compile
[INFO] |  |  |  |     \- org.springframework.plugin:spring-plugin-metadata:jar:2.0.0.RELEASE:compile
[INFO] |  |  |  \- com.github.xiaoymin:knife4j-spring-ui:jar:3.0.3:compile
[INFO] |  |  \- io.swagger:swagger-annotations:jar:1.5.22:compile
[INFO] |  +- javax.validation:validation-api:jar:2.0.1.Final:compile
[INFO] |  \- cn.ijiami.ai:ijiami-ai-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |     +- cn.ijiami.ai:ijiami-ai-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |     \- cn.ijiami.framework:ijiami-framework-ai:jar:2.6.8-SNAPSHOT:compile
[INFO] +- cn.ijiami.manager:ijiami-manager-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.base:ijiami-base-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter-validation:jar:2.6.15:compile
[INFO] |     \- org.apache.tomcat.embed:tomcat-embed-el:jar:9.0.74:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-i18n:jar:2.6.8-SNAPSHOT:compile
[INFO] |  \- org.springframework:spring-webmvc:jar:5.3.39:compile
[INFO] |     +- org.springframework:spring-aop:jar:5.3.39:compile
[INFO] |     \- org.springframework:spring-expression:jar:5.3.39:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-file:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:2.6.15:compile
[INFO] |  |  \- org.springframework.boot:spring-boot:jar:2.6.15:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-common:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-file-fdfs:jar:2.6.8-SNAPSHOT:compile
[INFO] |  \- commons-codec:commons-codec:jar:1.15:compile
[INFO] +- cn.ijiami.organ:ijiami-organ-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.organ:ijiami-organ-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  \- cn.ijiami.base:ijiami-base-common:jar:2.6.11-SNAPSHOT:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-security:jar:2.6.15:compile
[INFO] |     |  +- org.springframework.security:spring-security-config:jar:5.8.16:compile
[INFO] |     |  \- org.springframework.security:spring-security-web:jar:5.8.16:compile
[INFO] |     \- org.apache.commons:commons-jexl:jar:2.1.1:compile
[INFO] +- cn.ijiami.organ:ijiami-organ-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] +- org.springframework.data:spring-data-commons:jar:2.6.10:compile
[INFO] |  +- org.springframework:spring-core:jar:5.3.39:compile
[INFO] |  |  \- org.springframework:spring-jcl:jar:5.3.39:compile
[INFO] |  \- org.springframework:spring-beans:jar:5.3.39:compile
[INFO] +- cn.ijiami.base:ijiami-base-email:jar:2.6.11-SNAPSHOT:compile
[INFO] |  \- commons-fileupload:commons-fileupload:jar:1.5:compile
[INFO] +- com.github.oshi:oshi-core:jar:6.4.0:compile
[INFO] +- net.java.dev.jna:jna:jar:5.12.0:compile
[INFO] +- net.java.dev.jna:jna-platform:jar:5.12.0:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-utils:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- cn.hutool:hutool-all:jar:5.8.36:compile
[INFO] |  +- javax.servlet:javax.servlet-api:jar:4.0.1:compile
[INFO] |  \- com.googlecode.aviator:aviator:jar:5.4.1:compile
[INFO] +- net.sf.json-lib:json-lib:jar:jdk15:2.4:compile (optional) 
[INFO] |  +- commons-beanutils:commons-beanutils:jar:1.8.0:compile
[INFO] |  +- commons-collections:commons-collections:jar:3.2.1:compile (optional) 
[INFO] |  +- commons-lang:commons-lang:jar:2.5:compile
[INFO] |  +- commons-logging:commons-logging:jar:1.1.1:compile
[INFO] |  \- net.sf.ezmorph:ezmorph:jar:1.0.6:compile (optional) 
[INFO] +- org.projectlombok:lombok:jar:1.18.24:compile
[INFO] +- com.alibaba:fastjson:jar:1.2.83:compile
[INFO] +- org.apache.poi:poi:jar:4.1.2:compile
[INFO] |  +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] |  \- com.zaxxer:SparseBitSet:jar:1.2:compile
[INFO] +- org.apache.commons:commons-collections4:jar:4.1:compile
[INFO] +- org.apache.poi:poi-ooxml:jar:4.1.2:compile
[INFO] |  +- org.apache.poi:poi-ooxml-schemas:jar:4.1.2:compile
[INFO] |  +- org.apache.commons:commons-compress:jar:1.19:compile
[INFO] |  \- com.github.virtuald:curvesapi:jar:1.06:compile
[INFO] +- org.apache.xmlbeans:xmlbeans:jar:3.1.0:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-mongodb:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-data-mongodb:jar:2.6.15:compile
[INFO] |  |  \- org.mongodb:mongodb-driver-sync:jar:4.4.2:compile
[INFO] |  |     \- org.mongodb:bson:jar:4.4.2:compile
[INFO] |  +- org.springframework.data:spring-data-mongodb:jar:3.3.10:compile
[INFO] |  |  \- org.mongodb:mongodb-driver-core:jar:4.4.2:compile
[INFO] |  \- com.github.pagehelper:pagehelper:jar:5.3.3:compile
[INFO] |     \- com.github.jsqlparser:jsqlparser:jar:4.5:compile
[INFO] +- com.google.guava:guava:jar:23.0:compile
[INFO] |  +- com.google.code.findbugs:jsr305:jar:1.3.9:compile
[INFO] |  +- com.google.errorprone:error_prone_annotations:jar:2.0.18:compile
[INFO] |  +- com.google.j2objc:j2objc-annotations:jar:1.1:compile
[INFO] |  \- org.codehaus.mojo:animal-sniffer-annotations:jar:1.14:compile
[INFO] +- org.springframework.cloud:spring-cloud-starter-openfeign:jar:2.1.3.RELEASE:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-starter:jar:3.1.4:compile
[INFO] |  |  +- org.springframework.cloud:spring-cloud-context:jar:3.1.4:compile
[INFO] |  |  \- org.springframework.security:spring-security-rsa:jar:1.0.11.RELEASE:compile
[INFO] |  |     \- org.bouncycastle:bcpkix-jdk15on:jar:1.69:compile
[INFO] |  |        \- org.bouncycastle:bcutil-jdk15on:jar:1.69:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-openfeign-core:jar:3.1.4:compile
[INFO] |  |  \- io.github.openfeign.form:feign-form-spring:jar:3.8.0:compile
[INFO] |  |     \- io.github.openfeign.form:feign-form:jar:3.8.0:compile
[INFO] |  +- org.springframework:spring-web:jar:5.3.39:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-commons:jar:3.1.4:compile
[INFO] |  |  \- org.springframework.security:spring-security-crypto:jar:5.8.16:compile
[INFO] |  +- io.github.openfeign:feign-core:jar:11.8:compile
[INFO] |  +- io.github.openfeign:feign-slf4j:jar:11.8:compile
[INFO] |  \- io.github.openfeign:feign-hystrix:jar:11.8:compile
[INFO] |     \- com.netflix.hystrix:hystrix-core:jar:1.5.18:compile
[INFO] |        +- com.netflix.archaius:archaius-core:jar:0.4.1:compile
[INFO] |        |  \- commons-configuration:commons-configuration:jar:1.8:compile
[INFO] |        +- io.reactivex:rxjava:jar:1.3.8:compile
[INFO] |        \- org.hdrhistogram:HdrHistogram:jar:2.1.9:compile
[INFO] +- cn.ijiami.authentication:authentication-rest:jar:2.6.10-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.authentication:authentication-service-impl:jar:2.6.10-SNAPSHOT:compile
[INFO] |  |  \- cn.ijiami.authentication:authentication-service-api:jar:2.6.10-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.manager:ijiami-manager-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.manager:ijiami-manager-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.manager:ijiami-manager-security:jar:2.6.11-SNAPSHOT:compile
[INFO] |  |  +- org.springframework.security:spring-security-jwt:jar:1.1.1.RELEASE:compile
[INFO] |  |  +- org.springframework.security.oauth:spring-security-oauth2:jar:2.3.8.RELEASE:compile
[INFO] |  |  |  \- org.springframework.security:spring-security-core:jar:5.8.16:compile
[INFO] |  |  +- org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13-atlassian-1:compile
[INFO] |  |  |  \- org.codehaus.jackson:jackson-core-asl:jar:1.9.13-atlassian-1:compile
[INFO] |  |  +- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:jar:2.14.2:compile
[INFO] |  |  +- org.apache.commons:commons-text:jar:1.10.0:compile
[INFO] |  |  \- cn.ijiami.framework:ijiami-framework-ldap:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |     \- org.springframework.boot:spring-boot-starter-data-ldap:jar:2.6.15:compile
[INFO] |  |        \- org.springframework.data:spring-data-ldap:jar:2.6.10:compile
[INFO] |  |           \- org.springframework.ldap:spring-ldap-core:jar:2.3.8.RELEASE:compile
[INFO] |  +- io.jsonwebtoken:jjwt:jar:0.9.0:compile
[INFO] |  +- org.aspectj:aspectjrt:jar:1.9.7:compile
[INFO] |  \- org.aspectj:aspectjweaver:jar:1.9.7:compile
[INFO] +- mysql:mysql-connector-java:jar:8.0.16:compile
[INFO] +- com.dm:DmJdbcDriver18:jar:1.8:compile
[INFO] +- cn.ijiami.detection:privacy-android-server-client:jar:1.0-SNAPSHOT:compile
[INFO] |  \- cn.ijiami.detection:privacy-server-client-base:jar:1.0-SNAPSHOT:compile
[INFO] +- cn.ijiami.detection:privacy-ios-server-client:jar:1.0-SNAPSHOT:compile
[INFO] +- org.slf4j:slf4j-api:jar:1.7.36:compile
[INFO] \- org.apache.commons:commons-lang3:jar:3.12.0:compile
[INFO] 
[INFO] -----------------< cn.ijiami.detection:detection-web >------------------
[INFO] Building detection-web 1.0-SNAPSHOT                                [6/6]
[INFO]   from detection-web\pom.xml
[INFO] --------------------------------[ war ]---------------------------------
[INFO] 
[INFO] --- dependency:3.2.0:tree (default-cli) @ detection-web ---
[INFO] cn.ijiami.detection:detection-web:war:1.0-SNAPSHOT
[INFO] +- javax.servlet:javax.servlet-api:jar:4.0.1:provided
[INFO] +- org.springframework.boot:spring-boot-starter-tomcat:jar:2.6.15:compile
[INFO] |  +- jakarta.annotation:jakarta.annotation-api:jar:1.3.5:compile
[INFO] |  +- org.apache.tomcat.embed:tomcat-embed-core:jar:9.0.74:compile
[INFO] |  +- org.apache.tomcat.embed:tomcat-embed-el:jar:9.0.74:compile
[INFO] |  \- org.apache.tomcat.embed:tomcat-embed-websocket:jar:9.0.74:compile
[INFO] +- cn.ijiami.manager:ijiami-manager-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-web:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-web:jar:2.6.15:compile
[INFO] |  |     \- org.springframework.boot:spring-boot-starter-json:jar:2.6.15:compile
[INFO] |  |        +- com.fasterxml.jackson.datatype:jackson-datatype-jdk8:jar:2.14.2:compile
[INFO] |  |        \- com.fasterxml.jackson.module:jackson-module-parameter-names:jar:2.14.2:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-swagger:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  +- com.github.xiaoymin:knife4j-spring-boot-starter:jar:3.0.3:compile
[INFO] |  |  |  +- com.github.xiaoymin:knife4j-spring-boot-autoconfigure:jar:3.0.3:compile
[INFO] |  |  |  |  +- com.github.xiaoymin:knife4j-spring:jar:3.0.3:compile
[INFO] |  |  |  |  |  +- com.github.xiaoymin:knife4j-annotations:jar:3.0.3:compile
[INFO] |  |  |  |  |  |  \- io.swagger.core.v3:swagger-annotations:jar:2.1.2:compile
[INFO] |  |  |  |  |  +- com.github.xiaoymin:knife4j-core:jar:3.0.3:compile
[INFO] |  |  |  |  |  +- io.springfox:springfox-swagger2:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-spi:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-schema:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-swagger-common:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-spring-web:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  |  \- io.github.classgraph:classgraph:jar:4.8.83:compile
[INFO] |  |  |  |  |  |  +- io.springfox:springfox-spring-webflux:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  \- org.mapstruct:mapstruct:jar:1.3.1.Final:runtime
[INFO] |  |  |  |  |  +- io.springfox:springfox-spring-webmvc:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  \- io.springfox:springfox-core:jar:3.0.0:compile
[INFO] |  |  |  |  |  +- io.springfox:springfox-oas:jar:3.0.0:compile
[INFO] |  |  |  |  |  |  \- io.swagger.core.v3:swagger-models:jar:2.1.2:compile
[INFO] |  |  |  |  |  +- io.springfox:springfox-bean-validators:jar:3.0.0:compile
[INFO] |  |  |  |  |  +- io.swagger:swagger-models:jar:1.5.22:compile
[INFO] |  |  |  |  |  \- io.swagger:swagger-core:jar:1.5.22:compile
[INFO] |  |  |  |  |     \- com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:jar:2.14.2:compile
[INFO] |  |  |  |  \- io.springfox:springfox-boot-starter:jar:3.0.0:compile
[INFO] |  |  |  |     +- io.springfox:springfox-data-rest:jar:3.0.0:compile
[INFO] |  |  |  |     +- org.springframework.plugin:spring-plugin-core:jar:2.0.0.RELEASE:compile
[INFO] |  |  |  |     \- org.springframework.plugin:spring-plugin-metadata:jar:2.0.0.RELEASE:compile
[INFO] |  |  |  \- com.github.xiaoymin:knife4j-spring-ui:jar:3.0.3:compile
[INFO] |  |  \- io.swagger:swagger-annotations:jar:1.5.22:compile
[INFO] |  \- javax.validation:validation-api:jar:2.0.1.Final:compile
[INFO] +- cn.ijiami.manager:ijiami-manager-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- org.aspectj:aspectjrt:jar:1.9.7:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-email:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-starter-mail:jar:2.6.15:compile
[INFO] |  |  |  +- org.springframework:spring-context-support:jar:5.3.39:compile
[INFO] |  |  |  \- com.sun.mail:jakarta.mail:jar:1.6.7:compile
[INFO] |  |  |     \- com.sun.activation:jakarta.activation:jar:1.2.2:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-freemarker:jar:2.6.15:compile
[INFO] |  \- cn.ijiami.framework:ijiami-framework-redis:jar:2.6.8-SNAPSHOT:compile
[INFO] |     +- org.springframework.boot:spring-boot-starter-data-redis:jar:2.6.15:compile
[INFO] |     |  +- org.springframework.data:spring-data-redis:jar:2.6.10:compile
[INFO] |     |  |  +- org.springframework.data:spring-data-keyvalue:jar:2.6.10:compile
[INFO] |     |  |  \- org.springframework:spring-oxm:jar:5.3.39:compile
[INFO] |     |  \- io.lettuce:lettuce-core:jar:6.1.10.RELEASE:compile
[INFO] |     |     +- io.netty:netty-common:jar:4.1.92.Final:compile
[INFO] |     |     +- io.netty:netty-handler:jar:4.1.92.Final:compile
[INFO] |     |     |  +- io.netty:netty-resolver:jar:4.1.92.Final:compile
[INFO] |     |     |  +- io.netty:netty-buffer:jar:4.1.92.Final:compile
[INFO] |     |     |  +- io.netty:netty-transport-native-unix-common:jar:4.1.92.Final:compile
[INFO] |     |     |  \- io.netty:netty-codec:jar:4.1.92.Final:compile
[INFO] |     |     +- io.netty:netty-transport:jar:4.1.92.Final:compile
[INFO] |     |     \- io.projectreactor:reactor-core:jar:3.4.29:compile
[INFO] |     |        \- org.reactivestreams:reactive-streams:jar:1.0.4:compile
[INFO] |     +- com.fasterxml.jackson.core:jackson-databind:jar:2.14.2:compile
[INFO] |     \- org.springframework.boot:spring-boot-starter-cache:jar:2.6.15:compile
[INFO] +- cn.ijiami.manager:ijiami-manager-security:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-security:jar:2.6.15:compile
[INFO] |  |  +- org.springframework:spring-aop:jar:5.3.39:compile
[INFO] |  |  +- org.springframework.security:spring-security-config:jar:5.8.16:compile
[INFO] |  |  \- org.springframework.security:spring-security-web:jar:5.8.16:compile
[INFO] |  +- org.springframework.security:spring-security-jwt:jar:1.1.1.RELEASE:compile
[INFO] |  |  \- org.bouncycastle:bcpkix-jdk15on:jar:1.64:compile
[INFO] |  +- org.springframework.security.oauth:spring-security-oauth2:jar:2.3.8.RELEASE:compile
[INFO] |  |  +- org.springframework:spring-context:jar:5.3.39:compile
[INFO] |  |  \- org.springframework.security:spring-security-core:jar:5.8.16:compile
[INFO] |  +- org.codehaus.jackson:jackson-mapper-asl:jar:1.9.13-atlassian-1:compile
[INFO] |  |  \- org.codehaus.jackson:jackson-core-asl:jar:1.9.13-atlassian-1:compile
[INFO] |  +- com.alibaba.fastjson2:fastjson2:jar:2.0.38:compile
[INFO] |  +- com.fasterxml.jackson.module:jackson-module-jaxb-annotations:jar:2.14.2:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-annotations:jar:2.14.2:compile
[INFO] |  |  +- com.fasterxml.jackson.core:jackson-core:jar:2.14.2:compile
[INFO] |  |  +- jakarta.xml.bind:jakarta.xml.bind-api:jar:2.3.3:compile
[INFO] |  |  \- jakarta.activation:jakarta.activation-api:jar:1.2.2:compile
[INFO] |  +- org.apache.commons:commons-text:jar:1.10.0:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-ldap:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-data-ldap:jar:2.6.15:compile
[INFO] |  |     \- org.springframework.data:spring-data-ldap:jar:2.6.10:compile
[INFO] |  |        \- org.springframework.ldap:spring-ldap-core:jar:2.3.8.RELEASE:compile
[INFO] |  \- cn.ijiami.framework:ijiami-framework-websocket:jar:2.6.8-SNAPSHOT:compile
[INFO] +- cn.ijiami.base:ijiami-base-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.base:ijiami-base-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  \- commons-fileupload:commons-fileupload:jar:1.5:compile
[INFO] +- cn.ijiami.message:ijiami-message-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] |  \- cn.ijiami.base:ijiami-base-common:jar:2.6.11-SNAPSHOT:compile
[INFO] |     +- org.apache.commons:commons-jexl:jar:2.1.1:compile
[INFO] |     \- cn.ijiami.framework:ijiami-framework-mybatis:jar:2.6.8-SNAPSHOT:compile
[INFO] |        +- com.mysql:mysql-connector-j:jar:8.4.0:compile
[INFO] |        +- org.mybatis.spring.boot:mybatis-spring-boot-starter:jar:2.2.2:compile
[INFO] |        |  +- org.springframework.boot:spring-boot-starter-***********************
[INFO] |        |  |  +- com.zaxxer:HikariCP:jar:4.0.3:compile
[INFO] |        |  |  \- org.springframework:spring-***********************
[INFO] |        |  +- org.mybatis.spring.boot:mybatis-spring-boot-autoconfigure:jar:2.2.2:compile
[INFO] |        |  +- org.mybatis:mybatis:jar:3.5.9:compile
[INFO] |        |  \- org.mybatis:mybatis-spring:jar:2.0.7:compile
[INFO] |        +- tk.mybatis:mapper-spring-boot-starter:jar:4.2.2:compile
[INFO] |        |  +- tk.mybatis:mapper-core:jar:4.2.2:compile
[INFO] |        |  |  \- javax.persistence:javax.persistence-api:jar:2.2:compile
[INFO] |        |  +- tk.mybatis:mapper-base:jar:4.2.2:compile
[INFO] |        |  +- tk.mybatis:mapper-weekend:jar:4.2.2:compile
[INFO] |        |  +- tk.mybatis:mapper-spring:jar:4.2.2:compile
[INFO] |        |  +- tk.mybatis:mapper-extra:jar:4.2.2:compile
[INFO] |        |  \- tk.mybatis:mapper-spring-boot-autoconfigure:jar:4.2.2:compile
[INFO] |        +- com.baomidou:mybatis-plus-boot-starter:jar:3.5.3.1:compile
[INFO] |        |  \- com.baomidou:mybatis-plus:jar:3.5.3.1:compile
[INFO] |        |     \- com.baomidou:mybatis-plus-extension:jar:3.5.3.1:compile
[INFO] |        |        \- com.baomidou:mybatis-plus-core:jar:3.5.3.1:compile
[INFO] |        |           \- com.baomidou:mybatis-plus-annotation:jar:3.5.3.1:compile
[INFO] |        \- com.alibaba:druid:jar:1.2.23:compile
[INFO] +- cn.ijiami.message:ijiami-message-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  \- cn.ijiami.message:ijiami-message-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] +- cn.ijiami.detection:detection-rest:jar:1.0-SNAPSHOT:compile
[INFO] +- cn.ijiami.detection:detection-service-api:jar:1.0-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-openfeign:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  \- org.springframework.cloud:spring-cloud-starter-loadbalancer:jar:3.1.4:compile
[INFO] |  |     +- org.springframework.cloud:spring-cloud-loadbalancer:jar:3.1.4:compile
[INFO] |  |     |  \- io.projectreactor.addons:reactor-extra:jar:3.4.10:compile
[INFO] |  |     \- com.stoyanr:evictor:jar:1.0.0:compile
[INFO] |  +- com.ijm.ios:RuntimeDetection:jar:0.0.2-SNAPSHOT:compile
[INFO] |  |  \- commons-logging:commons-logging-api:jar:1.1:compile
[INFO] |  +- com.ocpframework.ocp:ocp-sdk-detect-enginer:jar:1.0-SNAPSHOT:compile
[INFO] |  |  +- org.xerial:sqlite-jdbc:jar:********:compile
[INFO] |  |  +- com.ocpframework.ocp:ocp-sdk-detect-common:jar:1.0-SNAPSHOT:compile
[INFO] |  |  \- org.eclipse.jgit:org.eclipse.jgit:jar:5.13.1.202206130422-r:compile
[INFO] |  |     \- com.googlecode.javaewah:JavaEWAH:jar:1.1.13:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-aop:jar:2.6.15:compile
[INFO] |  \- com.google.code.gson:gson:jar:2.8.9:compile
[INFO] +- cn.ijiami.detection:detection-service-impl:jar:1.0-SNAPSHOT:compile
[INFO] |  +- org.csource:fastdfs-client-java:jar:1.27-SNAPSHOT:compile
[INFO] |  +- commons-io:commons-io:jar:2.5:compile
[INFO] |  +- com.github.tobato:fastdfs-client:jar:1.26.6:compile
[INFO] |  |  +- org.slf4j:jcl-over-slf4j:jar:1.7.36:compile
[INFO] |  |  +- ch.qos.logback:logback-classic:jar:1.2.13:compile
[INFO] |  |  |  \- ch.qos.logback:logback-core:jar:1.2.13:compile
[INFO] |  |  +- org.apache.commons:commons-pool2:jar:2.11.1:compile
[INFO] |  |  +- org.hibernate.validator:hibernate-validator:jar:6.2.5.Final:compile
[INFO] |  |  |  +- jakarta.validation:jakarta.validation-api:jar:2.0.2:compile
[INFO] |  |  |  +- org.jboss.logging:jboss-logging:jar:3.4.3.Final:compile
[INFO] |  |  |  \- com.fasterxml:classmate:jar:1.5.1:compile
[INFO] |  |  \- net.coobird:thumbnailator:jar:0.4.8:compile
[INFO] |  +- cn.ijiami.message:ijiami-message-core:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- org.springframework.kafka:spring-kafka:jar:2.9.13:compile
[INFO] |  |  +- org.springframework:spring-messaging:jar:5.3.39:compile
[INFO] |  |  +- org.springframework:spring-tx:jar:5.3.39:compile
[INFO] |  |  +- org.springframework.retry:spring-retry:jar:1.3.4:compile
[INFO] |  |  \- org.apache.kafka:kafka-clients:jar:3.0.2:compile
[INFO] |  |     +- com.github.luben:zstd-jni:jar:1.5.0-2:runtime
[INFO] |  |     +- org.lz4:lz4-java:jar:1.7.1:runtime
[INFO] |  |     \- org.xerial.snappy:snappy-java:jar:1.1.8.1:runtime
[INFO] |  +- cn.ijiami.report:ijiami-report-service-api:jar:2.6.10-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.report:ijiami-report-service-impl:jar:2.6.10-SNAPSHOT:compile
[INFO] |  |  \- com.aspose:aspose.words:jar:15.8.0:compile
[INFO] |  +- org.apache.ant:ant:jar:1.10.5:compile
[INFO] |  |  \- org.apache.ant:ant-launcher:jar:1.10.5:compile
[INFO] |  +- org.apache.httpcomponents:httpclient:jar:4.5.14:compile
[INFO] |  +- org.apache.httpcomponents:httpcore:jar:4.4.16:compile
[INFO] |  +- org.apache.httpcomponents:httpmime:jar:4.5.14:compile
[INFO] |  +- com.squareup.okhttp3:okhttp:jar:4.9.3:compile
[INFO] |  |  +- com.squareup.okio:okio:jar:2.8.0:compile
[INFO] |  |  |  \- org.jetbrains.kotlin:kotlin-stdlib-common:jar:1.6.21:compile
[INFO] |  |  \- org.jetbrains.kotlin:kotlin-stdlib:jar:1.6.21:compile
[INFO] |  |     \- org.jetbrains:annotations:jar:13.0:compile
[INFO] |  +- org.springframework:spring-test:jar:5.3.39:compile
[INFO] |  +- com.maxmind.geoip2:geoip2:jar:2.12.0:compile
[INFO] |  |  \- com.maxmind.db:maxmind-db:jar:1.2.2:compile
[INFO] |  +- org.freemarker:freemarker:jar:2.3.32:compile
[INFO] |  +- cn.ijiami.msmp:msmp-ios-detection-tool:jar:2.1.0-SNAPSHOT:compile
[INFO] |  |  +- org.apache.logging.log4j:log4j-api:jar:2.18.0:compile
[INFO] |  |  +- org.apache.logging.log4j:log4j-core:jar:2.18.0:compile
[INFO] |  |  +- cn.ijiami.ios:XibDump:jar:1.0.0-SNAPSHOT:compile
[INFO] |  |  +- com.dd.plist:dd-plist:jar:1.0:compile
[INFO] |  |  +- net.lingala.zip4j:zip4j:jar:1.3.2:compile
[INFO] |  |  \- com.jcraft:jzlib:jar:1.1.3:compile
[INFO] |  +- com.googlecode.plist:dd-plist:jar:1.16:compile
[INFO] |  +- cn.ijiami:z-csp-device:jar:1.0.0:compile
[INFO] |  |  \- joda-time:joda-time:jar:2.10.6:compile
[INFO] |  +- dom4j:dom4j:jar:1.6.1:compile
[INFO] |  |  \- xml-apis:xml-apis:jar:1.0.b2:compile
[INFO] |  +- com.jcraft:jsch:jar:0.1.54:compile
[INFO] |  +- net.dongliu:apk-parser:jar:2.6.10:compile
[INFO] |  +- com.google.zxing:core:jar:3.3.0:compile
[INFO] |  +- com.google.zxing:javase:jar:3.3.0:compile
[INFO] |  |  +- com.beust:jcommander:jar:1.48:compile
[INFO] |  |  \- com.github.jai-imageio:jai-imageio-core:jar:1.3.1:compile
[INFO] |  +- org.java-websocket:Java-WebSocket:jar:1.5.2:compile
[INFO] |  +- com.qcloud:cos_api:jar:5.6.8:compile
[INFO] |  |  \- org.bouncycastle:bcprov-jdk15on:jar:1.59:compile
[INFO] |  +- org.jsoup:jsoup:jar:1.11.3:compile
[INFO] |  +- com.github.houbb:opencc4j:jar:1.6.1:compile
[INFO] |  |  +- com.github.houbb:heaven:jar:0.1.129:compile
[INFO] |  |  |  \- org.apiguardian:apiguardian-api:jar:1.0.0:compile
[INFO] |  |  \- com.github.houbb:nlp-common:jar:0.0.4:compile
[INFO] |  +- org.tensorflow:tensorflow:jar:1.15.0:compile
[INFO] |  |  +- org.tensorflow:libtensorflow:jar:1.15.0:compile
[INFO] |  |  \- org.tensorflow:libtensorflow_jni:jar:1.15.0:compile
[INFO] |  +- com.itextpdf:itextpdf:jar:5.5.13:compile
[INFO] |  +- org.lionsoul:ip2region:jar:2.6.5:compile
[INFO] |  +- com.android.apksig:apksigner:jar:1.1:compile
[INFO] |  +- commons-httpclient:commons-httpclient:jar:3.1:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-apk:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-ipa:jar:2.6.8-SNAPSHOT:compile
[INFO] |  |  \- hashfile:hashfile:jar:1.0:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-test:jar:2.6.15:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-test:jar:2.6.15:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-test-autoconfigure:jar:2.6.15:compile
[INFO] |  |  +- com.jayway.jsonpath:json-path:jar:2.6.0:compile
[INFO] |  |  |  \- net.minidev:json-smart:jar:2.4.10:compile
[INFO] |  |  |     \- net.minidev:accessors-smart:jar:2.4.9:compile
[INFO] |  |  |        \- org.ow2.asm:asm:jar:9.3:compile
[INFO] |  |  +- org.assertj:assertj-core:jar:3.21.0:compile
[INFO] |  |  +- org.hamcrest:hamcrest:jar:2.2:compile
[INFO] |  |  +- org.junit.jupiter:junit-jupiter:jar:5.8.2:compile
[INFO] |  |  |  +- org.junit.jupiter:junit-jupiter-api:jar:5.8.2:compile
[INFO] |  |  |  |  +- org.opentest4j:opentest4j:jar:1.2.0:compile
[INFO] |  |  |  |  \- org.junit.platform:junit-platform-commons:jar:1.8.2:compile
[INFO] |  |  |  +- org.junit.jupiter:junit-jupiter-params:jar:5.8.2:compile
[INFO] |  |  |  \- org.junit.jupiter:junit-jupiter-engine:jar:5.8.2:runtime
[INFO] |  |  |     \- org.junit.platform:junit-platform-engine:jar:1.8.2:runtime
[INFO] |  |  +- org.mockito:mockito-core:jar:4.0.0:compile
[INFO] |  |  |  +- net.bytebuddy:byte-buddy:jar:1.11.22:compile
[INFO] |  |  |  +- net.bytebuddy:byte-buddy-agent:jar:1.11.22:compile
[INFO] |  |  |  \- org.objenesis:objenesis:jar:3.2:runtime
[INFO] |  |  +- org.mockito:mockito-junit-jupiter:jar:4.0.0:compile
[INFO] |  |  +- org.skyscreamer:jsonassert:jar:1.5.1:compile
[INFO] |  |  |  \- com.vaadin.external.google:android-json:jar:0.0.20131108.vaadin1:compile
[INFO] |  |  \- org.xmlunit:xmlunit-core:jar:2.8.4:compile
[INFO] |  +- commons-net:commons-net:jar:3.5:compile
[INFO] |  +- org.apktool:apktool-lib:jar:2.6.0:compile
[INFO] |  |  +- org.apktool:brut.j.dir:jar:2.6.0:compile
[INFO] |  |  +- org.apktool:brut.j.util:jar:2.6.0:compile
[INFO] |  |  +- org.apktool:brut.j.common:jar:2.6.0:compile
[INFO] |  |  +- org.smali:baksmali:jar:2.5.2:runtime
[INFO] |  |  |  +- org.smali:dexlib2:jar:2.5.2:runtime
[INFO] |  |  |  \- org.smali:util:jar:2.5.2:runtime
[INFO] |  |  +- org.smali:smali:jar:2.5.2:runtime
[INFO] |  |  |  +- org.antlr:antlr:jar:3.5.2:runtime
[INFO] |  |  |  |  \- org.antlr:ST4:jar:4.0.8:runtime
[INFO] |  |  |  +- org.antlr:antlr-runtime:jar:3.5.2:runtime
[INFO] |  |  |  \- org.antlr:stringtemplate:jar:3.2.1:runtime
[INFO] |  |  |     \- antlr:antlr:jar:2.7.7:runtime
[INFO] |  |  +- org.yaml:snakeyaml:jar:android:1.29:runtime
[INFO] |  |  \- xpp3:xpp3:jar:1.1.4c:runtime
[INFO] |  +- cn.ijiami.detection:android-detection-decompiler:jar:1.7-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.detection:detection-common-utils:jar:1.7-SNAPSHOT:compile
[INFO] |  \- android.content.res.APKParser:APKParser-base:jar:2.0.1:compile
[INFO] +- cn.ijiami.detection:detection-core:jar:1.0-SNAPSHOT:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter-websocket:jar:2.6.15:compile
[INFO] |     \- org.springframework:spring-websocket:jar:5.3.39:compile
[INFO] +- org.springframework.cloud:spring-cloud-starter-bootstrap:jar:3.1.4:compile
[INFO] |  \- org.springframework.cloud:spring-cloud-starter:jar:3.1.4:compile
[INFO] |     \- org.springframework.security:spring-security-rsa:jar:1.0.11.RELEASE:compile
[INFO] +- com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-discovery:jar:2021.0.4.0:compile
[INFO] |  +- com.alibaba.cloud:spring-cloud-alibaba-commons:jar:2021.0.4.0:compile
[INFO] |  +- com.alibaba.nacos:nacos-client:jar:2.0.4:compile
[INFO] |  |  +- org.apache.httpcomponents:httpasyncclient:jar:4.1.5:compile
[INFO] |  |  |  \- org.apache.httpcomponents:httpcore-nio:jar:4.4.16:compile
[INFO] |  |  +- org.reflections:reflections:jar:0.9.11:compile
[INFO] |  |  |  \- org.javassist:javassist:jar:3.21.0-GA:compile
[INFO] |  |  \- io.prometheus:simpleclient:jar:0.12.0:compile
[INFO] |  |     +- io.prometheus:simpleclient_tracer_otel:jar:0.12.0:compile
[INFO] |  |     |  \- io.prometheus:simpleclient_tracer_common:jar:0.12.0:compile
[INFO] |  |     \- io.prometheus:simpleclient_tracer_otel_agent:jar:0.12.0:compile
[INFO] |  +- com.alibaba.spring:spring-context-support:jar:1.0.11:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-commons:jar:3.1.4:compile
[INFO] |  |  \- org.springframework.security:spring-security-crypto:jar:5.8.16:compile
[INFO] |  \- org.springframework.cloud:spring-cloud-context:jar:3.1.4:compile
[INFO] +- com.alibaba.cloud:spring-cloud-starter-alibaba-nacos-config:jar:2021.0.4.0:compile
[INFO] +- org.springframework.boot:spring-boot-starter-actuator:jar:2.6.15:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter:jar:2.6.15:compile
[INFO] |  |  +- org.springframework.boot:spring-boot:jar:2.6.15:compile
[INFO] |  |  \- org.springframework.boot:spring-boot-starter-logging:jar:2.6.15:compile
[INFO] |  |     +- org.apache.logging.log4j:log4j-to-slf4j:jar:2.18.0:compile
[INFO] |  |     \- org.slf4j:jul-to-slf4j:jar:1.7.36:compile
[INFO] |  +- org.springframework.boot:spring-boot-actuator-autoconfigure:jar:2.6.15:compile
[INFO] |  |  +- org.springframework.boot:spring-boot-actuator:jar:2.6.15:compile
[INFO] |  |  \- com.fasterxml.jackson.datatype:jackson-datatype-jsr310:jar:2.14.2:compile
[INFO] |  \- io.micrometer:micrometer-core:jar:1.8.13:compile
[INFO] |     +- org.hdrhistogram:HdrHistogram:jar:2.1.12:compile
[INFO] |     \- org.latencyutils:LatencyUtils:jar:2.0.3:runtime
[INFO] +- org.yaml:snakeyaml:jar:1.33:compile
[INFO] +- cn.ijiami.ai:ijiami-ai-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] |  \- cn.ijiami.ai:ijiami-ai-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |     +- cn.ijiami.ai:ijiami-ai-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |     \- cn.ijiami.framework:ijiami-framework-ai:jar:2.6.8-SNAPSHOT:compile
[INFO] +- cn.ijiami.manager:ijiami-manager-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.base:ijiami-base-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] |  \- org.springframework.boot:spring-boot-starter-validation:jar:2.6.15:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-i18n:jar:2.6.8-SNAPSHOT:compile
[INFO] |  \- org.springframework:spring-webmvc:jar:5.3.39:compile
[INFO] |     \- org.springframework:spring-expression:jar:5.3.39:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-file:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- org.springframework.boot:spring-boot-autoconfigure:jar:2.6.15:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-common:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.framework:ijiami-framework-file-fdfs:jar:2.6.8-SNAPSHOT:compile
[INFO] |  \- commons-codec:commons-codec:jar:1.15:compile
[INFO] +- cn.ijiami.organ:ijiami-organ-service-impl:jar:2.6.11-SNAPSHOT:compile
[INFO] |  \- cn.ijiami.organ:ijiami-organ-service-api:jar:2.6.11-SNAPSHOT:compile
[INFO] +- cn.ijiami.organ:ijiami-organ-rest:jar:2.6.11-SNAPSHOT:compile
[INFO] +- org.springframework.data:spring-data-commons:jar:2.6.10:compile
[INFO] |  +- org.springframework:spring-core:jar:5.3.39:compile
[INFO] |  |  \- org.springframework:spring-jcl:jar:5.3.39:compile
[INFO] |  \- org.springframework:spring-beans:jar:5.3.39:compile
[INFO] +- cn.ijiami.base:ijiami-base-email:jar:2.6.11-SNAPSHOT:compile
[INFO] +- com.github.oshi:oshi-core:jar:6.4.0:compile
[INFO] +- net.java.dev.jna:jna:jar:5.12.0:compile
[INFO] +- net.java.dev.jna:jna-platform:jar:5.12.0:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-utils:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- cn.hutool:hutool-all:jar:5.8.36:compile
[INFO] |  \- com.googlecode.aviator:aviator:jar:5.4.1:compile
[INFO] +- net.sf.json-lib:json-lib:jar:jdk15:2.4:compile (optional) 
[INFO] |  +- commons-beanutils:commons-beanutils:jar:1.8.0:compile
[INFO] |  +- commons-collections:commons-collections:jar:3.2.1:compile (optional) 
[INFO] |  +- commons-lang:commons-lang:jar:2.5:compile
[INFO] |  +- commons-logging:commons-logging:jar:1.1.1:compile
[INFO] |  \- net.sf.ezmorph:ezmorph:jar:1.0.6:compile (optional) 
[INFO] +- org.projectlombok:lombok:jar:1.18.24:compile
[INFO] +- com.alibaba:fastjson:jar:1.2.83:compile
[INFO] +- org.apache.poi:poi:jar:4.1.2:compile
[INFO] |  +- org.apache.commons:commons-math3:jar:3.6.1:compile
[INFO] |  \- com.zaxxer:SparseBitSet:jar:1.2:compile
[INFO] +- org.apache.commons:commons-collections4:jar:4.1:compile
[INFO] +- org.apache.poi:poi-ooxml:jar:4.1.2:compile
[INFO] |  +- org.apache.poi:poi-ooxml-schemas:jar:4.1.2:compile
[INFO] |  +- org.apache.commons:commons-compress:jar:1.19:compile
[INFO] |  \- com.github.virtuald:curvesapi:jar:1.06:compile
[INFO] +- org.apache.xmlbeans:xmlbeans:jar:3.1.0:compile
[INFO] +- cn.ijiami.framework:ijiami-framework-mongodb:jar:2.6.8-SNAPSHOT:compile
[INFO] |  +- org.springframework.boot:spring-boot-starter-data-mongodb:jar:2.6.15:compile
[INFO] |  |  \- org.mongodb:mongodb-driver-sync:jar:4.4.2:compile
[INFO] |  |     \- org.mongodb:bson:jar:4.4.2:compile
[INFO] |  +- org.springframework.data:spring-data-mongodb:jar:3.3.10:compile
[INFO] |  |  \- org.mongodb:mongodb-driver-core:jar:4.4.2:compile
[INFO] |  \- com.github.pagehelper:pagehelper:jar:5.3.3:compile
[INFO] |     \- com.github.jsqlparser:jsqlparser:jar:4.5:compile
[INFO] +- com.google.guava:guava:jar:23.0:compile
[INFO] |  +- com.google.code.findbugs:jsr305:jar:1.3.9:compile
[INFO] |  +- com.google.errorprone:error_prone_annotations:jar:2.0.18:compile
[INFO] |  +- com.google.j2objc:j2objc-annotations:jar:1.1:compile
[INFO] |  \- org.codehaus.mojo:animal-sniffer-annotations:jar:1.14:compile
[INFO] +- org.springframework.cloud:spring-cloud-starter-openfeign:jar:2.1.3.RELEASE:compile
[INFO] |  +- org.springframework.cloud:spring-cloud-openfeign-core:jar:3.1.4:compile
[INFO] |  |  \- io.github.openfeign.form:feign-form-spring:jar:3.8.0:compile
[INFO] |  |     \- io.github.openfeign.form:feign-form:jar:3.8.0:compile
[INFO] |  +- org.springframework:spring-web:jar:5.3.39:compile
[INFO] |  +- io.github.openfeign:feign-core:jar:11.8:compile
[INFO] |  +- io.github.openfeign:feign-slf4j:jar:11.8:compile
[INFO] |  \- io.github.openfeign:feign-hystrix:jar:11.8:compile
[INFO] |     \- com.netflix.hystrix:hystrix-core:jar:1.5.18:compile
[INFO] |        +- com.netflix.archaius:archaius-core:jar:0.4.1:compile
[INFO] |        |  \- commons-configuration:commons-configuration:jar:1.8:compile
[INFO] |        \- io.reactivex:rxjava:jar:1.3.8:compile
[INFO] +- cn.ijiami.authentication:authentication-rest:jar:2.6.10-SNAPSHOT:compile
[INFO] |  +- cn.ijiami.authentication:authentication-service-impl:jar:2.6.10-SNAPSHOT:compile
[INFO] |  |  \- cn.ijiami.authentication:authentication-service-api:jar:2.6.10-SNAPSHOT:compile
[INFO] |  +- io.jsonwebtoken:jjwt:jar:0.9.0:compile
[INFO] |  \- org.aspectj:aspectjweaver:jar:1.9.7:compile
[INFO] +- mysql:mysql-connector-java:jar:8.0.16:compile
[INFO] +- com.dm:DmJdbcDriver18:jar:1.8:compile
[INFO] +- cn.ijiami.detection:privacy-android-server-client:jar:1.0-SNAPSHOT:compile
[INFO] |  \- cn.ijiami.detection:privacy-server-client-base:jar:1.0-SNAPSHOT:compile
[INFO] +- cn.ijiami.detection:privacy-ios-server-client:jar:1.0-SNAPSHOT:compile
[INFO] +- org.slf4j:slf4j-api:jar:1.7.36:compile
[INFO] \- org.apache.commons:commons-lang3:jar:3.12.0:compile
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Summary for privacy-detection-web 1.0-SNAPSHOT:
[INFO] 
[INFO] privacy-detection-web .............................. SUCCESS [  1.571 s]
[INFO] detection-service-api .............................. SUCCESS [  0.332 s]
[INFO] detection-core ..................................... SUCCESS [  0.111 s]
[INFO] detection-service-impl ............................. SUCCESS [  0.388 s]
[INFO] detection-rest ..................................... SUCCESS [  0.207 s]
[INFO] detection-web ...................................... SUCCESS [  0.240 s]
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time:  3.425 s
[INFO] Finished at: 2025-06-18T18:21:59+08:00
[INFO] ------------------------------------------------------------------------
