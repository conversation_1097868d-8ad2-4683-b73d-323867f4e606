<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>cn.ijiami.manager</groupId>
		<artifactId>ijiami-manager</artifactId>
		<version>2.6.11-SNAPSHOT</version>
	</parent>
	<groupId>cn.ijiami.detection</groupId>
	<artifactId>privacy-detection-web</artifactId>
	<version>1.0-SNAPSHOT</version>
	<packaging>pom</packaging>
	<name>privacy-detection-web</name>
	<description>个人信息安全检测系统V5.2</description>
	<properties>
		<mysql8.version>8.0.16</mysql8.version>
		<ijiami-organ.version>2.6.11-SNAPSHOT</ijiami-organ.version>
		<ijiami-message.version>2.6.11-SNAPSHOT</ijiami-message.version>
		<ijiami-message-core.version>2.6.8-SNAPSHOT</ijiami-message-core.version>
		<ijiami-base.version>2.6.11-SNAPSHOT</ijiami-base.version>
		<ijiami-framework.version>2.6.10-SNAPSHOT</ijiami-framework.version>
		<ijiami.base.version>2.6.11-SNAPSHOT</ijiami.base.version>
		<ijiami-manager-security.version>2.6.11-SNAPSHOT</ijiami-manager-security.version>
		<ijiami.manager.ai.version>2.6.11-SNAPSHOT</ijiami.manager.ai.version>
	</properties>
	<dependencies>

		<!-- ai功能 -->
		<dependency>
			<groupId>cn.ijiami.ai</groupId>
			<artifactId>ijiami-ai-rest</artifactId>
			<version>${ijiami.manager.ai.version}</version>
		</dependency>

		<dependency>
			<groupId>cn.ijiami.manager</groupId>
			<artifactId>ijiami-manager-service-api</artifactId>
		</dependency>
		
		<!-- 国际化组件 -->
		<dependency>
			<groupId>cn.ijiami.framework</groupId>
			<artifactId>ijiami-framework-i18n</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.ijiami.framework</groupId>
			<artifactId>ijiami-framework-file</artifactId>
		</dependency>
		
		<dependency>
		  <groupId>cn.ijiami.organ</groupId>
		  <artifactId>ijiami-organ-service-impl</artifactId>
		   <version>${ijiami-organ.version}</version>
		</dependency>
		
		<dependency>
		   <groupId>cn.ijiami.organ</groupId>
		   <artifactId>ijiami-organ-rest</artifactId>
		    <version>${ijiami-organ.version}</version>
		</dependency>
		<dependency>
			<artifactId>spring-data-commons</artifactId>
			<groupId>org.springframework.data</groupId>
		</dependency>
		
		<dependency>
			<groupId>cn.ijiami.base</groupId>
			<artifactId>ijiami-base-email</artifactId>
			<version>${ijiami.base.version}</version>
		</dependency>

		<!-- oshi依赖开始 -->
		<dependency>
			<groupId>com.github.oshi</groupId>
			<artifactId>oshi-core</artifactId>
			<version>6.4.0</version>
		</dependency>
		<dependency>
			<groupId>net.java.dev.jna</groupId>
			<artifactId>jna</artifactId>
			<version>5.12.0</version>
		</dependency>
		<dependency>
			<groupId>net.java.dev.jna</groupId>
			<artifactId>jna-platform</artifactId>
			<version>5.12.0</version>
		</dependency>
		<!-- oshi依赖结束 -->
		
		<dependency>
			<groupId>cn.ijiami.framework</groupId>
			<artifactId>ijiami-framework-utils</artifactId>
			<exclusions>
				<exclusion>
					<groupId>cn.ijiami</groupId>
  					<artifactId>IJMSecurityDetection_IOS</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- json -->
		<dependency>
			<groupId>net.sf.json-lib</groupId>
			<artifactId>json-lib</artifactId>
			<version>2.4</version>
			<classifier>jdk15</classifier>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.alibaba</groupId>
   			<artifactId>fastjson</artifactId>
		</dependency>

		<!-- apche poi -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>4.1.2</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.commons</groupId>
					<artifactId>commons-collections4</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-collections4</artifactId>
			<version>4.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>4.1.2</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.xmlbeans</groupId>
					<artifactId>xmlbeans</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.xmlbeans</groupId>
			<artifactId>xmlbeans</artifactId>
			<version>3.1.0</version>
		</dependency>
		
		<dependency>
		  <groupId>cn.ijiami.framework</groupId>
		  <artifactId>ijiami-framework-mongodb</artifactId>
		</dependency>
		
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>23.0</version>
		</dependency>
		
		<dependency>
		    <groupId>org.springframework.cloud</groupId>
		    <artifactId>spring-cloud-starter-openfeign</artifactId>
		    <version>2.1.3.RELEASE</version>
		</dependency>

		<dependency>
			<groupId>cn.ijiami.authentication</groupId>
			<artifactId>authentication-rest</artifactId>
			<version>2.6.10-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>${mysql8.version}</version>
		</dependency>
		<!-- 达梦数据库驱动 -->
		<dependency>
		    <groupId>com.dm</groupId>
		    <artifactId>DmJdbcDriver18</artifactId>
		    <version>1.8</version>
		</dependency>
		<!-- 微服务client 开始 -->
		<dependency>
			<groupId>cn.ijiami.privacy.applet</groupId>
			<artifactId>privacy-applet-server-client</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>cn.ijiami.privacy.harmony</groupId>
			<artifactId>privacy-harmony-server-client</artifactId>
			<version>1.0-SNAPSHOT</version>
		</dependency>
		<!-- 微服务client 结束 -->
	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<testFailureIgnore>true</testFailureIgnore>
				</configuration>
			</plugin>
		</plugins>
	</build>
	<modules>
		<module>detection-rest</module>
		<module>detection-service-api</module>
		<module>detection-service-impl</module>
		<module>detection-web</module>
		<module>detection-core</module>
	</modules>
</project>