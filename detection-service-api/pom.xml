<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.ijiami.detection</groupId>
        <artifactId>privacy-detection-web</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <artifactId>detection-service-api</artifactId>
    <version>1.0-SNAPSHOT</version>
    <name>detection-service-api</name>
    <description>检测服务接口</description>
    <!-- 推送api jar包上nesux私服 -->
     <!-- <distributionManagement>
        <repository>
            <id>releases</id>
            <name>Local Nexus Repository</name>
            <url>http://************:8081/nexus/content/repositories/releases</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>Local Nexus Repository</name>
            <url>http://************:8081/nexus/content/repositories/snapshots</url>
        </snapshotRepository>
    </distributionManagement> -->

    <dependencies>
        
        <dependency>
			<groupId>cn.ijiami.framework</groupId>
			<artifactId>ijiami-framework-mongodb</artifactId>
		</dependency>
		<dependency>
			<groupId>cn.ijiami.message</groupId>
			<artifactId>ijiami-message-service-api</artifactId>
			<version>${ijiami-message.version}</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>cn.ijiami.framework</groupId>
			<artifactId>ijiami-framework-openfeign</artifactId>
		</dependency>
        
        <dependency>
            <groupId>com.ijm.ios</groupId>
            <artifactId>RuntimeDetection</artifactId>
            <version>0.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ocpframework.ocp</groupId>
            <artifactId>ocp-sdk-detect-enginer</artifactId>
            <version>1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ocpframework</groupId>
                    <artifactId>ocp-boot-parent</artifactId>
                </exclusion>
                <exclusion>
					<artifactId>spring-data-commons</artifactId>
					<groupId>org.springframework.data</groupId>
				</exclusion>
				<exclusion>
					<groupId>io.swagger</groupId>
    				<artifactId>swagger-annotations</artifactId>
				</exclusion>
            </exclusions>
        </dependency>
        <!-- aop依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.google.code.gson/gson -->
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.9</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.2</version>
                <configuration>
                    <configurationFile>${basedir}/src/main/resources/generator/generatorConfig.xml</configurationFile>
                    <overwrite>true</overwrite>
                    <verbose>true</verbose>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>tk.mybatis</groupId>
                        <artifactId>mapper</artifactId>
                        <version>3.4.0</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>
</project>