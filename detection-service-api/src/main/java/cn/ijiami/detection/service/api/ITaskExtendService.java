package cn.ijiami.detection.service.api;

import cn.ijiami.detection.VO.TTaskExtendVO;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.TTaskQueue;
import cn.ijiami.detection.enums.PushStatusEnum;
/**
 * 检测任务扩展表
 * <AUTHOR>
 *
 */
public interface ITaskExtendService {

    void save(TTaskExtendVO dto);

    void pushData(Long taskId);
    
    int updatePushStatus(Long taskId, PushStatusEnum pushStatus);
    
    int updatePushStatus(Long taskId,PushStatusEnum pushStatus,String message);

    /**
     * 根据业务ID获取任务信息
     *
     * @param bussinessId 业务ID
     * @return
     */
    TTask getTaskByBussinessId(String bussinessId);
    
    TTaskExtendVO findTaskByTaskId(Long taskId);
    
    /**
     * 保存任务队列
     * @param queue
     * @return
     */
    TTaskQueue taskQueueSave(TTaskQueue queue);
    
    TTask getTaskByBussinessIdDynamicDesc(String bussinessId);
}
