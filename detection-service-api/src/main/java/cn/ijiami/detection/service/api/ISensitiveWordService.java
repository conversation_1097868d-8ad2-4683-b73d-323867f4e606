package cn.ijiami.detection.service.api;

import cn.ijiami.detection.DTO.compliance.ComplianceSensitiveGroupDto;
import cn.ijiami.detection.VO.compliance.ComplianceBaseVo;
import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.entity.TSensitiveWord;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;

import java.util.List;

/**
 * 敏感词接口类
 * 
 * <AUTHOR>
 *
 */
public interface ISensitiveWordService {
	void addSensitiveWord(TSensitiveWord sensitiveWord) throws IjiamiApplicationException;

	TSensitiveWord findById(Long id);

	int deleteSensitiveWordById(Long sensitiveWordId);

	PageInfo<TSensitiveWord> findSensitiveWordByWord(TSensitiveWord sensitiveWord) throws IjiamiApplicationException;

	/**
	 * 获取当前任务的个人信息敏感词列表
	 * @param complianceBaseVo
	 * @return
	 */
	List<ComplianceSensitiveGroupDto> querySensitive(ComplianceBaseVo complianceBaseVo);
}
