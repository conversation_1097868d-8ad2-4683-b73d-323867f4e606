package cn.ijiami.detection.service;

import cn.ijiami.framework.common.response.BaseResponse;
import java.util.List;

import com.github.pagehelper.PageInfo;

import cn.ijiami.base.common.user.IUser;
import cn.ijiami.detection.VO.CustomSdkLibraryVO;
import cn.ijiami.detection.VO.CustomSdkSave;
import cn.ijiami.detection.entity.TSdkLibraryLabel;
import cn.ijiami.detection.entity.TSdkLibraryType;
import cn.ijiami.detection.query.CustomSdkQuery;
import org.springframework.web.multipart.MultipartFile;

public interface ISdkService {

    void executeUpdateSdkInfo(String sdkServer);

    PageInfo<CustomSdkLibraryVO> findListByPage(IUser user, CustomSdkQuery customSdkQuery);

    void save(IUser user, CustomSdkSave customSdkSave);

    void delete(IUser user, Long id);
    List<TSdkLibraryLabel> findSdkBadLabelList();

    List<TSdkLibraryType> findSdkTypeList();

    /**
     * 导入sdk
     * @param 文件
     * @return object
     */
    BaseResponse<Object>  uploadFileImport(IUser user, MultipartFile file);

}
