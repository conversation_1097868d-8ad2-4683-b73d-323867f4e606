package cn.ijiami.detection.service.api;

import cn.ijiami.base.common.user.IUser;
import cn.ijiami.detection.VO.CustomLawsRegulationsItem;
import cn.ijiami.detection.VO.CustomLawsGroupVO;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.query.CustomLawsGroupPageQuery;
import cn.ijiami.detection.query.CustomLawsRegulationsSave;
import com.github.pagehelper.PageInfo;

import java.util.List;

public interface CustomLawsRegulationsService {

    void save(IUser user, CustomLawsRegulationsSave save, boolean isAdmin);

    PageInfo<CustomLawsGroupVO> findByPage(CustomLawsGroupPageQuery query);

    List<CustomLawsRegulationsItem> findItems(Long id, TerminalTypeEnum terminalTypeEnum);

    void delete(IUser user, Long id, boolean isAdmin);
}
