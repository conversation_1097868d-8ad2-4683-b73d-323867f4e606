package cn.ijiami.detection.service.spi;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import cn.ijiami.detection.VO.sdk.SdkInfoResponse;

@FeignClient(name = "SdkServer", url = "${ijiami.sdk.server:localhost}")
public interface SdkServerFeign {

    @GetMapping(
            value = {"/restSdk/exportSdk"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    SdkInfoResponse getSdkInfoList(@RequestParam("identification") String identification, @RequestParam("version") String version);

}
