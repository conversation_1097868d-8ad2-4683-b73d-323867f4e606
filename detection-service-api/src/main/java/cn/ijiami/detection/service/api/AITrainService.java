package cn.ijiami.detection.service.api;

import cn.ijiami.base.common.user.IUser;
import cn.ijiami.detection.query.AITrainImagePageQuery;
import cn.ijiami.detection.query.AITrainImageVo;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiFileNotFoundException;
import com.github.pagehelper.PageInfo;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;

public interface AITrainService {

    String uploadImage(IUser currentUser, MultipartFile data) throws IOException, IjiamiApplicationException;

    void saveOrUpdate(IUser currentUser, Long id, Integer category, String imageUrl, Long taskId) throws IOException, IjiamiApplicationException;

    PageInfo<AITrainImageVo> findImageByPage(AITrainImagePageQuery query);

    void deleteImage(IUser user, boolean isAdmin, Long imageId) throws IjiamiFileNotFoundException;

    Integer countNewImage();

    File exportImage(IUser user);

}
