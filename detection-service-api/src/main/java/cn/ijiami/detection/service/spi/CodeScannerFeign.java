package cn.ijiami.detection.service.spi;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import cn.ijiami.detection.VO.codescanner.CodeScannerResponse;
import cn.ijiami.detection.VO.codescanner.StartPermissionScanInstanceParams;
import cn.ijiami.detection.VO.codescanner.StaticFunctionAnalysisResult;

@FeignClient(name = "CodeScanner", url = "${ijiami.codeScanner.server}")
public interface CodeScannerFeign {

    /**
     * 启动静态函数分析
     * @param params
     * @return
     */
    @PostMapping(
            value = {"/code/scanner/startPermissionScanInstance"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    CodeScannerResponse<String> startPermissionScanInstance(@RequestBody StartPermissionScanInstanceParams params);


    /**
     * 查询结果
     * @param businessKey
     * @return
     */
    @GetMapping(
            value = {"/code/scanner/getPermissionScanInstanceResult/{id}"},
            consumes = {"application/json"},
            produces = {"application/json"}
    )
    CodeScannerResponse<StaticFunctionAnalysisResult> getPermissionScanInstanceResult(@PathVariable("id") String businessKey);

}
