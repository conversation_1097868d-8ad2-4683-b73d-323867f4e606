package cn.ijiami.detection.service.api;

import cn.ijiami.detection.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.enums.ShellStatusEnum;

/**
 * 脱壳进展推送服务
 *
 * <AUTHOR>
 * @date 2020/6/30 18:53
 **/
public interface IShellSendMessage {

    /**
     * 推送脱壳进展
     *
     * @param taskId
     * @param status
     */
    void sendShellMessage(Long taskId, ShellStatusEnum status);

    /**
     * 推送脱壳进展
     *
     * @param taskId
     * @param status
     */
    void sendShellMessage(Long taskId, Integer status);

    /**
     * 推送脱壳进展
     *
     * @param taskId
     * @param status
     * @param desc 自定义描述(回调成功时候，返回的脱壳后地址)
     */
    void sendShellMessage(Long taskId, int status, String desc);

    /**
     * 更新mongo中对应的数据状态
     * @param documentId 文档ID
     * @param status 状态
     * @param desc 描述
     */
    void updateMongoTask(String documentId, DynamicAutoStatusEnum status, String desc);

    void updateDynamicStatusByShellStatus(Long taskId, int status, String desc);
}
