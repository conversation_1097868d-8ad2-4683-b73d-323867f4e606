package cn.ijiami.detection.query;

import java.util.Date;
import java.util.List;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionCompareVO.java
 * @Description 检测任务对比item
 * @createTime 2024年01月12日 12:16:00
 */
@Data
public class DetectionCompareVO {

    @ApiModelProperty(value = "对比报告id")
    private Long reportId;

    @ApiModelProperty(value = "被对比的报告简介")
    private List<DetectionSummary> detectionList;

    @ApiModelProperty(value = "对比报告创建时间", example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;
}
