package cn.ijiami.detection.query.task;

import cn.ijiami.detection.enums.TerminalTypeEnum;

/**
 * <AUTHOR>
 * @date 2020/6/8
 */
public class QueryDetectResultForm {

    // 引擎版本

    private String versionEngine;
    // 文件md5
    private String md5;
    // 检测终端
    private TerminalTypeEnum terminalType;

    public String getVersionEngine() {
        return versionEngine;
    }

    public void setVersionEngine(String versionEngine) {
        this.versionEngine = versionEngine;
    }

    public String getMd5() {
        return md5;
    }

    public void setMd5(String md5) {
        this.md5 = md5;
    }

    public TerminalTypeEnum getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(TerminalTypeEnum terminalType) {
        this.terminalType = terminalType;
    }

    @Override
    public String toString() {
        return "QueryDetectResultForm{" + "versionEngine='" + versionEngine + '\'' + ", md5='" + md5 + '\'' + ", terminalType=" + terminalType + '}';
    }
}
