package cn.ijiami.detection.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsStatisticsQuery.java
 * @Description 应用资产统计查询
 * @createTime 2023年06月29日 16:42:00
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class AssetsStatisticsQuery {

    @ApiParam(value = "标签页通知id，用来区分给哪个标签页发消息")
    private String notificationId;

    @ApiModelProperty(value = "用户名", hidden = false, example = "abc")
    private String userName;

    @ApiModelProperty(value = "资产名", hidden = false, example = "abc")
    private String assetsName;

    @ApiModelProperty(value = "检测类型", hidden = false, example = "1 快速检测 2 深度检测")
    private Integer detectionType;

    @ApiModelProperty(value = "所属终端", hidden = false, example = "1")
    private Integer terminalType;

    @ApiModelProperty(value = "检测任务状态", hidden = false, example = "1 全部完成 2 全部中断 3 仅完成静态检测 4 完成静态+动态检测")
    private Integer detectionStatus;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date startDate;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date endDate;

}
