package cn.ijiami.detection.query;

import cn.ijiami.framework.mybatis.page.BaseEntity;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ChannelNotificationQuery extends BaseEntity {

    @ApiModelProperty(value = "名称查询")
    private String name;

    @ApiModelProperty(value = "排序参数",notes = "{\"bulletinAppTime\":1,\"createTime\":2}")
    private Map<String,Integer> keyMap;

    @ApiModelProperty(value = "页面数")
    private Integer page;

    @ApiModelProperty(value = "行数")
    private Integer rows;

    @ApiModelProperty(value = "通报问题")
    private List<String> problem;

    @ApiModelProperty(value = "应用类型")
    private List<Integer> channelType;

    @ApiModelProperty(value = "通报主管部门")
    private List<String> bulletinOrgan;
}
