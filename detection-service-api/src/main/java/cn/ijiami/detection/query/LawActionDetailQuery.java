package cn.ijiami.detection.query;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName LawActionDetailQuery.java
 * @Description 自动化法规检测结果的行为分页查询
 * @createTime 2023年11月28日 11:10:00
 */
@Data
@ApiModel(value = "LawActionDetailQuery", description = "自动化法规检测结果中的行为数据查询query对象")
@JsonIgnoreProperties(ignoreUnknown = true)
public class LawActionDetailQuery extends LawItemResultDetailQuery {

    @NotNull(message="缺少dataType")
    @ApiModelProperty("2 行为数据, 3 传输个人信息, 4 储存个人信息, 5 通讯传输")
    private Integer dataType;

    @ApiModelProperty(value = "行为权限(多个分号英文分隔;)")
    private String actionPermissionAliases;

    @ApiModelProperty(value = "行为Id(多个分号英文分隔,)")
    private String actionIds;

    @ApiModelProperty(value = "主体名称(多个分号英文分隔;)")
    private String executors;

}
