package cn.ijiami.detection.query;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.framework.mybatis.page.BaseEntity;

/**
 * 模版详情QUERY
 * 
 * <AUTHOR>
 *
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class TemplateDetectionItemQuery extends BaseEntity{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	/**
	 * 模版ID
	 */
	private Long templateId;

	/**
	 * 分类ID
	 */
	private Long detectionTypeId;



	public Long getTemplateId() {
		return templateId;
	}

	public void setTemplateId(Long templateId) {
		this.templateId = templateId;
	}

	public Long getDetectionTypeId() {
		return detectionTypeId;
	}

	public void setDetectionTypeId(Long detectionTypeId) {
		this.detectionTypeId = detectionTypeId;
	}

}
