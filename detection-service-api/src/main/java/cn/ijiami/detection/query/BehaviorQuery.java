package cn.ijiami.detection.query;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.framework.mybatis.page.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "BehaviorQuery", description = "行为信息query对象")
@JsonIgnoreProperties(ignoreUnknown = true)
public class BehaviorQuery extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "行为id",hidden = true)
    private Long actionId;

    @ApiModelProperty(value = "主体名称(多个分号英文分隔;)")
    private String executors;

    @ApiModelProperty(value = "行为阶段")
    private Integer behaviorStage;

    //v2.5新增的筛选条件内容
    @ApiModelProperty(value = "行为权限(多个分号英文分隔;)")
    private String actionPermissionAliases;

    //v2.5新增的筛选条件内容
    @ApiModelProperty(value = "应用行为id(多个分号英文分隔,)")
    private String actionIds;

    @ApiModelProperty(value = "行为名(多个分号英文分隔,)")
    private String actionNames;

    //v2.5新增的筛选条件内容
    @ApiModelProperty(value = "个人信息相关，是，否(多个逗号英文分隔,)")
    private String isPersonal;

    //v2.5新增筛选条件 1是APP  2是SDK
    @ApiModelProperty(value = "主体类型，APP,SDK(多个逗号英文分隔,)")
    private String executorType;

    //v2.5新增筛选条件 0是境内  1是境外
    @ApiModelProperty(value = "境内外,境内，境外(多个逗号英文分隔,)")
    private String outside;

    //v2.5新增筛选条件
    @ApiModelProperty(value = "协议类型(多个逗号英文分隔,)")
    private String protocol;

    //v2.5新增筛选条件
    @ApiModelProperty(value = "信息分类(多个逗号英文分隔,)")
    private String typeName;

    //v2.5新增筛选条件
    @ApiModelProperty(value = "个人信息(多个分号英文分隔;)")
    private String personalName;

    //v2.5新增排序内容
    @ApiModelProperty(value = "排序类型 1触发时间 2主体类型 3主体名称 4行为权限 5个人信息相关 6境内外 7协议类型 8信息分类 9个人信息")
    private Integer sortType;

    //v2.5新增排序
    @ApiModelProperty(value = "排序升降 1升序  2降序")
    private Integer sortOrder;

    @ApiModelProperty(value = "是否有cookie标识，1是,用于网络数据类型后面生成cookie图标")
    private Integer cookieMark;

    @ApiModelProperty(value = "是否明文传输，1是 0否 查询传输个人信息行为的时候可用")
    private Integer plaintextTransmission;

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Long getActionId() {
        return actionId;
    }

    public void setActionId(Long actionId) {
        this.actionId = actionId;
    }

    public String getExecutors() {
        return executors;
    }

    public void setExecutors(String executors) {
        this.executors = executors;
    }

    public Integer getBehaviorStage() {
        return behaviorStage;
    }

    public void setBehaviorStage(Integer behaviorStage) {
        this.behaviorStage = behaviorStage;
    }

    public String getActionPermissionAliases() {
        return actionPermissionAliases;
    }

    public void setActionPermissionAliases(String actionPermissionAliases) {
        this.actionPermissionAliases = actionPermissionAliases;
    }

    public String getIsPersonal() {
        return isPersonal;
    }

    public void setIsPersonal(String isPersonal) {
        this.isPersonal = isPersonal;
    }

    public String getExecutorType() {
        return executorType;
    }

    public void setExecutorType(String executorType) {
        this.executorType = executorType;
    }

    public String getOutside() {
        return outside;
    }

    public void setOutside(String outside) {
        this.outside = outside;
    }

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getPersonalName() {
        return personalName;
    }

    public void setPersonalName(String personalName) {
        this.personalName = personalName;
    }

    public Integer getSortType() {
        return sortType;
    }

    public void setSortType(Integer sortType) {
        this.sortType = sortType;
    }

    public Integer getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }

    public String getActionIds() {
        return actionIds;
    }

    public void setActionIds(String actionIds) {
        this.actionIds = actionIds;
    }

    public Integer getCookieMark() {
        return cookieMark;
    }

    public void setCookieMark(Integer cookieMark) {
        this.cookieMark = cookieMark;
    }

    public String getActionNames() {
        return actionNames;
    }

    public void setActionNames(String actionNames) {
        this.actionNames = actionNames;
    }

    public Integer getPlaintextTransmission() {
        return plaintextTransmission;
    }

    public void setPlaintextTransmission(Integer plaintextTransmission) {
        this.plaintextTransmission = plaintextTransmission;
    }
}
