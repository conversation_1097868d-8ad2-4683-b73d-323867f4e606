package cn.ijiami.detection.query.task;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import cn.ijiami.framework.mybatis.page.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskLogQuery.java
 * @Description 检测任务日志查询
 * @createTime 2021年12月22日 15:51:00
 */
@Data
@ApiModel(value = "TaskLogQuery", description = "任务日志查询")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TaskLogQuery extends BaseEntity {

    @ApiModelProperty(value = "任务id")
    private Long taskId;

}
