package cn.ijiami.detection.DTO.compliance;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
import java.util.List;

/**
 * 评估项DTO
 */
@Data
@Accessors(chain = true)
public class ComplianceAssessItemDto {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 评估项名称
     */
    private String name;

    /**
     * 评估项编码
     */
    private String code;

    /**
     * 评估分类ID
     */
    private Long assessCategoryId;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 创建人
     */
    private Long createUserId;

    /**
     * 修改人
     */
    private Long updateUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 是否满足
     */
    private Boolean isSatisfy;

    /**
     * 评估点列表
     */
    private List<ComplianceAssessPointDto> complianceAssessPointDtoList;
}
