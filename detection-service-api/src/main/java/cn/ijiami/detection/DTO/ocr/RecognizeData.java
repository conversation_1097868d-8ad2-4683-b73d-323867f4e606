package cn.ijiami.detection.DTO.ocr;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class RecognizeData {

    @JsonProperty(value = "save_path")
    private String savePath;
    private List<DataDTO> data;

    @NoArgsConstructor
    @Data
    public static class DataDTO {
        private String text;
        private Double confidence;

        @JsonProperty(value = "text_box_position")
        private List<List<Integer>> textBoxPosition;
    }

}
