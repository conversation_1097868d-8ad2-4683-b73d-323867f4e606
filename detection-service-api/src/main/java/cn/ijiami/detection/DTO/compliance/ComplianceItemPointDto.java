package cn.ijiami.detection.DTO.compliance;

import cn.ijiami.detection.entity.compliance.TcomplianceItemPointResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("检测点信息")
public class ComplianceItemPointDto implements Serializable {

    private static final long serialVersionUID = 3922057259022403631L;

    @ApiModelProperty(value = "检测点信息")
    private TcomplianceItemInfoPointDto itemPoint;

    @ApiModelProperty(value = "检测点结果信息")
    private TcomplianceItemPointResult itemPointResult;

}
