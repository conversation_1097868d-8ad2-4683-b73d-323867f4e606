package cn.ijiami.detection.DTO.compliance;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@ApiModel("敏感词分组")
public class ComplianceSensitiveGroupDto implements Serializable {

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "分组id")
    private Long groupId;

    @ApiModelProperty(value = "敏感词列表")
    private List<ComplianceSensitiveWordDto> complianceSensitiveWordDtos;

}
