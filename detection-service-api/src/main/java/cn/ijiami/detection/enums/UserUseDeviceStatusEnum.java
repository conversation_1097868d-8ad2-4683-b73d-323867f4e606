package cn.ijiami.detection.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum UserUseDeviceStatusEnum implements BaseValueEnum {

    /**
     * 预占设备
     */
    PREEMPTED(0, "预占设备"),
    /**
     * 使用中
     */
    USING(1, "使用中"),
    /**
     * 已释放
     */
    RELEASE(2, "已释放"),
    /**
     * 使用失败
     */
    FAILURE(3, "预占失败");

    private final int value;
    private final String name;

    UserUseDeviceStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static UserUseDeviceStatusEnum getItem(int value) {
        for (UserUseDeviceStatusEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }

}
