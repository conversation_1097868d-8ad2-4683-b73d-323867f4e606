package cn.ijiami.detection.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import cn.ijiami.framework.common.enums.BaseValueEnum;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum JurisdictionGradeEnum implements BaseValueEnum {
	LOW(1, "低"), MIDDLE(2, "中"), HIGH(3, "高");

	// 值
	private int value;

	// 名称
	private String name;

	private JurisdictionGradeEnum(int value, String name) {
		this.value = value;
		this.name = name;
	}

	@JsonCreator
	public static JurisdictionGradeEnum getItem(@JsonProperty("value") int value) {
		for (JurisdictionGradeEnum item : values()) {
			if (item.getValue() == value) {
				return item;
			}
		}
		return null;
	}

	public int getValue() {
		return value;
	}

	public void setValue(int value) {
		this.value = value;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Override
	public int itemValue() {
		return value;
	}

	@Override
	public String itemName() {
		return name;
	}

}
