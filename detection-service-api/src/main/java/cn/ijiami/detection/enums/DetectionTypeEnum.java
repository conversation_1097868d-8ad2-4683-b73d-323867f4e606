package cn.ijiami.detection.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * 检测类型枚举
 *
 * <AUTHOR>
 * @date 2020/9/21 18:52
 **/
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum DetectionTypeEnum implements BaseValueEnum {

    STATIC(1, "静态检测"), DYNAMIC(2, "快速检测"), MANUAL(3, "深度检测"), LAW(4, "法规检测");

    private int    value;
    private String name;

    private DetectionTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static DetectionTypeEnum getItem(int value) {
        for (DetectionTypeEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }

}
