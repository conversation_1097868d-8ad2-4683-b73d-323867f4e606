package cn.ijiami.detection.enums;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DynamicDetectFailedStageEnum.java
 * @Description 动态检测失败阶段
 * @createTime 2023年07月11日 09:57:00
 */
public enum DynamicDetectFailedStageEnum {

    DYNAMIC(1),
    MANUAL(2),
    LAW(3);

    private final Integer stage;

    DynamicDetectFailedStageEnum(Integer stage) {
        this.stage = stage;
    }

    public Integer getStage() {
        return stage;
    }
}
