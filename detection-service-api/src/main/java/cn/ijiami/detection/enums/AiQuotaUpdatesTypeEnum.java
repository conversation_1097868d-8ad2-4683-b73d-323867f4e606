package cn.ijiami.detection.enums;

import cn.ijiami.framework.common.enums.BaseValueEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.util.Objects;

@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum AiQuotaUpdatesTypeEnum implements BaseValueEnum {

    INIT(1, "初始化"),
    INCREASE(2, "新增"),
    DECREASE(3, "减少");

    private final int value;
    private final String desc;

    AiQuotaUpdatesTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    @JsonCreator
    public static AiQuotaUpdatesTypeEnum getItem(Integer value) {
        if (Objects.isNull(value)) {
            return null;
        }
        for (AiQuotaUpdatesTypeEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return desc;
    }
}
