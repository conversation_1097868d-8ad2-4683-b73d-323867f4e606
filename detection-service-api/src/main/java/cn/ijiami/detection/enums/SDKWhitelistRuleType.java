package cn.ijiami.detection.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;


@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum SDKWhitelistRuleType implements BaseValueEnum {

    PACKAGE_NAME(1, "包名匹配"), PATTERN(2, "正则匹配");

    // 值
    private int value;

    // 名称
    private String name;

    private SDKWhitelistRuleType(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static SDKWhitelistRuleType getItem(int value) {
        for (SDKWhitelistRuleType item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }

}
