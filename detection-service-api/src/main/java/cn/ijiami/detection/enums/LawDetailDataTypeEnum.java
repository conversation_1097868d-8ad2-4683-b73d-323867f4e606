package cn.ijiami.detection.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * 数据类型（1、截图、文本数据 2、行为数据 3、传输个人信息） 
 * <AUTHOR>
 *
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum LawDetailDataTypeEnum implements BaseValueEnum {

    TXT_IMAG(1, "截图、文本数据"),
    ACTION(2, "行为数据"),
    TRANSMISSION(3, "传输个人信息"),
    SHARED_PREFS(4, "储存个人信息"),
    OUTSIDE_ADDRESS(5, "3");

    private int    value;
    private String name;

    private LawDetailDataTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @JsonCreator
    public static LawDetailDataTypeEnum getItem(int value) {
        for (LawDetailDataTypeEnum item : values()) {
            if (item.getValue() == value) {
                return item;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }

}
