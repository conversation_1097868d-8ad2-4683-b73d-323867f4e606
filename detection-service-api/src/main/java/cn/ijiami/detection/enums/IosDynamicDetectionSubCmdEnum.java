package cn.ijiami.detection.enums;

public enum IosDynamicDetectionSubCmdEnum {
    DEPTH(1, "深度检测"),
    FAST(2, "快速检测");

    private Integer value;
    private String name;

    IosDynamicDetectionSubCmdEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
