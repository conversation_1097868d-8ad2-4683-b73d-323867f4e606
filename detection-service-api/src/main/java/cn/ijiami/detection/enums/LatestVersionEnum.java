package cn.ijiami.detection.enums;

/**
 * 是否最新版本枚举
 */
public enum LatestVersionEnum {

    NOT_LATEST_VERSION(0,"否"),
    LATEST_VERSION(1,"是");

    private int value;
    private String name;

    LatestVersionEnum(int value,String name){
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public static LatestVersionEnum getEunm(int value){
        for(LatestVersionEnum item : values()){
            if(item.getValue() == value){
                return item;
            }
        }
        return null;
    }
}
