package cn.ijiami.detection.enums;

/**
 * zheng<PERSON>tian
 * 业务状态代码
 */
public enum DetectionHttpStatusEnum {

    TOO_MANY_REQUEST(429, "请求次数太多");

    //值
    private int value;

    //名称
    private String name;


    private DetectionHttpStatusEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
