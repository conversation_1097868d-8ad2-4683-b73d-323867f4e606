package cn.ijiami.detection.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * 报告类型枚举
 * 
 * <AUTHOR>
 *
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum ReportTypeEnum implements BaseValueEnum {
	WORD(1, "word"), PDF(2, "pdf");
	private int value;
	private String name;

	private ReportTypeEnum(int value, String name) {
		this.name = name;
		this.value = value;
	}

	@JsonCreator
	public static ReportTypeEnum getItem(int value) {
		for (ReportTypeEnum item : values()) {
			if (item.getValue() == value) {
				return item;
			}
		}
		return null;
	}

	@Override
	public int itemValue() {
		return value;
	}

	@Override
	public String itemName() {
		return name;
	}

	public int getValue() {
		return value;
	}

	public String getName() {
		return name;
	}

	public void setValue(int value) {
		this.value = value;
	}

	public void setName(String name) {
		this.name = name;
	}

}
