package cn.ijiami.detection.enums;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.framework.common.enums.BaseValueEnum;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AppletActionTypeEnum.java
 * @Description 小程序日志类型
 * @createTime 2023年04月26日 10:26:00
 */
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum AppletActionTypeEnum implements BaseValueEnum {

    ACTION(1, "行为日志"),
    NET(2, "网络日志"),
    STORAGE(3, "存储日志");


    private final int value;
    private final String name;

    AppletActionTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    @Override
    public int itemValue() {
        return value;
    }

    @Override
    public String itemName() {
        return name;
    }
}
