package cn.ijiami.detection.VO.detection.privacy.deserialize;

import cn.ijiami.detection.bean.WechatAppletStaticDetectionResult;
import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName WechatAppletSdkDeserializer.java
 * @Description 小程序静态检测sdk解析
 * @createTime 2024年06月18日 16:52:00
 */
public class WechatAppletSdkDeserializer extends JsonDeserializer<WechatAppletStaticDetectionResult.Sdks> {
    @Override
    public WechatAppletStaticDetectionResult.Sdks deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {
        if (p.hasTextCharacters()) {
            String json = p.getText();
            if (StringUtils.startsWith(json, "{")) {
                return DeserializerUtils.toBean(p.getText(), new TypeReference<WechatAppletStaticDetectionResult.Sdks>() {});
            } else {
                return null;
            }
        } else {
            try {
                return p.readValueAs(new TypeReference<WechatAppletStaticDetectionResult.Sdks>() {});
            } catch (Exception e) {
                return null;
            }
        }
    }
}

