package cn.ijiami.detection.VO;


import java.util.List;

import cn.ijiami.detection.enums.PrivacyStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "StaticFunctionBehaviorVO", description = "函数分析行为数据")
public class StaticFunctionBehaviorVO {

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    /**
     * 行为id
     */
    @ApiModelProperty(value = "行为id")
    private Long actionId;

    /**
     * 行为名称
     */
    @ApiModelProperty(value = "行为名称")
    private String actionName;

    /**
     * 行为id
     */
    @ApiModelProperty(value = "函数数量")
    private Integer functionCount;


    /**
     * 行为id
     */
    @ApiModelProperty(value = "个人信息相关")
    private PrivacyStatusEnum isPrivacy;
    
    @ApiModelProperty(value = "函数详情列表")
    private List<StaticFunctionDetailVO> staticFunctionDetailVOList;


}
