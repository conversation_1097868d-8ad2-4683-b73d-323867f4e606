package cn.ijiami.detection.VO;

/**
 * token转换类
 */
public class TokenVO {

    /**
     * 认证token
     */
    private String accessToken;

    /**
     * 刷新token
     */
    private String refreshToken;

    /**
     * 剩余时间（单位秒）
     */
    private long expiresIn;

    private String tokenType;

    /**
     * 认证范围
     */
    private String scope;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 租户
     */
    private String tenantId;

    private String tokenDataStr;

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTokenDataStr() {
        return tokenDataStr;
    }

    public void setTokenDataStr(String tokenDataStr) {
        this.tokenDataStr = tokenDataStr;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public long getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(long expiresIn) {
        this.expiresIn = expiresIn;
    }

    public String getTokenType() {
        return tokenType;
    }

    public void setTokenType(String tokenType) {
        this.tokenType = tokenType;
    }

    public String getScope() {
        return scope;
    }

    public void setScope(String scope) {
        this.scope = scope;
    }
}
