package cn.ijiami.detection.VO;

import java.io.Serializable;

import cn.ijiami.detection.enums.PermissionSensitiveTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value = "权限信息")
public class PermissionVO implements Comparable<PermissionVO>, Serializable {

    private static final long serialVersionUID = -8840109648073314423L;
    /**
     * 权限名称（英文）
     */
    @ApiModelProperty(value = "权限名称")
    private String name;

    @ApiModelProperty(value = "权限别名")
    private String aliasName;

    /**
     * 备注（权限名 中文）
     */
    @ApiModelProperty(value = "权限名")
    private String remark;

    /**
     * 权限危害
     */
    @ApiModelProperty(value = "权限危害")
    private String harm;

    /**
     * 权限等级
     */
    @ApiModelProperty(value = "权限等级")
    private Integer grade;

    @ApiModelProperty(value = "敏感等级 1敏感  0正常")
    private Integer type;

    @ApiModelProperty(value = "是否与个人信息相关")
    private Integer isPrivacy;

    @ApiModelProperty(value = "分组名称")
    private String groupName;

    @ApiModelProperty(value = "分组code")
    private String groupCode;

    @ApiModelProperty(value = "分组名")
    private String groupRemark;

    @ApiModelProperty(value = "是否被使用")
    private Boolean used = false;

    @ApiModelProperty(value = "使用次数")
    private Integer useCount = 0;

    private Integer appCount = 0;

    private Integer sdkCount = 0;

    @ApiModelProperty(value = "主体类型")
    private Integer executorType = 1;
    
    @ApiModelProperty(value = "权限等级")
    private String protectionLevel;

    @ApiModelProperty(value = "sdk是否与个人信息相关")
    private Integer sdkIsPrivacy;

    @ApiModelProperty(value = "app是否与个人信息相关")
    private Integer appIsPrivacy;

    @ApiModelProperty(value = "1 申明权限 ,2敏感权限  3尝试使用未申明权限 3越权权限")
    private Integer permissionType;

    @ApiModelProperty(value = "权限对应接口")
    private String actionApi;

    private String permissionCode;

    //是否声明权限
    private Boolean statementPermission;
    //是否超范围申请
    private Boolean excessPermission;
    //4个行为阶段的数据统计
    private Integer appB1Count;
    private Integer sdkB1Count;
    private Integer appB2Count;
    private Integer sdkB2Count;
    private Integer appB3Count;
    private Integer sdkB3Count;
    private Integer appB4Count;
    private Integer sdkB4Count;
    private Integer appB5Count;
    private Integer sdkB5Count;

    @ApiModelProperty(value = "涉及个人信息")
    private String personalInfo;

    @ApiModelProperty(value = "建议")
    private String suggest;

    @ApiModelProperty(value = "序号")
    private Integer serialNumber;
    
    public Integer getAppB1Count() {
		return appB1Count;
	}

	public void setAppB1Count(Integer appB1Count) {
		this.appB1Count = appB1Count;
	}

	public Integer getSdkB1Count() {
		return sdkB1Count;
	}

	public void setSdkB1Count(Integer sdkB1Count) {
		this.sdkB1Count = sdkB1Count;
	}

	public Integer getAppB2Count() {
		return appB2Count;
	}

	public void setAppB2Count(Integer appB2Count) {
		this.appB2Count = appB2Count;
	}

	public Integer getSdkB2Count() {
		return sdkB2Count;
	}

	public void setSdkB2Count(Integer sdkB2Count) {
		this.sdkB2Count = sdkB2Count;
	}

	public Integer getAppB3Count() {
		return appB3Count;
	}

	public void setAppB3Count(Integer appB3Count) {
		this.appB3Count = appB3Count;
	}

	public Integer getSdkB3Count() {
		return sdkB3Count;
	}

	public void setSdkB3Count(Integer sdkB3Count) {
		this.sdkB3Count = sdkB3Count;
	}

	public Integer getAppB4Count() {
		return appB4Count;
	}

	public void setAppB4Count(Integer appB4Count) {
		this.appB4Count = appB4Count;
	}

	public Integer getSdkB4Count() {
		return sdkB4Count;
	}

	public void setSdkB4Count(Integer sdkB4Count) {
		this.sdkB4Count = sdkB4Count;
	}

	public Boolean getStatementPermission() {
		return statementPermission;
	}

	public void setStatementPermission(Boolean statementPermission) {
		this.statementPermission = statementPermission;
	}

	public Boolean getExcessPermission() {
		return excessPermission;
	}

	public void setExcessPermission(Boolean excessPermission) {
		this.excessPermission = excessPermission;
	}

	public String getPermissionCode() {
		return permissionCode;
	}

	public void setPermissionCode(String permissionCode) {
		this.permissionCode = permissionCode;
	}

	public Integer getPermissionType() {
		return permissionType;
	}

	public void setPermissionType(Integer permissionType) {
		this.permissionType = permissionType;
	}

	public void setGrade(Integer grade) {
		this.grade = grade;
	}

	public Integer getSdkIsPrivacy() {
		return sdkIsPrivacy;
	}

	public void setSdkIsPrivacy(Integer sdkIsPrivacy) {
		this.sdkIsPrivacy = sdkIsPrivacy;
	}

	public Integer getAppIsPrivacy() {
		return appIsPrivacy;
	}

	public void setAppIsPrivacy(Integer appIsPrivacy) {
		this.appIsPrivacy = appIsPrivacy;
	}

	public String getProtectionLevel() {
		return protectionLevel;
	}

	public void setProtectionLevel(String protectionLevel) {
		this.protectionLevel = protectionLevel;
	}

	public String getName() {
        return name;
    }

    public String getRemark() {
        return remark;
    }

    public int getGrade() {
        return grade;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setGrade(int grade) {
        this.grade = grade;
    }

    public String getHarm() {
        return harm;
    }

    public void setHarm(String harm) {
        this.harm = harm;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public int compareTo(PermissionVO o) {
        if (o.getType().compareTo(this.getType()) == PermissionSensitiveTypeEnum.NORMAL_TYPE.value) {
            if (o.getIsPrivacy().compareTo(this.getIsPrivacy()) == 0) {
                if (o.getUseCount() != null && this.getUseCount() != null) {
                    return o.getUseCount().compareTo(this.getUseCount());
                }
            }
            return o.getIsPrivacy().compareTo(this.getIsPrivacy());
        }
        return o.getType().compareTo(this.getType());
    }

    public Integer getIsPrivacy() {
        return isPrivacy;
    }

    public void setIsPrivacy(Integer isPrivacy) {
        this.isPrivacy = isPrivacy;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getGroupRemark() {
        return groupRemark;
    }

    public void setGroupRemark(String groupRemark) {
        this.groupRemark = groupRemark;
    }

    public Boolean getUsed() {
        return used;
    }

    public void setUsed(Boolean used) {
        this.used = used;
    }

    public Integer getUseCount() {
        return useCount;
    }

    public void setUseCount(Integer useCount) {
        this.useCount = useCount;
    }

    public Integer getExecutorType() {
        return executorType;
    }

    public void setExecutorType(Integer executorType) {
        this.executorType = executorType;
    }

    public String getAliasName() {
        return aliasName;
    }

    public void setAliasName(String aliasName) {
        this.aliasName = aliasName;
    }

    public Integer getAppCount() {
        return appCount;
    }

    public void setAppCount(Integer appCount) {
        this.appCount = appCount;
    }

    public Integer getSdkCount() {
        return sdkCount;
    }

    public void setSdkCount(Integer sdkCount) {
        this.sdkCount = sdkCount;
    }

    public Integer getAppB5Count() {
        return appB5Count;
    }

    public void setAppB5Count(Integer appB5Count) {
        this.appB5Count = appB5Count;
    }

    public Integer getSdkB5Count() {
        return sdkB5Count;
    }

    public void setSdkB5Count(Integer sdkB5Count) {
        this.sdkB5Count = sdkB5Count;
    }

    public String getActionApi() {
        return actionApi;
    }

    public void setActionApi(String actionApi) {
        this.actionApi = actionApi;
    }

    public String getPersonalInfo() {
        return personalInfo;
    }

    public void setPersonalInfo(String personalInfo) {
        this.personalInfo = personalInfo;
    }

    public String getSuggest() {
        return suggest;
    }

    public void setSuggest(String suggest) {
        this.suggest = suggest;
    }

    public Integer getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(Integer serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getGroupCode() {
        return groupCode;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }
}
