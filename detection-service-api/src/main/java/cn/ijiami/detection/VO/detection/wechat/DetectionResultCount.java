package cn.ijiami.detection.VO.detection.wechat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * <AUTHOR>
 * @since 2019/3/6 17:30
 */
@ApiModel
public class DetectionResultCount {

    @ApiModelProperty(value = "检测数量")
    private int detectCount;

    @ApiModelProperty(value = "高")
    private int detectHigh;

    @ApiModelProperty(value = "中")
    private int detectMid;

    @ApiModelProperty(value = "低")
    private int detectLow;

    @ApiModelProperty(value = "危险数量")
    private int dangerCount;

    @ApiModelProperty(value = "高")
    private int dangerHigh;

    @ApiModelProperty(value = "中")
    private int dangerMid;

    @ApiModelProperty(value = "低")
    private int dangerLow;

    public int getDetectCount() {
        return detectCount;
    }

    public void setDetectCount(int detectCount) {
        this.detectCount = detectCount;
    }

    public int getDetectHigh() {
        return detectHigh;
    }

    public void setDetectHigh(int detectHigh) {
        this.detectHigh = detectHigh;
    }

    public int getDetectMid() {
        return detectMid;
    }

    public void setDetectMid(int detectMid) {
        this.detectMid = detectMid;
    }

    public int getDetectLow() {
        return detectLow;
    }

    public void setDetectLow(int detectLow) {
        this.detectLow = detectLow;
    }

    public int getDangerCount() {
        return dangerCount;
    }

    public void setDangerCount(int dangerCount) {
        this.dangerCount = dangerCount;
    }

    public int getDangerHigh() {
        return dangerHigh;
    }

    public void setDangerHigh(int dangerHigh) {
        this.dangerHigh = dangerHigh;
    }

    public int getDangerMid() {
        return dangerMid;
    }

    public void setDangerMid(int dangerMid) {
        this.dangerMid = dangerMid;
    }

    public int getDangerLow() {
        return dangerLow;
    }

    public void setDangerLow(int dangerLow) {
        this.dangerLow = dangerLow;
    }
}
