package cn.ijiami.detection.VO;

import cn.ijiami.detection.enums.AiUsageLimitTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionConfigItem.java
 * @Description 页面查询
 * @createTime 2023年06月21日 16:26:00
 */
@Data
public class DetectionConfigItem {

    private Long id;

    private String userName;

    private String roleName;

    private Long userId;

    private Long roleId;

    private Integer androidDevicesLimit;

    private Integer iosDevicesLimit;

    /**
     * 角色生效结束日期
     */
    private Date effectiveEndDate;

    /**
     * 创建日期
     */
    private Date createTime;

    private String description;
    
    /**
     * 静态检测自定义JobIds
     */
    private String staticJobIds;
    
    /**
     * 动态检测自动化JobIds
     */
    private String dynamicJobIds;
    
    private String  androidDeviceIps;

    private Integer harmonyDevicesLimit;

    private Integer aiUsageLimit;

    private Integer aiUsageLimitType;

}
