package cn.ijiami.detection.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AndroidShakeValueLog.java
 * @Description 安卓摇一摇数据日志
 * @createTime 2023年10月18日 18:10:00
 */
@Data
public class AndroidSensorLog {

    @ApiModelProperty(value = "机器人手臂传感器数据")
    private AndroidShakeData robot;

    @ApiModelProperty(value = "手机传感器数据")
    private AndroidShakeData phoneSensor;

    @ApiModelProperty(value = "日志接收时间")
    private Long actionTime;

    @Data
    public static class AndroidShakeData {
        private String angY;
        private String angX;
        private String angZ;

        private String accY;
        private String accX;
        private String accZ;

        private String maxAng;
        private String accS;

        private String dataTime;
    }
}