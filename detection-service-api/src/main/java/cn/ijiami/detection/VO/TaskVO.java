package cn.ijiami.detection.VO;
/**
 * 检测任务vo
 * <AUTHOR>
 *
 */

import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value = "TaskVO", description = "检测任务vo")
public class TaskVO implements Serializable {

	private static final long serialVersionUID = 3838557980677916466L;

	@ApiModelProperty(value = "深度检测数量")
	private Integer deepDetectionCount;

	@ApiModelProperty(value = "快速检测数量")
	private Integer fastDetectionCount;

	@ApiModelProperty(value = "AI检测数量")
	private Integer aiDetectionCount;

	@ApiModelProperty(value = "专家检测数量")
	private Integer expertCount;

	@ApiModelProperty(value = "专项检测数量")
	private Integer specialCount;

	@ApiModelProperty(value = "任务列表")
	private PageInfo<TaskDetailVO> pageInfo = new PageInfo<>();

	public PageInfo<TaskDetailVO> getPageInfo() {
		return pageInfo;
	}

	public void setPageInfo(PageInfo<TaskDetailVO> pageInfo) {
		this.pageInfo = pageInfo;
	}

	public Integer getAiDetectionCount() {
		return aiDetectionCount;
	}

	public void setAiDetectionCount(Integer aiDetectionCount) {
		this.aiDetectionCount = aiDetectionCount;
	}

	public Integer getDeepDetectionCount() {
		return deepDetectionCount;
	}

	public void setDeepDetectionCount(Integer deepDetectionCount) {
		this.deepDetectionCount = deepDetectionCount;
	}

	public Integer getFastDetectionCount() {
		return fastDetectionCount;
	}

	public void setFastDetectionCount(Integer fastDetectionCount) {
		this.fastDetectionCount = fastDetectionCount;
	}

	public Integer getExpertCount() {
		return expertCount;
	}

	public void setExpertCount(Integer expertCount) {
		this.expertCount = expertCount;
	}

	public Integer getSpecialCount() {
		return specialCount;
	}

	public void setSpecialCount(Integer specialCount) {
		this.specialCount = specialCount;
	}
}
