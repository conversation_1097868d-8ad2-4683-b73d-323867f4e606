package cn.ijiami.detection.VO;

import java.util.List;

import cn.ijiami.detection.enums.OutsideEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IpAddressCheckResultVO.java
 * @Description
 * @createTime 2021年11月19日 14:45:00
 */
@Data
public class IpAddressCheckResultVO {

    @ApiModelProperty(value = "通讯行为id")
    private Long outsideId;

    @ApiModelProperty(value = "IP")
    private String ip;

    @ApiModelProperty(value = "域名")
    private String host;

    @ApiModelProperty(value = "端口")
    private String port;

    @ApiModelProperty(value = "检测状态")
    private Integer status;

    @ApiModelProperty(value = "路由节点列表")
    private List<IpAddressCheckRoute> routeList;

    @Data
    public static class IpAddressCheckRoute {

        @ApiModelProperty(value = "IP")
        private String ip;

        @ApiModelProperty(value = "是否为境外IP")
        private OutsideEnum outside;

        @ApiModelProperty(value = "归属地")
        private String address;

    }

}
