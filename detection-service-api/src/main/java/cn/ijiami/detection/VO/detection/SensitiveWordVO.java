package cn.ijiami.detection.VO.detection;

import java.util.ArrayList;
import java.util.List;

/**
 * 敏感词汇VO
 * 
 * <AUTHOR>
 *
 */
public class SensitiveWordVO {

	// 敏感词
	private String name;

	// 出现次数
	private Integer count;

	// 词汇类型
	private String typeName;

	// 出现位置
	private List<SensitiveWordFilePathVO> filePathList = new ArrayList<SensitiveWordFilePathVO>();

	public String getName() {
		return name;
	}

	public Integer getCount() {
		return count;
	}

	public String getTypeName() {
		return typeName;
	}

	public List<SensitiveWordFilePathVO> getFilePathList() {
		return filePathList;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setCount(Integer count) {
		this.count = count;
	}

	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	public void setFilePathList(List<SensitiveWordFilePathVO> filePathList) {
		this.filePathList = filePathList;
	}
}
