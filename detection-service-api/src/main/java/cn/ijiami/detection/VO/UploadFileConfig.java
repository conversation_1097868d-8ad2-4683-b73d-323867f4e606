package cn.ijiami.detection.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UploadFileConfig.java
 * @Description 上传文件配置
 * @createTime 2021年12月30日 15:16:00
 */
@Data
@ApiModel(value = "上传文件配置")
public class UploadFileConfig {

    @ApiModelProperty(value = "单个上传文件大小，单位MB")
    private long maximumFileSize;

    @ApiModelProperty(value = "分片文件大小，单位MB")
    private long chunkFileSize;

    @ApiModelProperty(value = "添加资产的批量上传个数限制")
    private int multipleUploadLimit;

    @ApiModelProperty(value = "大批量上传的个数限制")
    private int bulkUploadLimit;
}
