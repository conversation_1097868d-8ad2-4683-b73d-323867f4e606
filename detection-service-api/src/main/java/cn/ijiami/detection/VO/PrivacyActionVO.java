package cn.ijiami.detection.VO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019-06-03 14:40
 */
@Data
@ApiModel(value = "行为信息")
public class PrivacyActionVO {

    @ApiModelProperty(value = "行为id")
    private Long actionId;

    @ApiModelProperty(value = "行为名称")
    private String actionName;

    @ApiModelProperty(value = "行为函数")
    private String actionFunction;

    @ApiModelProperty(value = "行为函数存储位置")
    private String functionLocation;

    @ApiModelProperty(value = "敏感等级")
    private String safeLevel;

    @ApiModelProperty(value = "权限名称")
    private String permissionName;

    @ApiModelProperty(value = "图片文件key")
    private String imgs;

    @ApiModelProperty(value = "图片信息")
    private List<RiskImageVo> base64Images;
}
