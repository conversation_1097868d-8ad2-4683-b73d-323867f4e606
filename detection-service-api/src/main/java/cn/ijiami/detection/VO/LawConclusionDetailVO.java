package cn.ijiami.detection.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName LawConclusionActionVO.java
 * @Description 返回给前端的法规结论行为
 * @createTime 2022年08月12日 16:58:00
 */
@Data
public class LawConclusionDetailVO {

    @ApiModelProperty(value = "结论开头")
    private String conclusionBegin;

    @ApiModelProperty(value = "结论结尾")
    private String conclusionEnd;

    @ApiModelProperty(value = "要展示的行为列表")
    private List<LawConclusionActionVO> actionList;

}
