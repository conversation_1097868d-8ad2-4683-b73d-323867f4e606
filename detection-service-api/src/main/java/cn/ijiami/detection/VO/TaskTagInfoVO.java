package cn.ijiami.detection.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Id;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskTagInfoVO.java
 * @Description 返回给前端的任务标签
 * @createTime 2022年08月09日 10:28:00
 */
@Data
public class TaskTagInfoVO {

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "应用名")
    private String assetsName;

    @ApiModelProperty(value = "版本号")
    private String version;

    @ApiModelProperty(value = "问题描述")
    private String description;

    @ApiModelProperty(value = "问题标签")
    private List<TaskTagVO> tagList;

}
