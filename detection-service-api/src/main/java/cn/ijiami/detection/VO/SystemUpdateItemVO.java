package cn.ijiami.detection.VO;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SystemUpdateVO.java
 * @Description 系统或工具更新
 * @createTime 2024年11月18日 15:05:00
 */
@Data
public class SystemUpdateItemVO {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "版本号")
    private String version;

    @ApiModelProperty(value = "版本信息")
    private String releaseNotes;

    @ApiModelProperty(value = "类型 1 系统 2 工具")
    private Integer type;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date updateTime;


}
