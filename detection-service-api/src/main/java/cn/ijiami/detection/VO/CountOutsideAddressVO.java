package cn.ijiami.detection.VO;

import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/4 14:16
 */
@Data
public class CountOutsideAddressVO implements Serializable {

    private static final long serialVersionUID = -7728335815723278377L;
    private List<TPrivacyOutsideAddress> outsideAddresses = new ArrayList<>();

    private Integer allSum = 0;

    private Integer outsideSum = 0;

    private String pieChartImageBase64 = "";
}
