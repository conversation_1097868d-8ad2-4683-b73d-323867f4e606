package cn.ijiami.detection.VO.detection.privacy.deserialize;

import cn.ijiami.detection.VO.detection.privacy.dto.AppletActionBO;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DataDeserializer.java
 * @createTime 2023年04月03日 15:11:00
 */
public class AppletDataDeserializer extends JsonDeserializer<AppletActionBO> {

    @Override
    public AppletActionBO deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JsonProcessingException {
        if (p.hasTextCharacters()) {
            return DeserializerUtils.toBean(p.getText(), AppletActionBO.class);
        } else {
            return p.readValueAs(AppletActionBO.class);
        }
    }
}
