package cn.ijiami.detection.VO.expert;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@Accessors(chain = true)
public class NetWorkBehaviorWebInfo implements Serializable {

    private static final long serialVersionUID = 8493042773121279261L;

    @ApiModelProperty(value = "id")
    @Column(name = "id")
    private Long id;

    @ApiModelProperty(value = "IP")
    @Column(name = "ip")
    private String ip;

    @ApiModelProperty(value = "域名")
    @Column(name = "host")
    private String host;

    @ApiModelProperty(value = "Cookie")
    @Column(name = "cookie")
    private String cookie;

    @ApiModelProperty(value = "协议")
    @Column(name = "protocol")
    private String protocol;

    @ApiModelProperty(value = "端口")
    @Column(name = "port")
    private String port;

    @ApiModelProperty(value = "归属地")
    @Column(name = "address")
    private String address;

    @ApiModelProperty(value = "url")
    @Column(name = "url")
    private String url;

    @ApiModelProperty(value = "是否为境外IP")
    @Column(name = "outside")
    private Integer outside;

    @ApiModelProperty(value = "地理位置")
    @Column(name = "attributively")
    private String attributively;

    @ApiModelProperty(value = "调用栈")
    @Column(name = "stack_info")
    private String stackInfo;

    @ApiModelProperty(value = "触发时间")
    @Column(name = "action_time")
    private String actionTime;

    @ApiModelProperty(value = "详细数据")
    @Column(name = "details_data")
    private String detailsData;

    @ApiModelProperty(value = "响应数据")
    @Column(name = "response_content")
    private String responseContent;

    @ApiModelProperty(value = "响应code")
    @Column(name = "response_code")
    private String responseCode;


}
