package cn.ijiami.detection.VO;

import cn.ijiami.detection.entity.TPermission;

public class UserPermissionVO extends TPermission {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 铭感权限ID
	 */
	private Long permissionId;

	/**
	 * 特征码
	 */
	private String signaTure;
	
	public Long getPermissionId() {
		return permissionId;
	}

	public void setPermissionId(Long permissionId) {
		this.permissionId = permissionId;
	}

	public String getSignaTure() {
		return signaTure;
	}

	public void setSignaTure(String signaTure) {
		this.signaTure = signaTure;
	}

}
