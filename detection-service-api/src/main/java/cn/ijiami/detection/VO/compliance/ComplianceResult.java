package cn.ijiami.detection.VO.compliance;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/26
 */
@Data
@Accessors(chain = true)
public class ComplianceResult implements Serializable {

    private static final long serialVersionUID = 3285156616788498455L;

    /**
     * 1 满足 2不满足
     * 1 是  2否
     */
    private int result = 1;

    /**
     * 结论
     */
    private String suggestText;

    /**
     * 整改建议
     */
    private String rectifySuggest;


    private List<String> screenImg;
}
