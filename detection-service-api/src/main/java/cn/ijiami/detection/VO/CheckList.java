package cn.ijiami.detection.VO;

import io.swagger.annotations.ApiModelProperty;

import java.util.List;

import static cn.ijiami.detection.constant.PinfoConstant.DETAILS_EMPTY;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName CheckList.java
 * @Description 第三方共享清单和个人信息收集清单
 * @createTime 2023年06月14日 10:58:00
 */
public class CheckList {

    private String fullText;
    private List<Row> rowList;

    public String getFullText() {
        return fullText;
    }

    public void setFullText(String fullText) {
        this.fullText = fullText;
    }

    public List<Row> getRowList() {
        return rowList;
    }

    public void setRowList(List<Row> rowList) {
        this.rowList = rowList;
    }

    public static class Row {

        @ApiModelProperty(value = "sdk名称")
        private String sdkName;
        @ApiModelProperty(value = "sdk包名")
        private String sdkPackage = DETAILS_EMPTY;
        @ApiModelProperty(value = "使用权限")
        private String permission;
        @ApiModelProperty(value = "使用目的")
        private String purpose;
        @ApiModelProperty(value = "公司名称")
        private String company;
        @ApiModelProperty(value = "隐私政策链接")
        private String privacyPolicyLink;

        public String getSdkName() {
            return sdkName;
        }

        public void setSdkName(String sdkName) {
            this.sdkName = sdkName;
        }

        public String getPermission() {
            return permission;
        }

        public void setPermission(String permission) {
            this.permission = permission;
        }

        public String getPurpose() {
            return purpose;
        }

        public void setPurpose(String purpose) {
            this.purpose = purpose;
        }

        public String getCompany() {
            return company;
        }

        public void setCompany(String company) {
            this.company = company;
        }

        public String getPrivacyPolicyLink() {
            return privacyPolicyLink;
        }

        public void setPrivacyPolicyLink(String privacyPolicyLink) {
            this.privacyPolicyLink = privacyPolicyLink;
        }

        public String privacyPolicyFragment() {
            return "SDK名称：" + sdkName + "\n" +
                    "权限行为：" + permission + "\n" +
                    "使用目的：" + purpose + "\n" +
                    "公司名称：" + company + "\n" +
                    "隐私政策：" + privacyPolicyLink + "\n";
        }

        public String getSdkPackage() {
            return sdkPackage;
        }

        public void setSdkPackage(String sdkPackage) {
            this.sdkPackage = sdkPackage;
        }
    }

}
