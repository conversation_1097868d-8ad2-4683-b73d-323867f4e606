package cn.ijiami.detection.VO.ccrc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/10/28 09:53
 */
@Data
@ApiModel(value = "CCRC上传apk实体类")
public class CCRCDTO implements Serializable {
    private static final long serialVersionUID = 4319631254498220023L;

    @ApiModelProperty(value = "下载地址")
    @NotBlank(message = "下载地址不能为空")
    private String downloadUrl;

    @ApiModelProperty(value = "APP业务功能分类名称，半角逗号分隔")
    private String categories;

    @ApiModelProperty(value = "回调地址")
    private String callbackUrl;

    @NotNull(message = "apkId不能为空")
    @ApiModelProperty(value = "apkId")
    private Long apkId;
    
    @ApiModelProperty(value = "模板ID")
    private Long templateId;
    @ApiModelProperty(value = "包名")
    private String packageName;
    @ApiModelProperty(value = "应用名称")
    private String appName;
    @ApiModelProperty(value = "版本")
    private String appVersion;
    @ApiModelProperty(value = "md5")
    private String md5;
    @ApiModelProperty(value = "大小")
    private String size;
    @ApiModelProperty(value = "1android 2ios")
    private Integer terminalType;
    @ApiModelProperty(value = "ios appID")
    private String appId;
    
}
