package cn.ijiami.detection.VO.compliance;

import cn.ijiami.detection.enums.TerminalTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "ComplianceBaseVo", description = "合规检测基础参数")
public class ComplianceBaseVo implements Serializable {

    private static final long serialVersionUID = 7089418671931457924L;

    @ApiModelProperty(value = "任务id")
    private Long taskId;

    @ApiModelProperty(value = "设备类型，1.android，2.ios，4微信小程序，5支付宝")
    private TerminalTypeEnum terminalType;

    @ApiModelProperty(value = "筛选条件，1.全部检测项、2.本次违规检测项、3.上次违规检测项、4.新增违规检测项")
    private Integer condition;

    @ApiModelProperty(value = "父检测任务id")
    private Long parentTaskId;

    @ApiModelProperty(value = "当前阶段")
    private String nodeCode;

    @ApiModelProperty(value = "行为阶段 授权前行为、前台运行行为、后台运行行为")
    private Integer behaviorStage;
}
