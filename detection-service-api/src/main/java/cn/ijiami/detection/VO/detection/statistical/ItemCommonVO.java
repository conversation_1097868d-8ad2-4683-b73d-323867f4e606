package cn.ijiami.detection.VO.detection.statistical;

/**
 * 检测项展示通用vo
 * 
 * <AUTHOR>
 *
 */
public class ItemCommonVO {

	// 检测项分类名称
	private String name;

	// 检测项目数量
	private String itemCount;

	// 等级列名
	private String cloumName = "风险项";

	// 存在风险项结果统计
	private String riskCount;

	public String getName() {
		return name;
	}

	public String getItemCount() {
		return itemCount;
	}

	public String getCloumName() {
		return cloumName;
	}

	public String getRiskCount() {
		return riskCount;
	}

	public void setName(String name) {
		this.name = name;
	}

	public void setItemCount(String itemCount) {
		this.itemCount = itemCount;
	}

	public void setCloumName(String cloumName) {
		this.cloumName = cloumName;
	}

	public void setRiskCount(String riskCount) {
		this.riskCount = riskCount;
	}
}
