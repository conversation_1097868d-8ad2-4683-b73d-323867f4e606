package cn.ijiami.detection.VO.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsInfo.java
 * @Description 误报资产详情
 * @createTime 2022年07月06日 10:54:00
 */
@Data
public class DetectFalsePositivesAssetsInfo {

    @ApiModelProperty(value = "资产名称", hidden = false, example = "连连看")
    private String assetsName;

    @ApiModelProperty(value = "检测次数", hidden = false, example = "1")
    private Integer detectionCount;

    @ApiModelProperty(value = "误报总数", example = "1")
    private Integer detectFalsePositivesTotalCount;

    @ApiModelProperty(value = "本条误报次数", example = "1")
    private Integer currentDetectFalsePositivesCount;

    @ApiModelProperty(value = "上传人员", example = "管理员")
    private String userName;

    @ApiModelProperty(value = "版本", hidden = false, example = "1.1.0")
    private String version;

    @ApiModelProperty(value = "应用logo", hidden = false, example = "L2RlZmF1bHQvaW1hZ2VzLzE2NTY5MTk2NTY1NTnmgYvniLHorrAucG5n")
    private String logo;

}
