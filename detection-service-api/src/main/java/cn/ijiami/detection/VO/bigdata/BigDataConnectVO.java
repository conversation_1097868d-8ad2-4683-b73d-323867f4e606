package cn.ijiami.detection.VO.bigdata;

import cn.ijiami.detection.VO.*;
import cn.ijiami.detection.VO.detection.BaseMessageVO;
import cn.ijiami.detection.VO.detection.SdkVO;
import cn.ijiami.detection.VO.detection.privacy.PrivacyCheckVO;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 大数据数据对接数据传输类
 *
 * <AUTHOR>
 * @date 2020-05-12 18:03
 */
@Data
public class BigDataConnectVO implements Serializable {
    private static final long serialVersionUID = -3642411348205434452L;

    @ApiModelProperty(value = "隐私检测检存在mongo中的id")
    private String id;

    @ApiModelProperty(value = "隐私检测应用md5")
    private String md5;

    @ApiModelProperty(value = "隐私检测系统版本")
    private Integer isRisk;

    @ApiModelProperty(value = "隐私检测检测时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date detectionTime;


    @ApiModelProperty(value = "隐私检测系统版本")
    private String privacyDetectionVersion = "2.3.0";

    @ApiModelProperty(value = "检测引擎版本")
    private String detectionToolsVersion = "2.0";

    @ApiModelProperty(value = "基本信息")
    private BaseMessageVO appBaseInfo;

    @ApiModelProperty(value = "SDK信息")
    private List<SdkVO> sdks;

    @ApiModelProperty(value = "法律法规类型 1.自评估指南 2.GB/T 35273")
    private List<Integer> lawType;

    @ApiModelProperty(value = "隐私条款检测结果")
    private List<PrivacyCheckVO> privacyChecks;

    @ApiModelProperty(value = "声明权限")
    List<PermissionVO> xmlPermissions;

    @ApiModelProperty(value = "越权权限")
    List<PermissionVO> excessPermissions;

    @ApiModelProperty(value = "尝试使用未申明权限")
    List<PermissionVO> noDeclaredPermissions;

    @ApiModelProperty(value = "敏感权限")
    List<PermissionVO> sensitivePermissions;

    @ApiModelProperty(value = "5.1沙箱行为数据")
    List<PrivacyActionVO> privacyActions;

    @ApiModelProperty(value = "7.1沙箱行为数据")
    List<TPrivacyActionNougat> privacyActionNougats;

    @ApiModelProperty(value = "个人信息cookie")
    List<TPrivacySensitiveWord> cookies;

    @ApiModelProperty(value = "网络传输个人信息")
    List<TPrivacySensitiveWord> privacySensitiveWords;

    @ApiModelProperty(value = "境外访问信息")
    List<TPrivacyOutsideAddress> outSideAddress;;

    @ApiModelProperty(value = "隐私条款检测项截图信息")
    List<PrivacyPolicyImgVO> policyImgs;

    @ApiModelProperty(value = "个人信息（网络请求造图）图片信息")
    List<SensitiveWordImgVO> sensitiveWordImgs;

}
