package cn.ijiami.detection.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AppStoreBehaviorApiVO.java
 * @Description 应用商店上架隐私类API
 * @createTime 2024年04月10日 18:36:00
 */
@Data
public class AppStorePrivacyApiVO {

    @ApiModelProperty(value = "权限名称")
    private String permissionName;

    @ApiModelProperty(value = "api名称")
    private String apiName;

    @ApiModelProperty(value = "api分类")
    private String apiType;

    @ApiModelProperty(value = "权限描述")
    private String permissionDesc;

    @ApiModelProperty(value = "主体名称")
    private String executor;

    @ApiModelProperty(value = "是否在隐私政策中声明")
    private Boolean isPolicyPrivacy;

    @ApiModelProperty(value = "调用次数")
    private Integer useCount;

    @ApiModelProperty(value = "权限等级")
    private String protectionLevel;

    @ApiModelProperty(value = "敏感等级 1敏感  0正常")
    private Integer type;

    @ApiModelProperty(value = "是否与个人信息相关")
    private Integer isPrivacy;

}