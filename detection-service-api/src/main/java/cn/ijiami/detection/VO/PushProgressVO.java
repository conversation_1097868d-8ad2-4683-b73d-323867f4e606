package cn.ijiami.detection.VO;

import cn.ijiami.detection.enums.DetectionTypeEnum;
import cn.ijiami.detection.enums.PushProgressEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 进度回调推送数据实体
 *
 * <AUTHOR>
 * @date 2021-01-25 18:58
 */
@ApiModel(value = "进度回调推送实体")
public class PushProgressVO implements Serializable {

    private static final long              serialVersionUID = -8272717380766679309L;
    @ApiModelProperty(value = "检测类型")
    private DetectionTypeEnum detectionType;
    @ApiModelProperty(value = "检测进度")
    private PushProgressEnum  pushProgress;
   
    public DetectionTypeEnum getDetectionType() {
        return detectionType;
    }

    public void setDetectionType(DetectionTypeEnum detectionType) {
        this.detectionType = detectionType;
    }

    public PushProgressEnum getPushProgress() {
        return pushProgress;
    }

    public void setPushProgress(PushProgressEnum pushProgress) {
        this.pushProgress = pushProgress;
    }

    @Override
    public String toString() {
        return "PushProgressVO{" + "detectionType=" + detectionType + ", pushProgress=" + pushProgress + '}';
    }
}
