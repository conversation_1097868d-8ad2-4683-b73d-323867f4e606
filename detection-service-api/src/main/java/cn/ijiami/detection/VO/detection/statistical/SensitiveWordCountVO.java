package cn.ijiami.detection.VO.detection.statistical;

/**
 * 敏感词统计vo
 * 
 * <AUTHOR>
 *
 */
public class SensitiveWordCountVO {

	// 敏感词数量
	private Integer sensitivi_num;
	// 敏感词分类数量
	private Integer type_num;

	// 名称
	private String name = "检出数量";

	public Integer getSensitivi_num() {
		return sensitivi_num;
	}

	public Integer getType_num() {
		return type_num;
	}

	public String getName() {
		return name;
	}

	public void setSensitivi_num(Integer sensitivi_num) {
		this.sensitivi_num = sensitivi_num;
	}

	public void setType_num(Integer type_num) {
		this.type_num = type_num;
	}

	public void setName(String name) {
		this.name = name;
	}
}
