package cn.ijiami.detection.VO.ai;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AIAvailableCountVO.java
 * @Description ai可用次数
 * @createTime 2025年02月25日 14:55:00
 */
@Data
public class AIAvailableCountVO {

    @ApiModelProperty("可用次数")
    private Integer available;

    @ApiModelProperty("限制次数")
    private Integer limit;

    @ApiModelProperty("次数限制类型 1 按总次数 2 按月 3 按年")
    private Integer limitType;

}
