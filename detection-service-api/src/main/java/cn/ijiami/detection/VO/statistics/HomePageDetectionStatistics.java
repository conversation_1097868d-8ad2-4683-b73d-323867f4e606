package cn.ijiami.detection.VO.statistics;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName HomePageDetectionStatistics.java
 * @Description 检测内容统计
 * @createTime 2024年05月16日 16:54:00
 */
@Data
public class HomePageDetectionStatistics {

    /**
     * 检测应用次数
     */
    @ApiModelProperty(value = "检测应用数，单个应用查询时为检测应用版本数", example = "1")
    private Integer detectionTotalCount;

    /**
     * 检测次数
     */
    @ApiModelProperty(value = "快速检测次数", example = "1")
    private Integer fastDetectionCount;

    /**
     * 检测次数
     */
    @ApiModelProperty(value = "深度检测次数", example = "1")
    private Integer deepDetectionCount;

    /**
     * 检测次数
     */
    @ApiModelProperty(value = "AI检测次数", example = "1")
    private Integer aIDetectionCount;

    /**
     * 合规问题总数
     */
    @ApiModelProperty(value = "合规问题总数", example = "1")
    private Integer nonComplianceTotalCount;

    @ApiModelProperty(value = "每日检测统计列表")
    private List<HomePageDailyStatistics> dailyStatisticsList;

    @ApiModelProperty(value = "法规统计列表")
    private List<HomePageLawStatistics> lawStatisticsList;

}
