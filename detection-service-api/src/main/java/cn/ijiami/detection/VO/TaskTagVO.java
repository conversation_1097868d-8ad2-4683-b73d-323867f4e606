package cn.ijiami.detection.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TaskTagVO.java
 * @Description 返回给前端的任务标签
 * @createTime 2022年08月08日 18:27:00
 */
@Data
public class TaskTagVO {

    @ApiModelProperty(value = "标签id")
    private Long tagId;

    @ApiModelProperty(value = "标签名称")
    private String tagName;

    @ApiModelProperty(value = "是否选中")
    private Boolean selected;

}
