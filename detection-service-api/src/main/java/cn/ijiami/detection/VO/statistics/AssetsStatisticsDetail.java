package cn.ijiami.detection.VO.statistics;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AssetsStatisticsDetail.java
 * @Description 资产统计详情
 * @createTime 2022年07月01日 11:47:00
 */
@Data
public class AssetsStatisticsDetail {

    @ApiModelProperty(value = "唯一id，前端展示要用", hidden = false, example = "1231231")
    private String id;

    @ApiModelProperty(value = "资产名称", hidden = false, example = "连连看")
    private String assetsName;

    @ApiModelProperty(value = "资产id", hidden = false, example = "1231231")
    private Long assetsId;

    @ApiModelProperty(value = "检测次数", hidden = false, example = "1")
    private Integer detectionCount;

    @ApiModelProperty(value = "快速检测次数", hidden = false, example = "1")
    private Integer fastDetectionCount;

    @ApiModelProperty(value = "深度检测次数", hidden = false, example = "1")
    private Integer deepDetectionCount;

    @ApiModelProperty(value = "快速检测完成次数", hidden = false, example = "1")
    private Integer fastDetectionCompletions;

    @ApiModelProperty(value = "深度检测完成次数", hidden = false, example = "1")
    private Integer deepDetectionCompletions;

    @ApiModelProperty(value = "快速检测失败次数", hidden = false, example = "1")
    private Integer fastDetectionFailures;

    @ApiModelProperty(value = "深度检测失败次数", hidden = false, example = "1")
    private Integer deepDetectionFailures;

    @ApiModelProperty(value = "版本", hidden = false, example = "1.1.0")
    private String version;

    @ApiModelProperty(value = "最近一次检测时间", hidden = false, example = "2022-07-01 09:55:27")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastDetectionTime;

    @ApiModelProperty(value = "静态检测耗时", hidden = false, example = "40min")
    private String staticDetectDuration;

    @ApiModelProperty(value = "动态检测耗时", hidden = false, example = "20min")
    private String dynamicDetectDuration;

    @ApiModelProperty(value = "资产版本列表", hidden = false, example = "")
    private List<AssetsStatisticsDetail> versionList;

}
