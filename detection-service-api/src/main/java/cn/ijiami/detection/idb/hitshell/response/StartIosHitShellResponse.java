package cn.ijiami.detection.idb.hitshell.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName StartIosHitShellResponse.java
 * @Description 开始砸壳响应
 * @createTime 2022年01月17日 18:09:00
 */
@Data
public class StartIosHitShellResponse {

    @JsonProperty(value = "state")
    private Integer state;

    @JsonProperty(value = "code")
    private Integer code;

    @JsonProperty(value = "message")
    private String message;

    @JsonProperty(value = "cmdType")
    private Integer cmdType;

    @JsonProperty(value = "resultData")
    private ResultData resultData;

    @Data
    static class ResultData {

        @JsonProperty(value = "businessId")
        private String businessId;

        @JsonProperty(value = "dataType")
        private Integer dataType;

        @JsonProperty(value = "deviceID")
        private String deviceId;

        @JsonProperty(value = "appData")
        private String appData;

        @JsonProperty(value = "appId")
        private String appId;

        @JsonProperty(value = "ipaData")
        private String ipaData;

    }
}
