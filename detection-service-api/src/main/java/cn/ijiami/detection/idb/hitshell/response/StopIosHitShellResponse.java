package cn.ijiami.detection.idb.hitshell.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName StopIosHitShellResponse.java
 * @Description 终止砸壳响应
 * @createTime 2022年01月17日 17:44:00
 */
@Data
public class StopIosHitShellResponse {

    @JsonProperty(value = "businessId")
    private String businessId;

    @JsonProperty(value = "taskState")
    private Integer taskState;

    @JsonProperty(value = "message")
    private String message;

    @JsonProperty(value = "cmdType")
    private Integer cmdType;
}
