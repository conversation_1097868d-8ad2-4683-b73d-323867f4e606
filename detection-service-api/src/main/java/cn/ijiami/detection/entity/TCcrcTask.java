package cn.ijiami.detection.entity;

import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2019/10/30 11:20
 */
@Data
@Table(name = "t_ccrc_task")
public class TCcrcTask implements Serializable {
    private static final long serialVersionUID = 564819223872415750L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    private Long apkId;

    private String categories;

    private String downloadUrl;

    private String callbackUrl;

}
