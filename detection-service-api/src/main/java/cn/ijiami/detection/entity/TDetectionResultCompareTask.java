package cn.ijiami.detection.entity;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TActionFilterGroup.java
 * @Description 检测结果对比任务数据
 * @createTime 2024年01月10日 18:14:00
 */
@Data
@Table(name = "t_detection_result_compare_task")
@ApiModel(value = "TDetectionResultCompareTask", description = "检测结果对比任务数据")
public class TDetectionResultCompareTask {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "主键", hidden = false, example = "1")
    private Long id;

    @ApiModelProperty(value = "任务id", hidden = false)
    @Column(name = "task_id")
    private Long taskId;

    @ApiModelProperty(value = "报告id", hidden = false)
    @Column(name = "report_id")
    private Long reportId;

}
