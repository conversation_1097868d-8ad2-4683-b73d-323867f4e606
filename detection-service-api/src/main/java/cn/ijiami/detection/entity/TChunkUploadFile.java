package cn.ijiami.detection.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;

import cn.ijiami.detection.enums.ChunkUploadFileStatus;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TChunkUpload.java
 * @Description 文件分片上传记录
 * @createTime 2021年12月24日 11:25:00
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
@Data
@Table(name = "t_chunk_upload_file")
public class TChunkUploadFile {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "file_md5")
    private String fileMd5;

    @Column(name = "file_name")
    private String fileName;

    @Column(name = "file_bucket_name")
    private String fileBucketName;

    @Column(name = "file_path_prefix")
    private String filePathPrefix;

    @Column(name = "user_id")
    private Long userId;

    @Column(name = "dfs_path")
    private String dfsPath;

    @Column(name = "chunk_total")
    private Integer chunkTotal;

    @Column(name = "file_size")
    private Long fileSize;

    @Column(name = "status")
    private ChunkUploadFileStatus status;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "file_local_path")
    private String fileLocalPath;

    @Column(name = "terminal_type")
    private Integer terminalType;

    @Column(name = "message")
    private String message;

    @Column(name = "analysis_result")
    private String analysisResult;

    @Column(name = "progress")
    private Integer progress;

    @Column(name = "auto_store")
    private Boolean autoStore;

    @Transient
    private JSONObject assets;
}
