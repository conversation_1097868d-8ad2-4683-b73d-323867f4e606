package cn.ijiami.detection.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 
 * <AUTHOR>
 *
 */
@Table(name = "t_excel_report")
@ApiModel(value = "ExcelReport", description = "Excel导出数据")
public class TExcelReport {

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@ApiModelProperty(value = "ID 主键", hidden = false, example = "1")
    private Long id;

	
	@Column(name = "file_name")
	@ApiModelProperty(value = "文件名称", example = "收集使用个人信息误报记录")
    private String fileName;

	@Column(name = "file_name_regex")
	@ApiModelProperty(value = "文件名匹配规则")
	private String fileNameRegex;

	@Column(name = "category")
	@ApiModelProperty(value = "文件类型，1 通用下载 2 个人信息误报记录 3 疑似SDK 4 IDB文件 5 164号文误报记录 6 191号文误报记录")
	private Integer category;

	@Column(name = "create_time")
	@ApiModelProperty(value = "创建时间", hidden = true, example = "2021-06-08 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;
    
	@Column(name = "update_time")
	@ApiModelProperty(value = "修改时间", hidden = true, example = "2021-06-08 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date updateTime;

	public Long getId() {
		return id;
	}

	public void setId(Long id) {
		this.id = id;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}


	public String getFileNameRegex() {
		return fileNameRegex;
	}

	public void setFileNameRegex(String fileNameRegex) {
		this.fileNameRegex = fileNameRegex;
	}

	public Integer getCategory() {
		return category;
	}

	public void setCategory(Integer category) {
		this.category = category;
	}
}
