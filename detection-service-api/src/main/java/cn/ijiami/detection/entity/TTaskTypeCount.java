package cn.ijiami.detection.entity;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TTaskTypeCount.java
 * @Description 任务类型统计
 * @createTime 2024年08月05日 17:40:00
 */
@Data
public class TTaskTypeCount {

    /**
     * 快速检测总量
     */
    private Integer fastTaskCount;
    /**
     * 深度检测总量
     */
    private Integer deepTaskCount;
    /**
     * ai智能检测
     */
    private Integer aiTaskCount;
    /**
     * 专家检测总量
     */
    private Integer deepExpTaskCount;
    /**
     * 专项检测总量
     */
    private Integer deepSpecialTaskCount;

}
