package cn.ijiami.detection.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TSdkLivraryUpdate.java
 * @Description sdk信息更新
 * @createTime 2022年08月25日 16:17:00
 */
@Data
@Table(name = "t_sdk_library_update")
public class TSdkLibraryUpdate {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 平台标识
     */
    @Column(name = "identification")
    private String identification;

    /**
     * 版本标识
     */
    @Column(name = "version")
    private String version;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

}
