package cn.ijiami.detection.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TXiaomiAppStoreDetectItem.java
 * @Description 小米应用商店专项检测表
 * @createTime 2023年12月13日 12:12:00
 */
@Data
@Table(name = "t_xiaomi_app_store_detect_item")
public class TXiaomiAppStoreDetectItem {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @ApiModelProperty(value = "关键值")
    @Column(name = "item_key")
    private String itemKey;

    @ApiModelProperty(value = "通俗名称")
    @Column(name = "name")
    private String name;

    @ApiModelProperty(value = "名词解析")
    @Column(name = "description")
    private String description;

    @ApiModelProperty(value = "合理的获取场景")
    @Column(name = "purpose")
    private String purpose;

    @ApiModelProperty(value = "获取规则")
    @Column(name = "apply_role")
    private String applyRole;

    @ApiModelProperty(value = "属性")
    @Column(name = "properties")
    private String properties;

    @Column(name = "status")
    @ApiModelProperty(value = "启用情况")
    private Integer status;

    @Column(name = "action_ids")
    @ApiModelProperty(value = "监控的行为id")
    private String actionIds;

    @Column(name = "detection_result_item_no")
    @ApiModelProperty(value = "监控的静态检测检测项编号")
    private String detectionResultItemNo;

    @ApiModelProperty(value = "创建时间")
    @Column(name = "create_time")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @Column(name = "update_time")
    private Date updateTime;
}
