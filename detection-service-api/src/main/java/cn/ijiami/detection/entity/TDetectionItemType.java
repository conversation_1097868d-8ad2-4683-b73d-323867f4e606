package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.enums.TerminalTypeEnum;
import io.swagger.annotations.ApiModelProperty;

/**
 * 检测项分类实体
 * 
 * <AUTHOR>
 *
 */
@Table(name = "t_detection_item_type")
public class TDetectionItemType implements Serializable {
	/**
	 * 主键id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@ApiModelProperty(value = "检测项分类ID", hidden = false, example = "1")
	private Long id;

	/**
	 * 分类名称
	 */
	@Column(name = "type_name")
	@ApiModelProperty(value = "检测项分类名称", hidden = false, example = "基本信息")
	private String typeName;

	/**
	 * 检测项数量
	 */
	@Column(name = "detection_item_count")
	@ApiModelProperty(value = "检测项数量", hidden = false)
	private Integer detectionItemCount;

	/**
	 * 终端类型(1：android、2：ios)
	 */
	@Column(name = "terminal_type")
	@ApiModelProperty(value = "终端类型(1:android , 2:ios)", hidden = false, example = "1")
	private TerminalTypeEnum terminalType;

	/**
	 * 所属平台
	 */
	@Column(name = "platform")
	@ApiModelProperty(value = "所属平台", hidden = false, example = "detection")
	private String platform;

	/**
	 * 创建人
	 */
	@Column(name = "create_user_id")
	@ApiModelProperty(value = "创建人", hidden = false)
	private Long createUserId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_time")
	@ApiModelProperty(value = "创建时间", hidden = false, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date createTime;

	/**
	 * 修改人
	 */
	@Column(name = "update_user_id")
	@ApiModelProperty(value = "修改人", hidden = false)
	private Long updateUserId;

	/**
	 * 修改时间
	 */
	@Column(name = "update_time")
	@ApiModelProperty(value = "修改时间", hidden = false, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date updateTime;

	private static final long serialVersionUID = 1L;

	/**
	 * 获取主键id
	 *
	 * @return id - 主键id
	 */
	public Long getId() {
		return id;
	}

	/**
	 * 设置主键id
	 *
	 * @param id
	 *            主键id
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * 获取分类名称
	 *
	 * @return type_name - 分类名称
	 */
	public String getTypeName() {
		return typeName;
	}

	/**
	 * 设置分类名称
	 *
	 * @param typeName
	 *            分类名称
	 */
	public void setTypeName(String typeName) {
		this.typeName = typeName;
	}

	/**
	 * 获取检测项数量
	 *
	 * @return detection_item_count - 检测项数量
	 */
	public Integer getDetectionItemCount() {
		return detectionItemCount;
	}

	/**
	 * 设置检测项数量
	 *
	 * @param detectionItemCount
	 *            检测项数量
	 */
	public void setDetectionItemCount(Integer detectionItemCount) {
		this.detectionItemCount = detectionItemCount;
	}

	/**
	 * 获取终端类型(1：android、2：ios)
	 *
	 * @return terminal_type - 终端类型(1：android、2：ios)
	 */

	/**
	 * 设置终端类型(1：android、2：ios)
	 *
	 * @param terminalType
	 *            终端类型(1：android、2：ios)
	 */

	/**
	 * 获取所属平台
	 *
	 * @return platform - 所属平台
	 */
	public String getPlatform() {
		return platform;
	}

	public TerminalTypeEnum getTerminalType() {
		return terminalType;
	}

	public void setTerminalType(TerminalTypeEnum terminalType) {
		this.terminalType = terminalType;
	}

	/**
	 * 设置所属平台
	 *
	 * @param platform
	 *            所属平台
	 */
	public void setPlatform(String platform) {
		this.platform = platform;
	}

	/**
	 * 获取创建人
	 *
	 * @return create - 创建人
	 */
	public Long getCreateUserId() {
		return createUserId;
	}

	/**
	 * 设置创建人
	 *
	 * @param create
	 *            创建人
	 */
	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	/**
	 * 获取修改人
	 *
	 * @return update - 修改人
	 */
	public Long getUpdateUserId() {
		return updateUserId;
	}

	/**
	 * 设置修改人
	 *
	 * @param update
	 *            修改人
	 */
	public void setUpdateUserId(Long updateUserId) {
		this.updateUserId = updateUserId;
	}

	/**
	 * 获取创建时间
	 *
	 * @return create_time - 创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}

	/**
	 * 设置创建时间
	 *
	 * @param createTime
	 *            创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	/**
	 * 获取修改时间
	 *
	 * @return update_time - 修改时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}

	/**
	 * 设置修改时间
	 *
	 * @param updateTime
	 *            修改时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
}