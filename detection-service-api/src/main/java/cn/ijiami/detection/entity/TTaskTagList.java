package cn.ijiami.detection.entity;

import javax.persistence.Column;
import javax.persistence.Table;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TTaskTag.java
 * @Description 任务标签
 * @createTime 2022年08月08日 18:07:00
 */
@Data
@Table(name = "t_task_tag_list")
public class TTaskTagList {

    @Column(name = "task_id")
    private Long taskId;

    @Column(name = "tag_id")
    private Long tagId;

}
