package cn.ijiami.detection.entity;

import cn.ijiami.detection.enums.ActionFilterGroupCategoryEnum;
import cn.ijiami.detection.enums.ActionFilterGroupStatusEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.*;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TActionFilterGroup.java
 * @Description 函数过滤集合
 * @createTime 2023年12月11日 18:14:00
 */
@Data
@Table(name = "t_action_filter_group")
@ApiModel(value = "TActionFilterGroup", description = "函数过滤集合")
public class TActionFilterGroup {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "集合ID 主键", hidden = false, example = "1")
    private Long id;

    @ApiModelProperty(value = "名称", hidden = false)
    @Column(name = "name")
    private String name;

    @ApiModelProperty(value = "描述", hidden = false)
    @Column(name = "description")
    private String description;

    @ApiModelProperty(value = "状态 1正常 2主线配置 3删除", hidden = false)
    @Column(name = "status")
    private ActionFilterGroupStatusEnum status;

    @ApiModelProperty(value = "类型 1 指定用户配置 2 全部用户配置", hidden = false)
    @Column(name = "category")
    private ActionFilterGroupCategoryEnum category;

    @ApiModelProperty(value = "所属终端", hidden = false)
    @Column(name = "terminal_type")
    private TerminalTypeEnum terminalType;

    @ApiModelProperty(value = "创建人id", hidden = false)
    @Column(name = "create_user_id")
    private Long createUserId;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value = "修改时间", hidden = true, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date updateTime;


    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间", hidden = true, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;

}
