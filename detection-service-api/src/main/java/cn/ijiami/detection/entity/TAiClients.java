package cn.ijiami.detection.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TAiClients.java
 * @Description ai本地客户调用信息
 * @createTime 2025年03月24日 17:09:00
 */

@Data
@Table(name = "t_ai_clients")
public class TAiClients {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "client_id")
    private Long clientId;

    @Column(name = "customer_name")
    private String customerName;

    @Column(name = "status")
    private Integer status;

    @Column(name = "is_delete")
    private Integer isDelete;

    @Column(name = "update_time")
    private Date updateTime;

    @Column(name = "create_time")
    private Date createTime;
}
