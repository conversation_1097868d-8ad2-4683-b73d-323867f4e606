package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TAppLawsDectDetail.java
 * @Description 统计风险详情表（只存有风险的条文）
 * @createTime 2022年07月05日 15:21:00
 */
@Data
@Table(name = "t_app_laws_dect_detail")
@ApiModel(value = "TAppLawsDectDetail", description = "统计风险详情表（只存有风险的条文）")
public class TAppLawsDectDetail implements Serializable {

    /**
     * 主键id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    @ApiModelProperty(value = "主键", hidden = false, example = "1")
    private Long id;

    /**
     * 任务id
     */
    @Column(name = "task_id")
    @ApiModelProperty(value = "任务id", hidden = false, example = "1")
    private Long taskId;

    /**
     * 法规条文id
     */
    @Column(name = "laws_item_id")
    @ApiModelProperty(value = "法规条文id", hidden = false, example = "1")
    private Long lawsItemId;


    /**
     * 状态
     */
    @Column(name = "status")
    @ApiModelProperty(value = "状态 1 最后一次检测", hidden = false, example = "0")
    private Integer status;

    /**
     * 法规条文父类id
     */
    @Column(name = "laws_item_parent_id")
    @ApiModelProperty(value = "法规条文父类id", hidden = false, example = "1")
    private Long lawsItemParentId;

    /**
     * 条文名称
     */
    @Column(name = "laws_item_name")
    @ApiModelProperty(value = "条文名称", hidden = false, example = "1")
    private String lawsItemName;

    /**
     * 法规类型
     */
    @Column(name = "law_id")
    @ApiModelProperty(value = "法规id", hidden = false, example = "2")
    private Integer lawId;

    /**
     * 终端类型
     */
    @Column(name = "terminal_type")
    @ApiModelProperty(value = "终端类型（1：android  2：ios）", hidden = false, example = "1")
    private Integer terminalType;

    /**
     * 资产id
     */
    @Column(name = "assets_id")
    @ApiModelProperty(value = "资产id", hidden = false, example = "1")
    private Long assetsId;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    @ApiModelProperty(value = "用户id", hidden = false, example = "1")
    private Long userId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间", hidden = true, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;

}
