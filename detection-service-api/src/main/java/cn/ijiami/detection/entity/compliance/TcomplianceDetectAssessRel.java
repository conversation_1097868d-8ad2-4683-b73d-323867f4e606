package cn.ijiami.detection.entity.compliance;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;

/**
 * 评估点与检测点关系
 */
@Table(name = "t_compliance_detect_assess_rel")
public class TcomplianceDetectAssessRel implements Serializable {
    @Id
    private Long id;

    /**
     * 评估点ID
     */
    @Column(name = "assess_point_id")
    private Long assessPointId;

    /**
     * 检测点ID
     */
    @Column(name = "detect_poinit_id")
    private Long detectPoinitId;

    @Column(name = "terminal_type")
    private Integer terminalType;

    private static final long serialVersionUID = -321L;

    /**
     * @return id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * 获取评估点ID
     *
     * @return assess_point_id - 评估点ID
     */
    public Long getAssessPointId() {
        return assessPointId;
    }

    /**
     * 设置评估点ID
     *
     * @param assessPointId 评估点ID
     */
    public void setAssessPointId(Long assessPointId) {
        this.assessPointId = assessPointId;
    }

    /**
     * 获取检测点ID
     *
     * @return detect_poinit_id - 检测点ID
     */
    public Long getDetectPoinitId() {
        return detectPoinitId;
    }

    /**
     * 设置检测点ID
     *
     * @param detectPoinitId 检测点ID
     */
    public void setDetectPoinitId(Long detectPoinitId) {
        this.detectPoinitId = detectPoinitId;
    }

    public Integer getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(Integer terminalType) {
        this.terminalType = terminalType;
    }
}