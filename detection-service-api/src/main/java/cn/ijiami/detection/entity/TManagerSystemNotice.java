package cn.ijiami.detection.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import cn.ijiami.detection.enums.SystemNoticeFrequencyEnum;
import cn.ijiami.detection.enums.SystemNoticePriorityEnum;
import cn.ijiami.detection.enums.SystemNoticeSendStatusEnum;
import cn.ijiami.detection.enums.SystemNoticeTypeEnum;
import cn.ijiami.detection.enums.SystemNoticeValidityPeriodEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName TManagerSystemNotice.java
 * @Description 管理员发送的系统通知
 * @createTime 2023年09月20日 10:33:00
 */
@Data
@Table(name = "t_manager_system_notice")
public class TManagerSystemNotice {

    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value = "ID 主键", hidden = false, example = "1")
    private Long id;

    @Column(name = "content")
    private String content;

    @Column(name = "regards")
    private String regards;

    @Column(name = "template_id")
    private Long templateId;

    @Column(name = "send_status")
    private SystemNoticeSendStatusEnum sendStatus;

    @Column(name = "validity_period")
    private SystemNoticeValidityPeriodEnum validityPeriod;

    @Column(name = "frequency")
    private SystemNoticeFrequencyEnum frequency;

    @Column(name = "type")
    private SystemNoticeTypeEnum type;

    @Column(name = "priority")
    private SystemNoticePriorityEnum priority;

    @Column(name = "create_user_id")
    private Long createUserId;

    @Column(name = "update_user_id")
    private Long updateUserId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value = "创建时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value = "更新时间", hidden = false, example = "2017-07-18 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
    private Date updateTime;

}
