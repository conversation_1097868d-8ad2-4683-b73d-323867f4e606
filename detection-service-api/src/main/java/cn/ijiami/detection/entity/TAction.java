package cn.ijiami.detection.entity;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

import org.springframework.format.annotation.DateTimeFormat;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 行为实体
 * 
 * <AUTHOR>
 *
 */
@Table(name = "t_action")
@ApiModel(value = "TAction", description = "行为实体")
public class TAction implements Serializable {
	/**
	 * 主键id
	 */
	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@ApiModelProperty(value = "行为ID 主键", hidden = false, example = "1")
	private Long id;

	/**
	 * 行为名称
	 */
	@Column(name = "name")
	@ApiModelProperty(value = "行为名称", hidden = false, example = "运动")
	private String name;

	/**
	 * 行为描述
	 */
	@Column(name = "remark")
	@ApiModelProperty(value = "行为描述", hidden = false, example = "描述")
	private String remark;

	/**
	 * 等级
	 */
	@Column(name = "grade")
	private String grade;

	/**
	 * 所属平台
	 */
	@Column(name = "platform")
	@ApiModelProperty(value = "所属平台", hidden = false, example = "detection")
	private String platform;

	/**
	 * 创建人
	 */
	@Column(name = "create_user_id")
	@ApiModelProperty(value = "创建人", hidden = false)
	private Long createUserId;

	/**
	 * 创建时间
	 */
	@Column(name = "create_time")
	@ApiModelProperty(value = "创建时间", hidden = true, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date createTime;

	/**
	 * 修改人
	 */
	@Column(name = "update_user_id")
	@ApiModelProperty(value = "修改人", hidden = false)
	private Long updateUserId;

	/**
	 * 修改时间
	 */
	@Column(name = "update_time")
	@ApiModelProperty(value = "修改时间", hidden = true, example = "2017-07-18 00:00:00")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd hh:mm:ss")
	private Date updateTime;

	private static final long serialVersionUID = 1L;

	/**
	 * 获取主键id
	 *
	 * @return id - 主键id
	 */
	public Long getId() {
		return id;
	}

	/**
	 * 设置主键id
	 *
	 * @param id
	 *            主键id
	 */
	public void setId(Long id) {
		this.id = id;
	}

	/**
	 * 获取行为名称
	 *
	 * @return name - 行为名称
	 */
	public String getName() {
		return name;
	}

	/**
	 * 设置行为名称
	 *
	 * @param name
	 *            行为名称
	 */
	public void setName(String name) {
		this.name = name;
	}

	/**
	 * 获取行为描述
	 *
	 * @return remark - 行为描述
	 */
	public String getRemark() {
		return remark;
	}

	/**
	 * 设置行为描述
	 *
	 * @param remark
	 *            行为描述
	 */
	public void setRemark(String remark) {
		this.remark = remark;
	}

	/**
	 * 获取等级
	 *
	 * @return grade - 等级
	 */
	public String getGrade() {
		return grade;
	}

	/**
	 * 设置等级
	 *
	 * @param grade
	 *            等级
	 */
	public void setGrade(String grade) {
		this.grade = grade;
	}

	/**
	 * 获取所属平台
	 *
	 * @return platform - 所属平台
	 */
	public String getPlatform() {
		return platform;
	}

	/**
	 * 设置所属平台
	 *
	 * @param platform
	 *            所属平台
	 */
	public void setPlatform(String platform) {
		this.platform = platform;
	}

	/**
	 * 获取创建人
	 *
	 * @return create - 创建人
	 */
	public Long getCreateUserId() {
		return createUserId;
	}

	/**
	 * 设置创建人
	 *
	 * @param create
	 *            创建人
	 */
	public void setCreateUserId(Long createUserId) {
		this.createUserId = createUserId;
	}

	/**
	 * 获取修改人
	 *
	 * @return update - 修改人
	 */
	public Long getUpdateUserId() {
		return updateUserId;
	}

	/**
	 * 设置修改人
	 *
	 * @param update
	 *            修改人
	 */
	public void setUpdateUserId(Long updateUserId) {
		this.updateUserId = updateUserId;
	}

	/**
	 * 获取创建时间
	 *
	 * @return create_time - 创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}

	/**
	 * 设置创建时间
	 *
	 * @param createTime
	 *            创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	/**
	 * 获取修改时间
	 *
	 * @return update_time - 修改时间
	 */
	public Date getUpdateTime() {
		return updateTime;
	}

	/**
	 * 设置修改时间
	 *
	 * @param updateTime
	 *            修改时间
	 */
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
}