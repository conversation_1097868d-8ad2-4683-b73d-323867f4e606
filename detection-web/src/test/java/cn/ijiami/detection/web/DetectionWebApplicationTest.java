package cn.ijiami.detection.web;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.test.context.web.WebAppConfiguration;

import tk.mybatis.spring.annotation.MapperScan;

/**
 * 检测测试类
 *
 * <AUTHOR>
 */
//@RunWith(SpringJUnit4ClassRunner.class)
@WebAppConfiguration
@SpringBootApplication
@ComponentScan(basePackages = {"cn.ijiami.*"})
@MapperScan("cn.ijiami.**.mapper")
@ServletComponentScan("cn.ijiami.*")
public class DetectionWebApplicationTest {

    private static final Logger LOG = LoggerFactory.getLogger(DetectionWebApplicationTest.class);
}
