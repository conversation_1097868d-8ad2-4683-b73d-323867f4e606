package cn.ijiami.detection.service.impl;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import cn.ijiami.detection.helper.IosActionExecutorHelper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.ocpframework.sdk.detection.vo.DiscernVO;
import com.ocpframework.sdk.detection.vo.SdkApiVO;

import cn.ijiami.detection.BaseJunit;
import cn.ijiami.detection.entity.TIosFrameworkLibrary;
import cn.ijiami.detection.mapper.TIosFrameworkLibraryMapper;
import cn.ijiami.detection.utils.StreamUtils;
import net.sf.json.JSONArray;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IosSdkLibraryServiceImplTest.java
 * @Description
 * @createTime 2021年10月09日 16:15:00
 */
public class IosSdkLibraryServiceImplTest extends BaseJunit {

    @Autowired
    private IosSdkLibraryServiceImpl iosSdkLibraryService;

    @Autowired
    private TIosFrameworkLibraryMapper tIosFrameworkLibraryMapper;

    @Test
    public void testQuery() {
        DiscernVO discernVO = iosSdkLibraryService.findSdkLibraryByStackInfo("[\"0###Qiniu###0x00038474###-[NSURLSession(QNURLProtocol) qn_dataTaskWithRequest:]###+52\",\"1###AFNetworking###0x0001b58c###-[AFURLSessionManager dataTaskWithRequest:uploadProgress:downloadProgress:completionHandler:]###+136\",\"2###AFNetworking###0x000070dc###-[AFHTTPSessionManager dataTaskWithHTTPMethod:URLString:parameters:headers:uploadProgress:downloadProgress:success:failure:]###+872\",\"3###AFNetworking###0x0000657c###-[AFHTTPSessionManager POST:parameters:headers:progress:success:failure:]###+64\",\"4###UIKitCore###0x182d107b0###<redacted>###+11716528\",\"5###UIKitCore###0x182d12150###<redacted>###+11723088\",\"6###UIKitCore###0x182d17f24###<redacted>###+11747108\",\"7###UIKitCore###0x1823ea8c0###<redacted>###+2123968\",\"8###UIKitCore###0x18290f2c8###_UIScenePerformActionsWithLifecycleActionMask###+96\",\"9###UIKitCore###0x1823eb3a8###<redacted>###+2126760\",\"10###UIKitCore###0x1823eaeb0###<redacted>###+2125488\",\"11###UIKitCore###0x1823eb1d4###<redacted>###+2126292\",\"12###UIKitCore###0x1823eaa90###<redacted>###+2124432\",\"13###UIKitCore###0x1823f272c###<redacted>###+2156332\",\"14###UIKitCore###0x18282938c###<redacted>###+6574988\",\"15###UIKitCore###0x182927184###_UISceneSettingsDiffActionPerformChangesWithTransitionContext###+248\",\"16###UIKitCore###0x1823f2420###<redacted>###+2155552\",\"17###UIKitCore###0x182231ae4###<redacted>###+318180\",\"18###UIKitCore###0x182230590###<redacted>###+312720\",\"19###UIKitCore###0x182231730###<redacted>###+317232\",\"20###UIKitCore###0x182d160fc###<redacted>###+11739388\",\"21###UIKitCore###0x18284fa84###<redacted>###+6732420\",\"22###FrontBoardServices###0x18ef49d00###<redacted>###+36096\",\"23###FrontBoardServices###0x18ef714fc###<redacted>###+197884\",\"24###FrontBoardServices###0x18ef57420###<redacted>###+91168\",\"25###FrontBoardServices###0x18ef71214###<redacted>###+197140\",\"26###libdispatch.dylib###0x1800a7298###<redacted>###+397976\",\"27###libdispatch.dylib###0x18004c5b8###<redacted>###+26040\",\"28###FrontBoardServices###0x18ef95e04###<redacted>###+347652\",\"29###FrontBoardServices###0x18ef95acc###<redacted>###+346828\",\"30###FrontBoardServices###0x18ef95fa0###<redacted>###+348064\",\"31###CoreFoundation###0x1803ee8a8###<redacted>###+633000\",\"32###CoreFoundation###0x1803ee7a8###<redacted>###+632744\",\"33###CoreFoundation###0x1803edafc###<redacted>###+629500\",\"34###CoreFoundation###0x1803e8018###<redacted>###+606232\",\"35###CoreFoundation###0x1803e77d0###CFRunLoopRunSpecific###+572\",\"36###GraphicsServices###0x196b29570###GSEventRunModal###+160\",\"37###UIKitCore###0x182d142d0###<redacted>###+11731664\",\"38###UIKitCore###0x182d1984c###UIApplicationMain###+164\",\"39###libdyld.dylib###0x1800c6140###<redacted>###+4416\"]");
        match(discernVO, Arrays.asList("Qiniu", "AFNetworking"));

        DiscernVO discernVO2 = iosSdkLibraryService.findSdkLibraryByStackInfo("[\"0###Qiniu###0x0003b70c###-[NSURLSession(QNURLProtocol) qn_dataTaskWithRequest:]###+52\",\"1###SDWebImage###0x00026a48###-[SDWebImageDownloaderOperation start]###+1096\",\"2###Foundation###0x1817617c0###<redacted>###+20\",\"3###Foundation###0x18176128c###<redacted>###+180\",\"4###libdispatch.dylib###0x1800563e4###<redacted>###+104\",\"5###libdispatch.dylib###0x1800a7298###<redacted>###+16\",\"6###libdispatch.dylib###0x18004c028###<redacted>###+412\",\"7###libdispatch.dylib###0x18004b828###<redacted>###+784\",\"8###libdispatch.dylib###0x180058bb8###<redacted>###+376\",\"9###libdispatch.dylib###0x180059378###<redacted>###+120\",\"10###libsystem_pthread.dylib###0x1c8a79580###_pthread_wqthread###+212\"]");
        match(discernVO2, Arrays.asList("Qiniu", "SDWebImage"));

        DiscernVO discernVO3 = iosSdkLibraryService.findSdkLibraryByStackInfo("[\"0###Hyphenate###0x00060f64###-[EMClient initializeSDKWithOptions:]###+2124\",\"1###HelpDesk###0x000528a4###-[HDClient initializeSDKWithOptions:]###+492\",\"2###UIKitCore###0x182d1ae8c###<redacted>###+11759244\",\"3###UIKitCore###0x1826ab018###<redacted>###+5009432\",\"4###UIKitCore###0x1826ab358###<redacted>###+5010264\",\"5###UIKitCore###0x1826a9ca8###<redacted>###+5004456\",\"6###UIKitCore###0x182d554c4###<redacted>###+11998404\",\"7###UIKitCore###0x182d56dac###<redacted>###+12004780\",\"8###UIKitCore###0x182d329d8###<redacted>###+11856344\",\"9###UIKit###0x1c8cb4cb4###<redacted>###+171188\",\"10###UIKitCore###0x182db5db4###<redacted>###+12393908\",\"11###UIKitCore###0x182dba5d8###<redacted>###+12412376\",\"12###UIKitCore###0x182db18c4###<redacted>###+12376260\",\"13###CoreFoundation###0x1803ee8a8###<redacted>###+633000\",\"14###CoreFoundation###0x1803ee7a8###<redacted>###+632744\",\"15###CoreFoundation###0x1803edafc###<redacted>###+629500\",\"16###CoreFoundation###0x1803e8018###<redacted>###+606232\",\"17###CoreFoundation###0x1803e77d0###CFRunLoopRunSpecific###+572\",\"18###GraphicsServices###0x196b29570###GSEventRunModal###+160\",\"19###UIKitCore###0x182d142d0###<redacted>###+11731664\",\"20###UIKitCore###0x182d1984c###UIApplicationMain###+164\",\"21###libdyld.dylib###0x1800c6140###<redacted>###+4416\"]");
        match(discernVO3, Arrays.asList("Hyphenate", "HelpDesk"));

        DiscernVO discernVO1 = iosSdkLibraryService.findSdkLibraryByStackInfo("[\"0###Qiniu###0x0001887c###+[_AFURLSessionTaskSwizzling load]###+140\",\"1###libobjc.A.dylib###0x1941eddd4###<redacted>###+64980\"]");
        match(discernVO1, Arrays.asList("Qiniu"));

        DiscernVO discernVO5 = iosSdkLibraryService.findSdkLibraryByStackInfo("[\"0###Qiniu###0x00036a10###-[NSURLSession(QNURLProtocol) qn_dataTaskWithRequest:]###+52\",\"1###WidgetPreload###0x00111c30###_swift_stdlib_malloc_size###+63876\",\"2###WidgetPreload###0x00173914###_swift_stdlib_malloc_size###+25964\",\"3###WidgetPreload###0x00180664###__swift_memcpy128_8###+5804\",\"4###WidgetPreload###0x000fa5a8###__swift_allocate_boxed_opaque_existential_1###+152\",\"5###libdispatch.dylib###0x1800a62b0###<redacted>###+24\",\"6###libdispatch.dylib###0x1800a7298###<redacted>###+16\",\"7###libdispatch.dylib###0x18004fa40###<redacted>###+612\",\"8###libdispatch.dylib###0x180050518###<redacted>###+420\",\"9###libdispatch.dylib###0x180059fac###<redacted>###+712\",\"10###libsystem_pthread.dylib###0x1c8a795bc###_pthread_wqthread###+272\"]");
        match(discernVO5, Arrays.asList("Qiniu"));

        DiscernVO discernVO4 = iosSdkLibraryService.findSdkLibraryByStackInfo("[\"0###AFNetworking###0x0000bd38###__47-[AFNetworkReachabilityManager startMonitoring]_block_invoke###+124\",\"1###AFNetworking###0x0000c0a0###__AFPostReachabilityStatusChange_block_invoke###+60\",\"2###libdispatch.dylib###0x1800a62b0###<redacted>###+393904\",\"3###libdispatch.dylib###0x1800a7298###<redacted>###+397976\",\"4###libdispatch.dylib###0x180055ce0###<redacted>###+64736\",\"5###CoreFoundation###0x1803ee298###<redacted>###+631448\",\"6###CoreFoundation###0x1803e86f8###<redacted>###+607992\",\"7###CoreFoundation###0x1803e77d0###CFRunLoopRunSpecific###+572\",\"8###GraphicsServices###0x196b29570###GSEventRunModal###+160\",\"9###UIKitCore###0x182d142d0###<redacted>###+11731664\",\"10###UIKitCore###0x182d1984c###UIApplicationMain###+164\",\"11###libdyld.dylib###0x1800c6140###<redacted>###+4416\"]");
        match(discernVO4, Arrays.asList("AFNetworking"));

    }

    private void match(DiscernVO discernVO, List<String> nameList) {
        Set<SdkApiVO> sdkSet = discernVO.getDumpApis().stream().filter(StreamUtils.distinctByKey(SdkApiVO::getSdkName)).collect(Collectors.toSet());
        assert sdkSet.size() == nameList.size();
        assert sdkSet.stream().allMatch(sdkApiVO -> nameList.contains(sdkApiVO.getSdkName()));
    }

    @Test
    public void testSystemLibrary() {
        List<TIosFrameworkLibrary> list = tIosFrameworkLibraryMapper.selectAll();
        JSONArray jsonArray = JSONArray.fromObject("[\"0###CFNetwork###0x180a6bac4###CFNetServiceBrowserSearchForServices###+75840\",\"1###CFNetwork###0x180a7c584###_CFHTTPMessageSetResponseProxyURL###+9328\",\"2###libdispatch.dylib###0x1800a62b0###<redacted>###+24\",\"3###libdispatch.dylib###0x1800a7298###<redacted>###+16\",\"4###libdispatch.dylib###0x18004fa40###<redacted>###+612\",\"5###libdispatch.dylib###0x180050548###<redacted>###+468\",\"6###libdispatch.dylib###0x180059fac###<redacted>###+712\",\"7###libsystem_pthread.dylib###0x1c8a795bc###_pthread_wqthread###+272\"]");
        assert IosActionExecutorHelper.isSystemLibrary(list, jsonArray, "淘宝");
    }

}
