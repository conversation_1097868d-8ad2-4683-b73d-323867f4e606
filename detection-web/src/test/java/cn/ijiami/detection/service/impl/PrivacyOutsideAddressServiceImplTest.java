package cn.ijiami.detection.service.impl;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import cn.ijiami.detection.BaseJunit;
import cn.ijiami.detection.VO.networkcheck.DialDetectResult;
import cn.ijiami.detection.entity.TIpAddressCheckRecord;
import cn.ijiami.detection.mapper.TIpAddressCheckRecordMapper;
import cn.ijiami.detection.query.IpAddressCheckQuery;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName PrivacyOutsideAddressServiceImplTest.java
 * @Description
 * @createTime 2021年11月23日 15:48:00
 */
public class PrivacyOutsideAddressServiceImplTest extends BaseJunit {

    @Autowired
    private PrivacyOutsideAddressServiceImpl privacyOutsideAddressService;

    @Autowired
    private TIpAddressCheckRecordMapper addressCheckRecordMapper;

    @Test
    public void testSaveIpAddressCheckData() {
        TIpAddressCheckRecord record = addressCheckRecordMapper.selectByPrimaryKey(5L);
        DialDetectResult result = privacyOutsideAddressService.queryIpAddressCheckDataByRecord(record);
        assert result != null;
        privacyOutsideAddressService.saveIpAddressCheckData(result);
    }

    @Test
    public void testIpAddressCheck() {
        TIpAddressCheckRecord record = addressCheckRecordMapper.selectByPrimaryKey(5L);
        IpAddressCheckQuery query = new IpAddressCheckQuery();
        query.setOutsideId(3052361L);
        privacyOutsideAddressService.ipAddressCheck(query, 1L);
    }

}
