package cn.ijiami.detection.util;

import java.io.File;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.io.FileUtils;
import org.junit.jupiter.api.Test;

import com.github.xiaoymin.knife4j.annotations.Ignore;

import cn.ijiami.detection.utils.HtmlToPdf;

public class HtmlToPdfTest {

    /**
     * 清理ios 获取到的app icon,转换位可用的格式
     */
    @Ignore
    @Test
    public void testConvertImg() {
        File file = new File("C:\\Users\\<USER>\\Desktop\\images");
        Collection<File> files = FileUtils.listFiles(file, null, false);
        List<String> filePaths = files.stream().map(File::getAbsolutePath).collect(Collectors.toList());
        System.err.println(filePaths);
        for (String filePath : filePaths) {
            HtmlToPdf.convertImg(filePath);

        }
    }
}