package cn.ijiami.detection.util;

import cn.ijiami.detection.entity.TSensitiveWord;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;

import cn.ijiami.detection.helper.SensitiveWordsHelper;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName SensitiveWordsHelperTest.java
 * @Description
 * @createTime 2022年09月26日 10:31:00
 */
public class SensitiveWordsHelperTest {

    @Test
    public void test() {
        assert !SensitiveWordsHelper.findName("&NAME=\"$[abc\"", "$[abc").isEmpty();
    }

    @Test
    public void testPhoneNumber() {
        TSensitiveWord word = new TSensitiveWord();
        word.setName("电话号码");
        word.setSensitiveWords("phone|telephone|dianhua|Tel|mobile");
        word.setRegex("((13[0-9]|14[5|7]|15[0|1|2|3|5|6|7|8|9]|18[0|1|2|3|5|6|7|8|9])\\d{8})|(\\d{3}-\\d{8}|\\d{4}-\\d{7})");
        Pattern pattern = Pattern.compile(word.getRegex());
        String content = "9e4e5fa7244c6b6e_gdp_session_id=7edf61a4-2358-4f6c-84e6-52eb141a9afa; gdp_user_id=gioenc-8a05c21d%2C0gb5%2C5g03%2C809c%2C2ba8g8b63c65; 9e4e5fa7244c6b6e_gdp_session_id_sent=7edf61a4-2358-4f6c-84e6-52eb141a9afa; userMobileForBigData=13726208087; sajssdk_2015_cross_new_user=1; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22195dbbf3fd326-033c8a7ff702994-112f1629-301584-195dbbf3fd415%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk1ZGJiZjNmZDMyNi0wMzNjOGE3ZmY3MDI5OTQtMTEyZjE2MjktMzAxNTg0LTE5NWRiYmYzZmQ0MTUifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%22%2C%22value%22%3A%22%22%7D%2C%22%24device_id%22%3A%22195dbbf3fd326-033c8a7ff702994-112f1629-301584-195dbbf3fd415%22%7D; 9e4e5fa7244c6b6e_gdp_cs1=gioenc-EkFI30um9M02V2L*41xCfv<<; 9e4e5fa7244c6b6e_gdp_gio_id=gioenc-EkFI30um9M02V2L*41xCfv<<; 9e4e5fa7244c6b6e_gdp_sequence_ids={%22globalKey%22:10%2C%22VISIT%22:2%2C%22PAGE%22:3%2C%22CUSTOM%22:7}\n" +
                "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";
        Matcher matcher = pattern.matcher(content);
        while (matcher.find()) {
            boolean isInvalidInfo = SensitiveWordsHelper.isInvalidInfo(matcher, content, word, true);
            if (isInvalidInfo) {
                continue;
            }
            String sensitiveWord = matcher.group();
            if (StringUtils.isBlank(sensitiveWord)) {
                continue;
            }
            //关键字匹配到开始位置
            String code = SensitiveWordsHelper.getMatchString(matcher, content);
            assert StringUtils.isNotBlank(code);
        }
    }
}
