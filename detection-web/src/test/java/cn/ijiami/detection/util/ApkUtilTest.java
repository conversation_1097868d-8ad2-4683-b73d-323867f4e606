package cn.ijiami.detection.util;

import cn.ijiami.detection.common.datamodels.EncryptCompanyDataModel;
import cn.ijiami.detection.common.utils.CommonUtil;
import cn.ijiami.detection.utils.AppSignatureInfoUtil;
import cn.ijiami.detection.utils.HapUtils;
import cn.ijiami.detection.utils.IpaAnalysisUtils;
import cn.ijiami.detection.utils.hap.HapInfo;
import cn.ijiami.framework.apk.entity.ApkInfo;
import com.android.apksig.ApkVerifier;
import com.android.apksigner.ApkSignerTool;
import com.ijiami.ios.config.InitConfig;
import com.ijmsd.analyzeapktool.UnpackAAB;
import com.ijmsd.analyzeapktool.UnpackAPK;
import com.ijmsd.common.exception.DecompileException;
import net.dongliu.apk.parser.bean.ApkMeta;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.util.List;

import static cn.ijiami.detection.utils.CommonUtil.getFileExtName;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ApkUtilTest.java
 * @Description apk工具类测试
 * @createTime 2021年12月30日 14:55:00
 */
public class ApkUtilTest {

    @Test
    public void testGetApkSign() throws Exception {
        String filePath = "D:\\decompile\\7.APK";
        ApkInfo apkInfo = cn.ijiami.detection.utils.APKUtil.readApkInfo(filePath);
        ApkMeta apkMeta = cn.ijiami.detection.utils.APKUtil.parserReadApkInfo(filePath);
        AppSignatureInfoUtil.AppSignatureInfo apkSignInfo = AppSignatureInfoUtil.extractApkSignatureInfo(filePath);
        assert apkSignInfo != null;
    }

    @Test
    public void testGetApkSingInfo() throws Exception {
        String[] arg = {"--print-certs","--min-sdk-version=24","-v", "D:\\1.apk"};
        ApkVerifier.Result result = ApkSignerTool.verify2(arg);
        assert result != null;
    }


    @Test
    public void testDex() throws Exception {
        String toolPath = "E:/zywa/ijiami/ios/tools";
        String apkPath = "D:\\decompile\\6.APK";
        String decompile = "D:\\decompile\\6";
        File decompileFile = new File(decompile);
        CommonUtil.setConfigPath("E:/zywa/ijiami/ios/configs");
        CommonUtil.setToolsPath("E:/zywa/ijiami/ios/tools");
        if (!decompileFile.exists()) {
            decompileFile.mkdirs();
        }
        try {
            if (apkPath.toLowerCase().endsWith("aab")) {
                UnpackAAB mUnpackAAB = new UnpackAAB(toolPath);
                mUnpackAAB.unpackageAAB(apkPath, decompile, null);
            } else {
                UnpackAPK mUnpackAPK = new UnpackAPK(toolPath);
                mUnpackAPK.unpackageAPK(apkPath, decompile, null);
            }
        } catch (DecompileException e) {
            e.getMessage();
        }
        EncryptCompanyDataModel encryption = CommonUtil.checkEncryptionCompany(decompile, getFileExtName(apkPath));
        assert encryption != null;
    }

    @Test
    public void testIPA() throws Exception {
        InitConfig.configRootPath("E:/zywa/ijiami/ios/masm");
        List<String> permissionList = IpaAnalysisUtils.extractPermissionInfo("D:\\decompile\\5.ipa");
        assert permissionList != null;
    }


    @Test
    public void testHapSign() throws Exception {
        AppSignatureInfoUtil.AppSignatureInfo appSignatureInfo = AppSignatureInfoUtil.extractHapSignatureInfo(
                "D:\\harmony",
                "D:\\harmony\\红塔银行_20240712194440_.hap"
        );
        assert appSignatureInfo != null;
    }

    @Test
    public void testHap() throws Exception {
        HapInfo hapInfo = HapUtils.readHapInfo(
                "D:\\harmony\\proUpdate_HD_release_240606_10.9.0_sec_signed.hap",
                "D:\\harmony\\icon"
        );
        assert hapInfo != null;
    }

}
