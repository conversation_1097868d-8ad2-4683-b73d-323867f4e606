<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- ================================================ 【开发环境】 ================================================ -->
    <!-- 定义全局参数常量 -->
    <property name="log.appName" value="detection"/>
    <property name="log.filePath" value="E:/logs/${log.appName}"/>
    <!-- 日志文件保留天数 -->
    <property name="log.maxHistory" value="1"/>
    <!-- 单个文件最大存储 -->
    <property name="log.size" value="100MB"/>
    <property name="log.pattern" value="[TRACE-ID:%32X{traceId}] %d{yyyy-MM-dd HH:mm:ss.SSS}-[%-5level] [%25thread{25}] %50logger{50} [%4line{4}] : %msg%n"/>
    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="[TRACE-ID:%X{traceId}] ${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39} [%4line{4}]){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

    <!-- 彩色日志 -->
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <!-- 滚动记录文件，先将日志记录到指定文件，当符合某个条件时，将日志记录到其他文件 -->
    <appender name="APP_ALL_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 指定日志文件的名称 -->
        <file>${log.filePath}/${log.appName}.log</file>
        <!--
            当发生滚动时，决定 RollingFileAppender 的行为，涉及文件移动和重命名
            TimeBasedRollingPolicy： 最常用的滚动策略，它根据时间来制定滚动策略，既负责滚动也负责出发滚动。
        -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!--
                滚动时产生的文件的存放位置及文件名称 %d{yyyy-MM-dd}：按天进行日志滚动
                %i：当文件大小超过maxFileSize时，按照i进行文件滚动
            -->
            <fileNamePattern>${log.filePath}/all/%d{yyyy-MM-dd}/%i.log</fileNamePattern>
            <!--
                可选节点，控制保留的归档文件的最大数量，超出数量就删除旧文件。假设设置每天滚动，
                且maxHistory是365，则只保存最近365天的文件，删除之前的旧文件。注意，删除旧文件是，
                那些为了归档而创建的目录也会被删除。
            -->
            <MaxHistory>${log.maxHistory}</MaxHistory>
            <!--
                当日志文件超过maxFileSize指定的大小时，根据上面提到的%i进行日志文件滚动
                注意此处配置SizeBasedTriggeringPolicy是无法实现按文件大小进行滚动的，必须配置timeBasedFileNamingAndTriggeringPolicy
            -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${log.size}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <!-- 追加为true,追加方式记录日志 -->
        <append>true</append>
        <!-- 日志输出格式： -->
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <!-- INFO -->
    <appender name="APP_INFO_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.filePath}/info.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.filePath}/info/%d{yyyy-MM-dd}/%i.log</fileNamePattern>
            <maxHistory>${log.maxHistory}</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${log.size}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <!-- 追加为true,追加方式记录日志 -->
        <append>true</append>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <!-- WARN -->
    <appender name="APP_WARN_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.filePath}/warn.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>WARN</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.filePath}/warn/%d{yyyy-MM-dd}/%i.log</fileNamePattern>
            <MaxHistory>${log.maxHistory}</MaxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${log.size}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <append>true</append>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <!-- ERROR -->
    <appender name="APP_ERROR_LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.filePath}/error.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.filePath}/error/%d{yyyy-MM-dd}/%i.log</fileNamePattern>
            <MaxHistory>${log.maxHistory}</MaxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${log.size}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>
        <append>true</append>
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>utf-8</charset>
        </encoder>
    </appender>

    <!-- logger的上级是root,additivity为true会传递至root, 此时日志会打印两次-->
    <logger name="cn.ijiami" level="info" additivity="true"/>
    <!--过滤掉spring和mybatis的一些无用的DEBUG信息-->
    <logger name="org.mybatis" level="info" additivity="true"/>
    <!--监控系统信息-->
    <logger name="org.springframework" level="info" additivity="true"/>
    <!--数据库和网络-->
    <logger name="org.apache" level="info" additivity="true"/>
    <logger name="druid.sql" level="info" additivity="true"/>
    <logger name="c.n.d.shared" level="info" additivity="true"/>
    <logger name="com.netflix" level="info" additivity="true"/>
    <root level="debug">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>