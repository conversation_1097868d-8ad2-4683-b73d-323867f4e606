<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TIosDeviceConnectionMapper">
    <resultMap id="BaseResultMap"
               type="cn.ijiami.detection.entity.TIosDeviceConnection">
        <!-- WARNING - @mbg.generated -->
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="task_id" property="taskId" jdbcType="BIGINT"/>
        <result column="address" property="address" jdbcType="VARCHAR"/>
        <result column="connection_id" property="connectionId" jdbcType="VARCHAR"/>
        <result column="device_id" property="deviceId" jdbcType="VARCHAR"/>
        <result column="frpc_url" property="frpcUrl" jdbcType="VARCHAR"/>
        <result column="frpc_port" property="frpcPort" jdbcType="VARCHAR"/>
        <result column="frpc_port_status" property="frpcPortStatus" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="create_time" property="createTime"
                jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime"
                jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="base_sql">
        id,task_id,address,connection_id,device_id,user_id,frpc_url,frpc_port,frpc_port_status,create_time,update_time
    </sql>

    <!-- 查询行为、行为函数 -->
    <select id="findByConnectionId"
            parameterType="cn.ijiami.detection.entity.TIosDeviceConnection"
            resultMap="BaseResultMap">
        SELECT
        <include refid="base_sql" />
        FROM t_ios_device_connection
        WHERE connection_id=#{connectionId}
    </select>

    <!-- 查询行为、行为函数 -->
    <select id="findByTaskId"
            parameterType="cn.ijiami.detection.entity.TIosDeviceConnection"
            resultMap="BaseResultMap">
        SELECT
        <include refid="base_sql" />
        FROM t_ios_device_connection
        WHERE task_id=#{taskId} AND address != ''
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <select id="findByTaskIds"
            parameterType="cn.ijiami.detection.entity.TIosDeviceConnection"
            resultMap="BaseResultMap">
        SELECT
        <include refid="base_sql" />
        FROM t_ios_device_connection
        WHERE task_id IN
        <foreach collection="taskIdList" item="taskId" open="(" separator="," close=")">
            #{taskId,jdbcType=BIGINT}
        </foreach>
        AND address != ''
        ORDER BY create_time DESC
    </select>

    <!--    有连接地址的说明idb接到扫码请求，端口释放的时间判断需要大于任务超时时间，没有连接地址的，说明这个码没有被使用，只需要大于二维码超时时间就释放-->
    <update id="freeTimeoutFrpcPort" >
        UPDATE t_ios_device_connection AS c
        LEFT JOIN t_task AS t
        ON t.task_id=c.task_id
        SET c.frpc_port_status=0
        WHERE <![CDATA[ (t.dynamic_status NOT IN (4,7,8,9) OR t.dynamic_status IS NULL)
        AND c.frpc_port_status=1
        AND ((c.address IS NULL AND unix_timestamp(c.update_time)<#{qrCodeTimeout} / 1000)
        OR (c.address IS NOT NULL AND unix_timestamp(c.update_time)<#{taskTimeout} / 1000))
        ]]>
    </update>

    <select id="getTimeoutFrpcPort" resultType="java.lang.String" >
        SELECT c.frpc_port FROM t_ios_device_connection AS c
        LEFT JOIN t_task AS t
        ON t.task_id=c.task_id
        WHERE <![CDATA[ t.dynamic_status NOT IN (4,7,8,9)
        AND c.frpc_port_status=1
        AND c.address IS NULL AND unix_timestamp(c.update_time)<#{qrCodeTimeout}
        ]]>
    </select>

    <select id="findUsedFrpcPort"
            resultType="java.lang.String">
        SELECT
        frpc_port
        FROM t_ios_device_connection
        WHERE frpc_url=#{frpcUrl} AND frpc_port_status=1
    </select>
</mapper>