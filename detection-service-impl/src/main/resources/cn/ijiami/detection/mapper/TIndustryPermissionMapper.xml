<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.ijiami.detection.mapper.TIndustryPermissionMapper">
    <resultMap id="BaseResultMap" type="cn.ijiami.detection.entity.TIndustryPermission">
        <id property="id" column="id"/>
        <result property="industryId" column="industry_id"/>
        <result property="permissionCode" column="permission_code"/>
        <result property="permissionName" column="permission_name"/>
    </resultMap>

    <select id="findByCategoryIds" resultMap="BaseResultMap">
        select industry_id,permission_code,permission_name from t_industry_permission where industry_id in
        <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </select>
</mapper>
