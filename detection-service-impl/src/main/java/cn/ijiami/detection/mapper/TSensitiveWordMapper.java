package cn.ijiami.detection.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.SensitiveAndTypeVO;
import cn.ijiami.detection.entity.TSensitiveWord;
import cn.ijiami.detection.query.SensitiveWordQuery;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TSensitiveWordMapper extends IjiamiMapper<TSensitiveWord> {

	int batchAddSensitiveWord(@Param("sensitiveWordList") List<TSensitiveWord> sensitiveWordList);

	List<TSensitiveWord> selectSensitiveWordByPage(TSensitiveWord sensitiveWord);

	List<SensitiveAndTypeVO> findSensitiveWordByQuery(SensitiveWordQuery sensitiveWordQuery);
    
	List<TSensitiveWord> findSensitiveWord(TSensitiveWord sensitiveWord);

	List<TSensitiveWord> findByTerminalType(@Param("terminalType")Integer terminalType);
}