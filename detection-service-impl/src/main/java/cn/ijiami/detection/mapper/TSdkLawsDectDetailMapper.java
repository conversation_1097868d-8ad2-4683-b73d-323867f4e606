package cn.ijiami.detection.mapper;

import java.util.Date;
import java.util.List;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.statistics.SdkRiskResult;
import cn.ijiami.detection.VO.statistics.SdkUsageResult;
import cn.ijiami.detection.entity.TSdkLawsDectDetail;
import cn.ijiami.framework.mybatis.IjiamiMapper;

public interface TSdkLawsDectDetailMapper extends IjiamiMapper<TSdkLawsDectDetail> {

    List<SdkRiskResult> findRiskSdkByPage(@Param("userId") Long userId,
                                          @Param("lawIds") List<Integer> lawIds,
                                          @Param("sdkName") String sdkName,
                                          @Param("startDate") Date startDate,
                                          @Param("endDate") Date endDate,
                                          @Param("assetsName") String assetsName);

    List<SdkRiskResult> findRiskLawItem(@Param("userId") Long userId,
                                        @Param("lawIds") List<Integer> lawIds,
                                        @Param("sdkName") String sdkName,
                                        @Param("startDate") Date startDate,
                                        @Param("endDate") Date endDate,
                                        @Param("assetsName") String assetsName);

    void deleteByTaskId(@Param("taskId") Long taskId);

    void deleteByAssetsId(@Param("assetsId") Long assetsId);

}
