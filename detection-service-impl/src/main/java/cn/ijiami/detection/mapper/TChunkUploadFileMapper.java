package cn.ijiami.detection.mapper;

import java.util.Collection;
import java.util.List;

import cn.ijiami.detection.VO.CheckChunkFileVO;
import cn.ijiami.detection.query.BaseQuery;
import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.entity.TChunkUploadFile;
import cn.ijiami.framework.mybatis.IjiamiMapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

public interface TChunkUploadFileMapper extends IjiamiMapper<TChunkUploadFile> {


    TChunkUploadFile findOneByUserIdAndStatus(@Param("userId") Long userId, @Param("fileMd5") String fileMd5,
                                              @Param("statusList") Collection<Integer> statusList);

    List<TChunkUploadFile> findByStatusAndTimeout(@Param("hour") Integer hour, @Param("statusList") Collection<Integer> statusList);

    boolean updateFileStatus(@Param("userId") Long userId,@Param("dfsPath") String dfsPath,
                             @Param("updateStatus") Integer updateStatus,
                             @Param("statusList") Collection<Integer> statusList);

    TChunkUploadFile findOneByFileNameOrMd5(@Param("userId") Long userId, @Param("fileName") String fileName,@Param("fileMd5") String fileMd5,@Param("statusList") Collection<Integer> statusList);

    List<TChunkUploadFile> queryAnalysisQueue(BaseQuery baseQuery);

    boolean updateFileStatusFrom(@Param("id") Long id,@Param("updateStatus") Integer updateStatus,@Param("statusList") Collection<Integer> statusList);

    @Select("select count(1) from t_chunk_upload_file where status=#{status}")
    Integer selectCountByStatus(@Param("status") Integer status);

    @Select("select * from t_chunk_upload_file where status=#{status} limit 1")
    TChunkUploadFile selectOneByStatus(@Param("status")Integer status);

}
