package cn.ijiami.detection.mapper;

import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

import cn.ijiami.detection.VO.DetectionItemVO;
import cn.ijiami.detection.entity.TDetectionItem;
import cn.ijiami.detection.query.TemplateDetectionItemQuery;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * 检测项映射类
 *
 * <AUTHOR>
 */
public interface TDetectionItemMapper extends IjiamiMapper<TDetectionItem> {
    /**
     * 根据检测模版ID 查询检测项列表
     *
     * @param detectionItemQuery
     * @return
     */
    List<DetectionItemVO> selectDetectionItemByQuery(TemplateDetectionItemQuery detectionItemQuery);

    /**
     * 查询检测项VO 不分页， 去掉风险为空的数据
     *
     * @param detectionItem
     * @return
     */
    List<DetectionItemVO> selectDetectionItemListVO(TDetectionItem detectionItem);

    /**
     * 根据条件查询检测项
     *
     * @param detectionItem
     * @return
     */
    List<DetectionItemVO> selectDetectionItemByPage(TDetectionItem detectionItem);

    /**
     * 根据模板id查询检测项列表
     *
     * @param templateId
     * @return
     */
    List<DetectionItemVO> setectUserDetectionByTemplateId(@Param("templateId") Long templateId);

    /**
     * 查询检测项数量
     */
    public Map<Object, Integer> detectionItemCountSize(TDetectionItem detectionItem);

    /**
     * 根据终端类型查询检测项列表
     *
     * @param terminalType 终端类型
     * @return 检测项列表
     */
    List<TDetectionItem> findByTerminalType(@Param("terminalType") Integer terminalType);

    /**
     *
     * @description 根据检测项编号查询检测项信息
     * <AUTHOR>
     * @date 2019/3/25
     * @param
     * @return java.util.List<cn.ijiami.detection.entity.TDetectionItem>
     */
    List<TDetectionItem> selectByItemNo(@Param("itemNo") String itemNo,@Param("terminalType")Integer terminalType);
    
    List<DetectionItemVO> selectByItemNoVO(@Param("itemNo") String itemNo,@Param("terminalType")Integer terminalType);
    
    TDetectionItem findDetectionItemInfo(@Param("itemNo")String itemNo,@Param("terminalType")Integer terminalType);
}