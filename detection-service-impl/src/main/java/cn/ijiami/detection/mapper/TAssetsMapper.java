package cn.ijiami.detection.mapper;

import java.util.Date;
import java.util.List;
import java.util.Map;

import cn.ijiami.detection.VO.statistics.AssetsTask;
import cn.ijiami.detection.VO.statistics.HomePageDailyStatistics;
import cn.ijiami.detection.entity.AssetsDetectionStatistics;
import cn.ijiami.detection.entity.TAssetsCount;
import cn.ijiami.detection.query.compliance.AssetsQuery;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

import cn.ijiami.detection.VO.AssetsListVO;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.framework.mybatis.IjiamiMapper;

/**
 * 资产数据mapper类
 *
 * <AUTHOR>
 */
public interface TAssetsMapper extends IjiamiMapper<TAssets> {

    public List<TAssetsCount> assetsTerminalTypeCount(TAssets tassets);

    public List<AssetsListVO> findAssetsList(TAssets tassets);

    public List<Map<String, Object>> findAssetsList1(Map map);

    /**
     * 逻辑删除
     *
     * @param id 主键
     */
    @Update(value = "update t_assets set is_delete = 1 where id = #{id}")
    void logicDeleteById(@Param("id") Long id);
    
    @Delete(value = "delete from t_assets where id = #{id}")
    void deleteById(@Param("id") Long id);

    /**
     * 逻辑删除
     *
     * @param id 主键
     */
//    @Update(value = "update t_assets set is_delete = 1 where id = #{id} and create_user_id=#{userId} ")
//    void deleteByIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);
    
    @Delete(value = "delete from t_assets where id = #{id} and create_user_id=#{userId} ")
    void deleteByIdAndUserId(@Param("id") Long id, @Param("userId") Long userId);

    /**
     * 修改脱壳后包地址
     *
     * @param shellIpaPath
     * @param id
     */
    @Update(value = "update t_assets set shell_ipa_path = #{shellIpaPath},is_have_packer = #{isHavePacker} where id = #{id} ")
    void updateById(@Param("shellIpaPath") String shellIpaPath, @Param("isHavePacker") Integer isHavePacker, @Param("id") Long id);

    void updateDumpZipUrlById(@Param("dumpZipUrl") String dumpZipUrl, @Param("isHavePacker") Integer isHavePacker, @Param("id") Long id);
    /**
     * 根据任务查询应用信息
     *
     * @param taskId 任务ID
     * @return
     */
    TAssets selectAssetByTaskId(@Param("taskId") Long taskId);

    /**
     * 根据任务文档id查询应用信息
     *
     * @param documentId 任务文档ID
     * @return
     */
    TAssets selectAssetByDocumentId(@Param("documentId") String documentId);

    TAssets findOneByMd5(@Param("md5") String md5);

    TAssets findOneByUserAndMd5(@Param("userId") Long userId, @Param("md5") String md5);

    TAssets findOneByUserAndName(@Param("userId") Long userId, @Param("name") String name, @Param("terminalType") TerminalTypeEnum terminalType);

    TAssets findOneByUserAndAppId(@Param("userId") Long userId, @Param("appId") String appId, @Param("terminalType") TerminalTypeEnum terminalType);

    List<TAssets> findByUserAndMd5(@Param("userId") Long userId, @Param("md5") String md5);

    List<TAssets> findAssetsListTest(@Param("terminalType") TerminalTypeEnum terminalType, @Param("startId") Integer startId, @Param("num") Integer num);

    TAssets getAssetByTaskId(@Param("taskId") Long taskId);

    TAssets getAssetIncludeDeletedByTaskId(@Param("taskId") Long taskId);

    int updateFunctionTypeById(@Param("id") Long id, @Param("functionType") String functionType);

    List<TAssets> detectionCountRank(@Param("userId") Long userId,
                                     @Param("terminalType") TerminalTypeEnum terminalType,
                                     @Param("startDate") Date startDate,
                                     @Param("endDate") Date endDate,
                                     @Param("detectionType") Integer detectionType,
                                     @Param("detectionStatus") Integer detectionStatus,
                                     @Param("assetsName") String assetsName,
                                     @Param("limit") Integer limit);

    List<AssetsDetectionStatistics> findSimpleByName(@Param("userId") Long userId,
                                                     @Param("terminalType") TerminalTypeEnum terminalType,
                                                     @Param("startDate") Date startDate,
                                                     @Param("endDate") Date endDate,
                                                     @Param("detectionType") Integer detectionType,
                                                     @Param("detectionStatus") Integer detectionStatus,
                                                     @Param("nameList") List<String> nameList);

    List<Long> findSameAssetsIdById(@Param("id") Long id);

    List<TAssets> assetsDetectionCountRank(@Param("userId") Long userId,
                                           @Param("terminalType") TerminalTypeEnum terminalType,
                                           @Param("startDate") Date startDate,
                                           @Param("endDate") Date endDate,
                                           @Param("detectionType") Integer detectionType,
                                           @Param("detectionStatus") Integer detectionStatus,
                                           @Param("assetsName") String assetsName);

    List<AssetsTask> assetsTask(@Param("userId") Long userId,
                                @Param("terminalType") TerminalTypeEnum terminalType,
                                @Param("startDate") Date startDate,
                                @Param("endDate") Date endDate,
                                @Param("detectionType") Integer detectionType,
                                @Param("assetsName") String assetsName);

    List<TAssets> selectByDate(@Param("startDate") String startDate, @Param("endDate") String endDate);

    Integer assetsDetectionCount(@Param("userId") Long userId,
                                           @Param("terminalType") TerminalTypeEnum terminalType,
                                           @Param("startDate") Date startDate,
                                           @Param("endDate") Date endDate,
                                           @Param("detectionType") Integer detectionType,
                                           @Param("packageNameList") List<String> packageNameList);


    List<TAssets> findByMd5AndPackage(@Param("packageNameList") List<String> packageNameList);


    List<HomePageDailyStatistics> assetsDailyDetection(@Param("userId") Long userId,
                                                       @Param("terminalType") TerminalTypeEnum terminalType,
                                                       @Param("startDate") Date startDate,
                                                       @Param("endDate") Date endDate,
                                                       @Param("packageNameList") List<String> packageNameList);


    Integer detectionAssetsCount(@Param("userId") Long userId,
                                 @Param("terminalType") TerminalTypeEnum terminalType,
                                 @Param("startDate") Date startDate,
                                 @Param("endDate") Date endDate,
                                 @Param("packageNameList") List<String> packageNameList);

    List<AssetsListVO> findUploadedAssets(AssetsQuery query);
}