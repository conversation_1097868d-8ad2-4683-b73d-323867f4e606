package cn.ijiami.detection.fastdfs;

import cn.ijiami.framework.file.context.FileContext;
import cn.ijiami.framework.file.service.impl.DefaultFastDfsFileService;

/**
 * 解决同一个类无法存在两个同类型实列对象
 *
 * <AUTHOR>
 * @date 2020-07-10 16:55
 */
public class SingleFastDfsFileService {

    private CustomFastDfsFileService instance;

    public CustomFastDfsFileService instance() {
        return instance;
    }

    public void init(String filePath, int diskHoldSize) {
        FileContext fileContext = new FileContext();
        fileContext.setFilePath(filePath);
        fileContext.setType("fdfs");
        fileContext.setDiskHoldSize(diskHoldSize);
        fileContext.setFilePath("");
        instance = new CustomFastDfsFileService(fileContext);
    }

}
