package cn.ijiami.detection.helper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import cn.ijiami.detection.config.IjiamiCommonProperties;
import cn.ijiami.detection.fastdfs.SingleFastDfsFileService;
import cn.ijiami.detection.miit.DetectPointManager;

/**
 * 应该工具相关
 *
 * <AUTHOR>
 * @date 2020/6/30 11:38
 **/
@Configuration
public class HelperConfiguration {

    @Autowired
    private IjiamiCommonProperties commonProperties;

    @Autowired
    private Environment environment;

    @Bean(initMethod = "init", destroyMethod = "destroy")
    public SpringMonitorHeart helperMonitorHeart() {
        SpringMonitorHeart springMonitorHeart = new SpringMonitorHeart();
        return springMonitorHeart;
    }

    @Bean
    public SingleFastDfsFileService singleFastDfsFileService() {
        SingleFastDfsFileService fastDfsFileService = new SingleFastDfsFileService();
        fastDfsFileService.init(commonProperties.getFilePath(), commonProperties.getDiskHoldSize());
        return fastDfsFileService;
    }

    @Bean(initMethod = "init")
    public DetectPointManager detectPointManager() {
        return new DetectPointManager();
    }
}
