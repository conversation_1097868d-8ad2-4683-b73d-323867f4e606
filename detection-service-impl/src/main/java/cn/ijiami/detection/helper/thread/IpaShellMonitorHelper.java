package cn.ijiami.detection.helper.thread;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.fasterxml.jackson.core.type.TypeReference;

import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TIpaShellRecord;
import cn.ijiami.detection.enums.HitShellDataTypeEnum;
import cn.ijiami.detection.enums.IosDynamicDetectionCmdEnum;
import cn.ijiami.detection.enums.IosHitShellCmdTypeEnum;
import cn.ijiami.detection.enums.PackerStatusEnum;
import cn.ijiami.detection.enums.ShellStatusEnum;
import cn.ijiami.detection.enums.StartIosHitShellStateEnum;
import cn.ijiami.detection.helper.SpringMonitorHeart;
import cn.ijiami.detection.idb.IdbDevice;
import cn.ijiami.detection.idb.IdbDeviceList;
import cn.ijiami.detection.idb.IdbDeviceStatusEnum;
import cn.ijiami.detection.idb.hitshell.GetIosHitShellDeviceListRequest;
import cn.ijiami.detection.idb.hitshell.StartIosHitShellRequest;
import cn.ijiami.detection.idb.hitshell.StopIosHitShellRequest;
import cn.ijiami.detection.idb.hitshell.response.StartIosHitShellResponse;
import cn.ijiami.detection.idb.hitshell.response.StopIosHitShellResponse;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.HttpUtils;
import cn.ijiami.detection.websocket.idb.HitShellSocketClient;
import cn.ijiami.framework.kit.utils.UuidUtil;

/**
 * 砸壳服务助手
 *
 * <AUTHOR>
 * @date 2020/6/30 11:30
 **/
public class IpaShellMonitorHelper {
    private static final Logger logger = LoggerFactory.getLogger(IpaShellMonitorHelper.class);

    private IpaShellMonitorHelper() {
    }

    private static volatile IpaShellMonitorHelper instance;
    private Thread monitorThread;
    private volatile boolean toStop = false;
    /**
     * 线程休眠时间,单位：秒
     */
    private static int duration = 10;
    /**
     * 最大并发数
     */
    private static int maxShellNum = 1;

    /**
     * 最大尝试砸壳次数
     */
    private static int maxRetryShellCount = 5;

    /**
     * 默认超时时长：8分钟
     */
    private static int defaultTimeout = 60 * 30;

    /**
     * 请求idb超时时长：默认60秒
     */
    private static int idbTimeout = 60;

    /**
     * 最大重试次数
     */
    private static final int MAX_RETRY_COUNT = 3;

    private HitShellSocketClient hitShellSocketClient;

    public static IpaShellMonitorHelper getInstance() {
        if (instance == null) {
            synchronized (IpaShellMonitorHelper.class) {
                if (instance == null) {
                    instance = new IpaShellMonitorHelper();
                    instance.init();
                }
            }
        }
        return instance;
    }

    private void init() {
        String num = SpringMonitorHeart.commonProperties.getProperty("ijiami.shell.server.num");
        if (StringUtils.isNotEmpty(num)) {
            IpaShellMonitorHelper.maxShellNum = Integer.parseInt(num);
        }
        String timeout = SpringMonitorHeart.commonProperties.getProperty("ijiami.shell.server.fail.second");
        if (StringUtils.isNotEmpty(timeout)) {
            IpaShellMonitorHelper.defaultTimeout = Integer.parseInt(timeout);
        }
        String idbTimeout = SpringMonitorHeart.commonProperties.getProperty("ijiami.ios.remote.tool.timeout");
        if (StringUtils.isNotEmpty(idbTimeout)) {
            IpaShellMonitorHelper.idbTimeout = Integer.parseInt(idbTimeout);
        }
        String url = SpringMonitorHeart.commonProperties.getProperty("ijiami.shell.server.url");
        if (StringUtils.isNotBlank(url)) {
            try {
                hitShellSocketClient = new HitShellSocketClient(new URI(url));
                hitShellSocketClient.setExecutorServiceHelper(SpringMonitorHeart.executorServiceHelper);
                hitShellSocketClient.connect();
            } catch (URISyntaxException e) {
                logger.error("砸壳服务WebSocket连接失败", e);
            }
        }
    }

    /**
     * 启动砸壳服务进程
     */
    public void start() {
        // 创建线程
        Runnable run = () -> {
            while (!toStop && Objects.nonNull(hitShellSocketClient)) {
                try {
                    TimeUnit.SECONDS.sleep(duration);

                    logger.info("实时更新ios砸壳进度>>>>>>>>>>>");
                    List<TIpaShellRecord> recordList = SpringMonitorHeart.ipaShellRecordMapper.selectAllShellRecordByStatus();
                    if(recordList != null && recordList.size()>0) {
                        recordList.stream().forEach(r ->{
                            try {
                                if(r.getTaskId() != null) {
                                    SpringMonitorHeart.shellSendMessage.sendShellMessage(r.getTaskId(), r.getStatus());
                                    logger.info("发送砸壳任务taskId={}进度成功",r.getTaskId());
                                }
                            } catch (Exception e) {
                                e.getMessage();
                            }
                        });
                    }
                    // 清理砸壳超时的应用变更任务状态
                    //IpaShellMonitorHelper.cleanFailShell();
                    List<TIpaShellRecord> runShell = SpringMonitorHeart.ipaShellRecordMapper.selectShellRecordByStatus(ShellStatusEnum.RUN.getValue());
                    int runShellNum = runShell.size();
//                    List<TIpaShellRecord> timeoutShell = runShell.stream().filter(IpaShellMonitorHelper::checkTimeout).collect(Collectors.toList());
//                    int clientNum = 0;
                    // 超时砸壳记录清理
//                    if (CollectionUtils.isNotEmpty(timeoutShell) && timeoutShell.size() > 0) {
//                        cleanFailShell(timeoutShell);
//                        clientNum = timeoutShell.size();
//                    }
                    // 计算当前砸壳数
//                    runShellNum = runShellNum - clientNum;
                    // 判断是否有检测中的应用
                    if (runShellNum < maxShellNum) {
                        List<Long> shellIds = SpringMonitorHeart.ipaShellRecordMapper.selectByStatus(ShellStatusEnum.WAIT.getValue(), maxRetryShellCount);
                        if (CollectionUtils.isEmpty(shellIds)) {
                            continue;
                        }
                        
                        // 调用砸壳服务
                        for (Long shellId : shellIds) {
                        	
                        	LinkedList<IdbDevice> deviceList = null;
                        	
                        	//20220913增加判断如果ipa是无壳包可以直接下发砸壳，不需要判断手机是否存在
                        	TIpaShellRecord shellRecord = SpringMonitorHeart.ipaShellRecordMapper.selectByPrimaryKey(shellId);
                            TAssets assets = SpringMonitorHeart.assetsMapper.selectByPrimaryKey(shellRecord.getAssetId());
                            if(assets == null) {
                            	logger.info("砸壳记录中资产ID不存在{}",shellId);
                                continue;
                            }
                            if(PackerStatusEnum.SHELL_LESS.getValue() != assets.getIsHavePacker() && PackerStatusEnum.RESIGN.getValue() != assets.getIsHavePacker()){
                            	IdbDeviceList idbDeviceList = getHitShellDevice();
                                if (Objects.isNull(idbDeviceList) || CollectionUtils.isEmpty(idbDeviceList.getDeviceList())) {
                                    logger.info("没有设备");
                                    continue;
                                }
                                deviceList = idbDeviceList.getDeviceList()
                                        .stream()
                                        .filter(d -> d.getDeviceState() == IdbDeviceStatusEnum.UNUSED.value)
                                        .collect(Collectors.toCollection(LinkedList::new));
                            	
                            	if (deviceList.isEmpty()) {
                                    logger.info("设备已经用完");
                                    break;
                                }
                            }
                            int lockShell = SpringMonitorHeart.ipaShellRecordMapper.updateStatusById(shellId, ShellStatusEnum.WAIT.getValue(), ShellStatusEnum.RUN.getValue());
                            if (lockShell < 1) {
                                continue;
                            }
                            TIpaShellRecord record = null;
                            try {
                                record = startIpaShellServerByShellId(shellId, deviceList==null?null:deviceList.removeFirst());
                                
                                // 已经成功砸过壳的
                                if (record.getStatus() == ShellStatusEnum.SUCCESS.getValue()) {
                                    // 变更动态检测状态
                                    SpringMonitorHeart.shellSendMessage.updateDynamicStatusByShellStatus(record.getTaskId(), record.getStatus(),record.getResultJson());
                                    // 发送砸壳进度
                                    SpringMonitorHeart.shellSendMessage.sendShellMessage(record.getTaskId(), record.getStatus(), record.getResultJson());
                                }
                                // 更新成功，并且http请求成功
                                if (record.getStatus() == ShellStatusEnum.RUN.getValue()) {
                                    // 增加正在砸壳的数量
                                    runShellNum++;
                                    // 变更动态检测状态
                                    SpringMonitorHeart.shellSendMessage.updateDynamicStatusByShellStatus(record.getTaskId(), record.getStatus(),null);
                                    // 发送砸壳进度
                                    SpringMonitorHeart.shellSendMessage.sendShellMessage(record.getTaskId(), record.getStatus());
                                }
                            } catch (Exception e) {
                                if (record == null) {
                                    record = SpringMonitorHeart.ipaShellRecordMapper.selectByPrimaryKey(shellId);
                                }
                                logger.error("Ipa Shell monitor helper error shellId={} taskId={}", shellId, record.getTaskId(), e);
                                // 变更动态检测状态
                                SpringMonitorHeart.shellSendMessage.updateDynamicStatusByShellStatus(record.getTaskId(), record.getStatus(),null);
                                // 通知前端任务失败
                                SpringMonitorHeart.shellSendMessage.sendShellMessage(record.getTaskId(), record.getStatus());
                            }
                            // 当前砸壳数等于最大砸壳数终止
                            if (runShellNum == maxShellNum) {
                                break;
                            }
                        }
                    }
                } catch (Exception e) {
                	e.getMessage();
                    logger.error("Ipa Shell monitor helper error:", e);
                }
            }
        };
        monitorThread = new Thread(run);
        monitorThread.setName("ipa脱壳监控-IpaShellMonitorHelper");
        monitorThread.setDaemon(true);
        monitorThread.start();
        logger.debug("ipa脱壳监控-IpaShellMonitorHelper启动完成");
    }

    /**
     * 获取砸壳服务可使用设备
     *
     * @param url
     * @return
     */
    private static int getHitShellDeviceNum(String url) {
        String result = HttpUtils.post(null, url);
        if (StringUtils.isNotBlank(result)) {
            try {
                return com.alibaba.fastjson.JSONObject.parseArray(result).size();
            } catch (Exception e) {
                logger.info("ipa脱壳，获取砸壳可用服务异常，请求地址：{}，返回结果：{}", url, result);
                return 1;
            }
        }
        return 1;
    }

    /**
     * 超时判断
     *
     * @param record
     * @return
     */
    private static boolean checkTimeout(TIpaShellRecord record) {
        if (record.getUpdateTime() == null) {
            return true;
        }
        // 包体积越大，超时时间越长
        long hitShellTimeout = getHitShellTimeout(record);
        // 计算相差多少秒
        long interval = ((new Date()).getTime() - record.getUpdateTime().getTime()) / 1000;
        return interval > hitShellTimeout;
    }

    public static long getHitShellTimeout(TIpaShellRecord record) {
        long appSizeMB = record.getAppSize() / 1024;
        // 包体积越大，超时时间越长
        return defaultTimeout + (appSizeMB / 300) * 180;
    }

    /**
     * 通过shellId执行砸壳
     *
     * @param shellId
     * @return
     */
    public TIpaShellRecord startIpaShellServerByShellId(Long shellId, IdbDevice idbDevice) {
        TIpaShellRecord record = SpringMonitorHeart.ipaShellRecordMapper.selectByPrimaryKey(shellId);
        // 启动前校验是否被手动终止
        if (record.getStatus() == ShellStatusEnum.STOP.getValue()) {
            return record;
        }
        return startIpaShellServer(record, idbDevice);
    }

    /**
     * 启动砸壳流程
     *
     * @param record
     * @return
     */
    private TIpaShellRecord startIpaShellServer(TIpaShellRecord record, IdbDevice idbDevice) {
        // 查询资产信息
        TAssets assets = SpringMonitorHeart.assetsMapper.selectByPrimaryKey(record.getAssetId());
        // 判断是否已经砸过壳
        if (assets.getIsHavePacker() == PackerStatusEnum.SHELLING.getValue()
                || assets.getIsHavePacker() == PackerStatusEnum.RESIGN.getValue()) {
            record.setDescp(assets.getIsHavePacker() == PackerStatusEnum.RESIGN.getValue() ? "该应用已经重签成功，不再进行重签" : "该应用已经砸壳成功，不再进行砸壳");
            record.setResultJson(assets.getShellIpaPath());
            record.setStatus(ShellStatusEnum.SUCCESS.getValue());
            record.setUpdateTime(new Date());
            // 更新砸壳记录
            SpringMonitorHeart.ipaShellRecordMapper.updateByPrimaryKey(record);
            return record;
        }
        int countShell = Objects.isNull(record.getShellCount()) ? 0 : record.getShellCount();
        record.setStatus(ShellStatusEnum.RUN.getValue());
        record.setShellCount(countShell + 1);
        record.setDeviceId((idbDevice==null?null:idbDevice.getDeviceId()));
        // 更新砸壳的时间
        record.setUpdateTime(new Date());
        record.setTestflightUrl(assets.getTestflightUrl()==null? "" : assets.getTestflightUrl());
        
        SpringMonitorHeart.ipaShellRecordMapper.updateByPrimaryKey(record);
        // 调用砸壳服务
        startHitShell(record, (idbDevice==null?null:idbDevice.getDeviceId()));
        return record;
    }

    /**
     * 调用砸壳终止接口
     *
     * @param taskId 任务ID
     * @return
     */
    public Integer stopIpaShellServer(Long taskId) {
        // 构建请求砸壳服务参数
        // 调用终止砸壳服务
        try {
            TIpaShellRecord record = SpringMonitorHeart.ipaShellRecordMapper.selectOneByTaskId(taskId);
            if (Objects.isNull(record)) {
                logger.info("砸壳终止服务失败，砸壳记录不存在：{}", taskId);
                return null;
            }
            if (StringUtils.isBlank(record.getDeviceId())) {
                logger.info("砸壳终止服务失败，设备记录不存在：{}", taskId);
                return null;
            }
            StopIosHitShellResponse response = stopHitShell(taskId.toString(), record.getDeviceId());
            logger.info("砸壳终止服务，请求参数：{}", taskId);
            if (Objects.isNull(response)) {
                return null;
            }
            // 调用成功，返回状态
            return response.getTaskState();
        } catch (Exception e) {
            logger.error("砸壳终止服务，调用失败 {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 清理超时的数据
     */
    public static void cleanFailShell() {
        // 查询失效的砸壳记录
        List<TIpaShellRecord> records = SpringMonitorHeart.ipaShellRecordMapper.selectFailRecordByTime(defaultTimeout).stream()
                .filter(IpaShellMonitorHelper::checkTimeout).collect(Collectors.toList());
        cleanFailShell(records);
        List<TIpaShellRecord> retryOverRecords = SpringMonitorHeart.ipaShellRecordMapper.selectWaitRecordOverShellCount(maxRetryShellCount);
        cleanFailShell(retryOverRecords);
    }

    /**
     * 清理超时的数据
     */
    public static void cleanFailShell(List<TIpaShellRecord> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        // 清理超时并推送砸壳失败信息
        for (TIpaShellRecord record : records) {
            record.setStatus(ShellStatusEnum.FAIL.getValue());
            record.setDescp(getHitShellName(record) + "回调超时");
            record.setUpdateTime(new Date());
            // 变更动态检测状态
            SpringMonitorHeart.shellSendMessage.updateDynamicStatusByShellStatus(record.getTaskId(), record.getStatus(),record.getDescp());
            // 推送砸壳回调超时，砸壳失败信息
            SpringMonitorHeart.shellSendMessage.sendShellMessage(record.getTaskId(), record.getStatus(), record.getDescp());
            // 更新脱壳记录
            SpringMonitorHeart.ipaShellRecordMapper.updateByPrimaryKey(record);
        }

    }

    /**
     * 停止应用风险监测 线程
     */
    public void toStop() {
        toStop = true;
        monitorThread.interrupt();
        try {
            monitorThread.join();
            logger.debug("ipa脱壳监控-IpaShellMonitorHelper，销毁完成");
        } catch (InterruptedException e) {
            logger.error(e.getMessage(), e);
        }
    }


    public int getMaxRetryShellCount() {
        return maxRetryShellCount;
    }

    private void startHitShell(TIpaShellRecord record, String deviceId) {
        StartIosHitShellRequest request = CommonUtil.jsonToBean(record.getRequestParam(), new TypeReference<StartIosHitShellRequest>() {
        });
        request.getRequestParam().setUploadUrl(SpringMonitorHeart.commonProperties.getProperty("ijiami.ios.remote.tool.uploadUrl"));
        request.getRequestParam().setCmdType(IosHitShellCmdTypeEnum.START_HIT_SHELL.getValue());
        request.getRequestParam().setDeviceId(deviceId == null ?"" : deviceId);
        String requestId = request.getRequestParam().getBusinessId() + IosHitShellCmdTypeEnum.START_HIT_SHELL.getValue();
        hitShellSocketClient.sendMessage(CommonUtil.beanToJson(request), requestId,
                new SyncHitShellResponseListener(TimeUnit.SECONDS.toMillis(getHitShellTimeout(record))) {

                    @Override
                    public void onResponse(String responseMessage) {
                        StartIosHitShellResponse response = CommonUtil.jsonToBean(responseMessage, new TypeReference<StartIosHitShellResponse>() {
                        });
                        if (Objects.isNull(response)) {
                            return;
                        }
                        // 重新进入排队
                        if (Objects.nonNull(response.getCode()) && response.getCode() != HttpStatus.SC_OK) {
                            TIpaShellRecord currentRecord = SpringMonitorHeart.ipaShellRecordMapper.selectOneByTaskId(record.getTaskId());
                            if (currentRecord.getStatus() == ShellStatusEnum.RUN.getValue()) {
                                retry(getCallServerName(), response.getMessage());
                            }
                        } else if (!StartIosHitShellStateEnum.SUCCESS.getValue().equals(response.getState())) {
                            TIpaShellRecord currentRecord = SpringMonitorHeart.ipaShellRecordMapper.selectOneByTaskId(record.getTaskId());
                            if (currentRecord.getStatus() == ShellStatusEnum.RUN.getValue()) {
                                // 设备原因的失败，重新排队
                                if ("设备".contains(response.getMessage())) {
                                    retry(getCallServerName(), "设备原因");
                                } else {
                                    record.setStatus(ShellStatusEnum.FAIL.getValue());
                                    record.setDescp(getCallServerName() + "执行失败:" + response.getMessage());
                                    logger.info("Ipa Shell monitor helper info: " + getCallServerName() + "执行失败，修改状态");
                                    // 变更动态检测状态
                                    SpringMonitorHeart.shellSendMessage.updateDynamicStatusByShellStatus(record.getTaskId(), record.getStatus(), record.getDescp());
                                    SpringMonitorHeart.shellSendMessage.sendShellMessage(record.getTaskId(), record.getStatus(), record.getDescp());
                                    SpringMonitorHeart.ipaShellRecordMapper.updateByPrimaryKey(record);
                                }
                            }
                        } else {
                            logger.info(getCallServerName() + "调用成功");
                        }
                    }

                    @Override
                    public void onError() {
                        super.onError();
                        TIpaShellRecord currentRecord = SpringMonitorHeart.ipaShellRecordMapper.selectOneByTaskId(record.getTaskId());
                        if (currentRecord.getStatus() == ShellStatusEnum.RUN.getValue()) {
                            retry(getCallServerName(), "调用错误");
                        }
                    }

                    @Override
                    public void onTimeout() {
                        super.onTimeout();
                        TIpaShellRecord currentRecord = SpringMonitorHeart.ipaShellRecordMapper.selectOneByTaskId(record.getTaskId());
                        if (currentRecord.getStatus() == ShellStatusEnum.RUN.getValue()) {
                            retry(getCallServerName(), "调用超时");
                        }
                    }

                    private void retry(String callName, String message) {
                        int countShell = Objects.isNull(record.getShellCount()) ? 0 : record.getShellCount();
                        if (countShell < MAX_RETRY_COUNT) {
                            record.setStatus(ShellStatusEnum.WAIT.getValue());
                            logger.info("Ipa Shell monitor helper info: " + callName + "，重新进行排队");
                            SpringMonitorHeart.ipaShellRecordMapper.updateByPrimaryKey(record);
                        } else {
                            record.setStatus(ShellStatusEnum.FAIL.getValue());
                            record.setDescp(callName + " 执行失败：" + message);
                            logger.info("Ipa Shell monitor helper info: " + callName + " " + message + "，重试次数已经超过 " + MAX_RETRY_COUNT);
                            SpringMonitorHeart.ipaShellRecordMapper.updateByPrimaryKey(record);
                            // 变更动态检测状态
                            SpringMonitorHeart.shellSendMessage.updateDynamicStatusByShellStatus(record.getTaskId(), record.getStatus(), record.getDescp());
                            SpringMonitorHeart.shellSendMessage.sendShellMessage(record.getTaskId(), record.getStatus(), record.getDescp());
                        }
                    }

                    private String getCallServerName() {
                        return getHitShellName(record);
                    }

                });
    }

    private static String getHitShellName(TIpaShellRecord record) {
        StartIosHitShellRequest request = CommonUtil.jsonToBean(record.getRequestParam(), new TypeReference<StartIosHitShellRequest>() {
        });
        if (request == null) {
            return "砸壳";
        }
        return request.getRequestParam().getDataType() == HitShellDataTypeEnum.APP_ID.getValue() ? "砸壳" : "重签";
    }

    private StopIosHitShellResponse stopHitShell(String businessId, String deviceId) {
        StopIosHitShellRequest request = new StopIosHitShellRequest();
        StopIosHitShellRequest.Params params = new StopIosHitShellRequest.Params();
        params.setCmdType(IosHitShellCmdTypeEnum.STOP_HIT_SHELL.getValue());
        params.setDeviceId(deviceId);
        params.setBusinessId(businessId);
        request.setRequestParam(params);
        String requestId = businessId + IosHitShellCmdTypeEnum.STOP_HIT_SHELL.getValue();
        StopHitShellResponseListener listener = new StopHitShellResponseListener(TimeUnit.SECONDS.toMillis(30));
        hitShellSocketClient.sendMessage(CommonUtil.beanToJson(request), requestId, listener);
        return listener.getResponse();
    }

    static class GetHitShellDeviceListResponseListener extends AsyncHitShellResponseListener<IdbDeviceList> {

        public GetHitShellDeviceListResponseListener(long timeoutMillis) {
            super(timeoutMillis);
        }

        @Override
        IdbDeviceList processResponseMessage(String responseMessage) {
            return CommonUtil.jsonToBean(responseMessage, new TypeReference<IdbDeviceList>() {
            });
        }
    }

    static class StopHitShellResponseListener extends AsyncHitShellResponseListener<StopIosHitShellResponse> {

        public StopHitShellResponseListener(long timeoutMillis) {
            super(timeoutMillis);
        }

        @Override
        StopIosHitShellResponse processResponseMessage(String responseMessage) {
            return CommonUtil.jsonToBean(responseMessage, new TypeReference<StopIosHitShellResponse>() {
            });
        }
    }

    /**
     * 异步监听器，线程不等待返回结果就继续往下走
     */
    static abstract class SyncHitShellResponseListener implements HitShellSocketClient.SendMessageListener {

        private final long timeoutMillis;

        public SyncHitShellResponseListener(long timeoutMillis) {
            this.timeoutMillis = timeoutMillis;
        }

        @Override
        public void onTimeout() {
        }

        @Override
        public void onError() {

        }

        @Override
        public long getTimeoutMillis() {
            return timeoutMillis;
        }
    }

    /**
     * 同步监听器，线程等待返回结果后才会继续往下走
     *
     * @param <T>
     */
    static abstract class AsyncHitShellResponseListener<T> implements HitShellSocketClient.SendMessageListener {

        private final CountDownLatch countDownLatch;
        private final long timeoutMillis;
        private T responseData;

        public AsyncHitShellResponseListener(long timeoutMillis) {
            this.timeoutMillis = timeoutMillis;
            this.countDownLatch = new CountDownLatch(1);
        }

        @Override
        public long getTimeoutMillis() {
            return timeoutMillis;
        }

        @Override
        public void onResponse(String responseMessage) {
            logger.info("砸壳或重签调用 返回结果");
            responseData = processResponseMessage(responseMessage);
            countDownLatch.countDown();
        }

        public T getResponse() {
            try {
                if (countDownLatch.await(timeoutMillis, TimeUnit.MILLISECONDS)) {
                    return responseData;
                } else {
                    return null;
                }
            } catch (InterruptedException e) {
                logger.error("等待砸壳或重签数据 失败", e);
                return null;
            }
        }

        abstract T processResponseMessage(String responseMessage);

        @Override
        public void onTimeout() {
            logger.info("砸壳或重签调用 超时");
            countDownLatch.countDown();
        }

        @Override
        public void onError() {
            logger.info("砸壳或重签调用 失败");
            countDownLatch.countDown();
        }
    }

    public IdbDeviceList getHitShellDevice() {
        GetHitShellDeviceListResponseListener listener = new GetHitShellDeviceListResponseListener(TimeUnit.SECONDS.toMillis(idbTimeout));
        GetIosHitShellDeviceListRequest request = new GetIosHitShellDeviceListRequest();
        request.setRequestid(UuidUtil.uuid());
        request.setCmdType(IosHitShellCmdTypeEnum.GET_DEVICES.getValue());
        hitShellSocketClient.sendMessage(CommonUtil.beanToJson(request), request.getRequestid() + request.getCmdType(), listener);
        return listener.getResponse();
    }
    
    public IdbDeviceList getIosDetectionDevice() {
        GetHitShellDeviceListResponseListener listener = new GetHitShellDeviceListResponseListener(TimeUnit.SECONDS.toMillis(idbTimeout));
        GetIosHitShellDeviceListRequest request = new GetIosHitShellDeviceListRequest();
        request.setRequestid(UuidUtil.uuid());
        request.setCmdType(IosDynamicDetectionCmdEnum.GET_DEVICE_LIST.getValue());
        hitShellSocketClient.sendMessage(CommonUtil.beanToJson(request), request.getRequestid() + request.getCmdType(), listener);
        return listener.getResponse();
    }

    public long getDefaultTimeout() {
        return defaultTimeout;
    }

}
