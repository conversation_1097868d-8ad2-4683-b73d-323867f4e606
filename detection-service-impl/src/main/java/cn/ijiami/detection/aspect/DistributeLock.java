package cn.ijiami.detection.aspect;

import cn.ijiami.detection.enums.lock.LockFailureAction;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import static cn.ijiami.detection.constant.DistributedLockConstant.*;

@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface DistributeLock {

    /**
     * key的前缀
     * @return
     */
    String keyPrefix();

    /**
     * key的变量获取，使用Spring EL表达式解析
     * @return
     */
    String keyValue();

    /**
     * 锁超时时间
     * @return
     */
    long timeoutMillis() default DEFAULT_TIMEOUT_MILLIS;

    /**
     * 使用的分布式锁服务名，默认使用redis
     * @return
     */
    String serverName() default "'redisDistributedLock'";

    /**
     * 尝试获取次数
     * @return
     */
    int retryTimes() default DEFAULT_RETRY_TIMES;

    /**
     * 尝试获取的间隔时间
     * @return
     */
    long sleepMillis() default DEFAULT_SLEEP_TIME_MILLIS;


    /**
     * 尝试获取锁失败之后的动作
     * @return
     */
    LockFailureAction lockFailureAction() default LockFailureAction.RETRY;

}
