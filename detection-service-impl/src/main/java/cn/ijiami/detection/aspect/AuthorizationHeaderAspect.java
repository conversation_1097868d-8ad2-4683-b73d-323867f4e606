package cn.ijiami.detection.aspect;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.assertj.core.util.Sets;
import org.springframework.stereotype.Component;

import javax.servlet.FilterChain;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AuthorizationHeaderAspect.java
 * @Description 禁止OAuth2AuthenticationProcessingFilter处理某些请求，这些请求的header中包含Authentication，如果被处理会导致401
 * @createTime 2023年06月06日 15:50:00
 */
@Aspect
@Component
public class AuthorizationHeaderAspect {

    private static final Set<String> IGNORE_URLS = Sets.set("/idb/interact/", "/ai/access/");

    @Pointcut("execution(* org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationProcessingFilter.doFilter(..))")
    public void securityOauth2DoFilter() {
    }

    @Around("securityOauth2DoFilter()")
    public void skipNotCustom(ProceedingJoinPoint joinPoint) throws Throwable {
        Object[] args = joinPoint.getArgs();
        if (args == null || args.length != 3 || !(args[0] instanceof HttpServletRequest && args[1] instanceof javax.servlet.ServletResponse && args[2] instanceof FilterChain)) {
            joinPoint.proceed();
            return;
        }
        HttpServletRequest request = (HttpServletRequest) args[0];
        String uri = request.getRequestURI();
        if (IGNORE_URLS.stream().noneMatch(uri::contains)) {
            joinPoint.proceed();
        } else {
            ((FilterChain) args[2]).doFilter((ServletRequest) args[0], (ServletResponse) args[1]);
        }
    }
}
