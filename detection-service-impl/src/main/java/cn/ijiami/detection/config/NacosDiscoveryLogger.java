package cn.ijiami.detection.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationRunner;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName NacosDiscoveryLogger.java
 * @Description TODO
 * @createTime 2025年06月18日 18:48:00
 */
@Configuration
@Slf4j
public class NacosDiscoveryLogger {

    @Bean
    public ApplicationRunner logDiscoveredServices(DiscoveryClient discoveryClient) {
        return args -> {
            log.info("===== Nacos 注册服务列表 =====");
            discoveryClient.getServices().forEach(serviceName -> {
                log.info("服务名: {}", serviceName);
                discoveryClient.getInstances(serviceName).forEach(instance -> {
                    log.info("  实例: {}:{} (元数据: {})",
                            instance.getHost(),
                            instance.getPort(),
                            instance.getMetadata());
                });
            });
        };
    }
}