package cn.ijiami.detection.interceptors;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName FeignRequestInterceptor.java
 * @Description feign请求拦截器
 * @createTime 2025年06月18日 18:37:00
 */
@Slf4j
public class FeignRequestInterceptor implements RequestInterceptor {
    @Override
    public void apply(RequestTemplate template) {
        log.info("Feign Request URL: {}{}", template.feignTarget().url(), template.url());
        log.info("Feign Request Method: {}", template.method());
        log.info("Feign Request Headers: {}", template.headers());
    }
}
