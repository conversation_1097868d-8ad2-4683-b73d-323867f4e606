package cn.ijiami.detection.websocket;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.converter.MessageConverter;
import org.springframework.messaging.handler.invocation.HandlerMethodArgumentResolver;
import org.springframework.messaging.handler.invocation.HandlerMethodReturnValueHandler;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketTransportRegistration;
import org.springframework.web.socket.server.standard.ServletServerContainerFactoryBean;

import javax.websocket.server.ServerContainer;
import java.util.List;

@Configuration
@EnableWebSocketMessageBroker
public class MyWebSocketMessageBrokerConfig implements WebSocketMessageBrokerConfigurer {

    @Value("${project.ignoreNullWsContainer:false}")
    private boolean ignoreNullWebSocketContainer;

    @Bean
    public ServletServerContainerFactoryBean createServletServerContainerFactoryBean() {
        if (ignoreNullWebSocketContainer) {
            return null;
        }
        ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
        // 增加消息体缓存大小，解决前端SockJS+Stomp发送大消息的时候，会根据stomp协议切分开几条发，后面几条消息后台缓存池不够无法接收完整而断开。
        container.setMaxTextMessageBufferSize(512 * 1024);
        container.setMaxBinaryMessageBufferSize(512 * 1024);
        return container;
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry stompEndpointRegistry) {

    }

    @Override
    public void configureWebSocketTransport(WebSocketTransportRegistration registration) {
        // 增大WebSocket的消息大小限制
        registration.setMessageSizeLimit(16 * 1024 * 1024); // default : 64 * 1024
        registration.setSendTimeLimit(20 * 10000); // default : 10 * 10000
        registration.setSendBufferSizeLimit(3 * 512 * 1024);
    }

    @Override
    public void configureClientInboundChannel(ChannelRegistration channelRegistration) {
        channelRegistration.taskExecutor()
                .corePoolSize(Runtime.getRuntime().availableProcessors() * 2)
                .maxPoolSize(Runtime.getRuntime().availableProcessors() * 4)
                .keepAliveSeconds(60)
                .queueCapacity(100000);
    }

    @Override
    public void configureClientOutboundChannel(ChannelRegistration channelRegistration) {

    }

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> list) {

    }

    @Override
    public void addReturnValueHandlers(List<HandlerMethodReturnValueHandler> list) {

    }

    @Override
    public boolean configureMessageConverters(List<MessageConverter> list) {
        return false;
    }

    @Override
    public void configureMessageBroker(MessageBrokerRegistry messageBrokerRegistry) {

    }
}
