package cn.ijiami.detection.miit.point.harmony.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint120301;

/**
 * 在申请打开可收集个人信息的权限，或申请收集用户身份证号、银行账号、行踪轨迹等个人敏感信息时，未同步告知用户其目的，或者目的不明确、难以理解；
 *
 * 未发现风险：
 * 1、申请系统权限授权前，已检测到对应权限已在权限弹窗前已有相关文字内容描述
 * 2、没有权限申请
 * 发现风险：
 * 1、申请系统权限授权前，未检测到对应权限已在权限弹窗前有相关文字内容描述（存在1个即发现风险）
 */
@EnableDetectPoint
public class DetectPoint40120301 extends DetectPoint120301 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }
    
}
