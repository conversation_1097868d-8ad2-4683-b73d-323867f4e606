package cn.ijiami.detection.miit.point.android.law41391;

import cn.ijiami.detection.helper.DetectPoint20102Helper;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20102;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350401.java
 * @Description
 * App拒绝或撤回App个人信息收集、权限申请或业务功能使用的同意时是否强制退出或者关闭App
 * 1. App拒绝或撤回App个人信息收集、权限申请或业务功能使用的同意时是否强制退出或者关闭App
 * 判断规则：
 * 当用户拒绝或撤回App个人信息收集、权限申请或业务功能使用的同意时，不应强制退出或者关闭App
 * 发现风险：
 * 建议APP明示用到【权限名称，多项时，用“、”隔开】等权限的所有服务场景和必要性。用户拒绝授权后，与授权不相关的服务应该不受影响，不能出现用户拒绝授权应用整体退出或关闭的情况
 */
@EnableDetectPoint
public class DetectPoint350401 extends AbstractDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = DetectPoint20102Helper.doDetect(commonDetectInfo, customDetectInfo);;
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE) {
            String actionTypeNames = String.join("、", detectResult.getRequestPermissionNames());
            String executorName = executor(commonDetectInfo);
            detectResult.setConclusion(buildSequenceText(
                    String.format("%s运行时，向用户索取当前服务场景未使用到的%s等权限，且用户拒绝授权后，应用退出或关闭（应用陷入弹窗循环，无法正常使用）。",
                            executorName, actionTypeNames)
            ));
            detectResult.setSuggestion(buildSequenceText(
                    String.format("建议%s明示用到%s等权限的所有服务场景和必要性。用户拒绝授权后，与授权不相关的服务应该不受影响，不能出现用户拒绝授权应用整体退出或关闭的情况",
                            executorName, actionTypeNames)));
            return detectResult;
        }
        return detectResult;
    }

}
