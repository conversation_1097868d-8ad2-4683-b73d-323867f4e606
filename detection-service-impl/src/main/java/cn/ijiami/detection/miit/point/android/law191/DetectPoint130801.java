package cn.ijiami.detection.miit.point.android.law191;

import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.*;
import cn.ijiami.detection.miit.enums.MiitDataTypeEnum;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import cn.ijiami.detection.utils.CommonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 以欺诈、诱骗等不正当方式误导用户同意收集个人信息或打开可收集个人信息的权限，如故意欺瞒、掩饰收集使用个人信息的真实目的；
 *
 * 检测规则
 * 发现风险：
 * 1、检测出现【抽奖、红包】页面，点击，弹出权限申请窗口；
 * 2、通讯抓包数据代码片度内容与通讯录内容完全一直（预设通讯录内容）
 *
 * 数据展现
 * 发现风险：
 * 【无关隐私政策】
 * 1、检测出现【抽奖、红包】页面，点击，弹出权限申请窗口；（遍历流程中相关的截图【抽奖、红包】页面、权限截图）
 * 2、通讯抓包数据代码片度内容与通讯录内容完全一直（展示抓包数据：行为阶段、触发时间、IP、域名、端口号、地理位置、代码片段）
 *
 * 检测结论
 * 发现风险：
 * 1、App存在以欺诈、诱骗等不正当方式误导用户打开可收集个人信息的权限行为
 *
 * 未发现风险：
 * 1、该检测项未发现风险
 *
 */
@EnableDetectPoint
public class DetectPoint130801 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        Set<String> screenshots = new HashSet<>();
        detectResult.setScreenshots(screenshots);
        detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        checkRedEnvelopeUi(commonDetectInfo, detectResult);
        // 出现cookie传输个人信息
        List<TPrivacySensitiveWord> sensitiveWordList = getSensitiveWords(commonDetectInfo)
                .stream()
                .filter(s -> containsAddressBook(customDetectInfo, s.getCode()))
                .collect(Collectors.toList());
        if (!sensitiveWordList.isEmpty()) {
            detectResult.setSensitiveWordResult(sensitiveWordList.stream().map(this::buildActionNetwork).collect(Collectors.toList()));
        }
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE) {
            detectResult.setConclusion(buildSequenceTextFormat("%s存在以欺诈、诱骗等不正当方式误导用户打开可收集个人信息的权限行为", executor(commonDetectInfo)));
        } else {
            detectResult.setConclusion("该检测项未发现风险");
        }
        return detectResult;
    }

    protected void checkRedEnvelopeUi(CommonDetectInfo commonDetectInfo, DetectResult detectResult) {
        // 遍历界面
        List<String> keyWordList = Arrays.asList("红包", "抽奖");
        if (CollectionUtils.isNotEmpty(commonDetectInfo.getResultDataLogs())) {
            for (int index = 0; index < commonDetectInfo.getResultDataLogs().size(); index++) {
                ResultDataLogBO resultDataLogBO = commonDetectInfo.getResultDataLogs().get(index);
                // 是否有点击红包文本动作
                if (onClickRedEnvelopeText(resultDataLogBO, keyWordList)) {
                    // 有点击红包文本的动作，往后面找看有没有权限弹窗
                    for (int applyIndex = index + 1; applyIndex < commonDetectInfo.getResultDataLogs().size(); applyIndex++)  {
                        ResultDataLogBO applyDataLogBO = commonDetectInfo.getResultDataLogs().get(applyIndex);
                        // 权限弹框的点击事件有时候会记录在权限弹框截图之前，遇到就跳过继续往下检查
                        if (isClickApplyPermissionDialogEvent(applyDataLogBO)) {
                            continue;
                        }
                        // 有非权限弹窗界面，不用再往下检查
                        if (!equalsUiType(applyDataLogBO, Arrays.asList(
                                MiitUITypeEnum.APPLY_PERMISSION,
                                MiitUITypeEnum.AGREE_PERMISSION,
                                MiitUITypeEnum.DISAGREE_PERMISSION))) {
                            break;
                        }
                        // 找到有红包文本的界面
                        ResultDataLogBO redEnvelopeUi = findRedEnvelopeUi(commonDetectInfo.getResultDataLogs(), resultDataLogBO);
                        if (redEnvelopeUi != null) {
                            addNoComplianceImage(commonDetectInfo, detectResult, redEnvelopeUi);
                        }
                        // 添加违规的截图证据
                        addNoComplianceImage(commonDetectInfo, detectResult, applyDataLogBO);
                    }
                }
            }
        }
    }

    private ResultDataLogBO findRedEnvelopeUi(List<ResultDataLogBO> uiList, ResultDataLogBO redEnvelopeEvent) {
        ResultDataLogIosDetailsBO detailsBO = CommonUtil.jsonToBean(redEnvelopeEvent.getDetails(),
                new TypeReference<ResultDataLogIosDetailsBO>() {
                });
        for (ResultDataLogBO ui:uiList) {
            if (Objects.nonNull(ui.getUiDumpResult()) && StringUtils.contains(ui.getUiDumpResult().getFullText(), detailsBO.getText())) {
                return ui;
            }
        }
        return null;
    }

    private boolean onClickRedEnvelopeText(ResultDataLogBO resultDataLogBO, List<String> keyWordList) {
        return resultDataLogBO != null
                && resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_EVENT.getValue()
                && keyWordList.stream().anyMatch(keyWord -> StringUtils.contains(resultDataLogBO.getDetails(), keyWord));
    }

    /**
     * 包含云手机内置通讯录内容
     * @param code
     * @return
     */
    private boolean containsAddressBook(CustomDetectInfo customDetectInfo, String code) {
        if (StringUtils.isNotBlank(code) || code.length() < 10 || MapUtils.isEmpty(customDetectInfo.getCloudPhoneAddressBook())) {
            return false;
        }
        // 完全匹配上通讯
        return customDetectInfo.getCloudPhoneAddressBook()
                .entrySet()
                .stream()
                .allMatch(entry -> code.contains(entry.getKey()) && code.contains(entry.getValue()));
    }
}
