package cn.ijiami.detection.miit.point.wechat.law164;

import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10205;

import java.util.ArrayList;
import java.util.List;

/**
 * APP未见向用户告知且未经用户同意，在静默状态下或在后台运行时，存在收集通讯录、短信、通话记录、相机等信息的行为，非服务所必需且无合理应用场景，超出与收集个人信息时所声称的目的具有直接或合理关联的范围。
 * <p>
 * <blockquote><pre>
 * 1、无隐私政策
 * 2、共XXX次（通讯录、短信、通话记录、相机行为）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「后台行为」「授权前行为」
 *
 * 1、隐私政策截图
 * 2、共XXX次（通讯录、短信、通话记录、相机行为）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「授权前行为」「后台行为」
 *
 * 分两种情况：
 * 一、无隐私政策
 * 1、判断无隐私政策
 * 2、判断后台运行情况下（后台行为），是否有触发运行通讯录、短信、通话记录、相机等信息的行为，如果有，则违规，无则合规
 * 二、有隐私政策
 * 1、判断有隐私政策
 * 2、判断静默安装状态下，是否有触发运行通讯录、短信、通话记录、相机等信息的行为，如果有，则违规，如果无，则继续判断后台运行情况下（后台行为），是否有触发运行通讯录、短信、通话记录、相机等信息的行为，如果有，则违规，无则合规
 *
 * 涉及行为：13001L, 13002L, 13003L, 13004L, 13005L, 14001L, 14002L, 14003L, 14004L, 14005L, 14006L, 14007L, 14008L, 14009L, 14010L, 14011L,
 *                         14012L, 21002L
 *
 * </pre></blockquote>
 * 
 * 场景一：修改
 * 1、没有有隐私政策
 * 2、触发了行为
 * 【授权前行为】【后台行为】

 * 场景二：修改
 * 1、有隐私政策
 * 2、有关键词
 * 2、触发了行为
 * 【授权前行为】

 * 场景三：修改
 * 1、有隐私政策
 * 2、没有关键词
 * 2、触发了行为
 * 【授权前行为】【后台行为】
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint2010205 extends DetectPoint10205 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return buildNonInvolved(commonDetectInfo, customDetectInfo);
    }

}
