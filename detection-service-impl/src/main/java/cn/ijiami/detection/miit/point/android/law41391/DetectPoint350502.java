package cn.ijiami.detection.miit.point.android.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint120301;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350502.java
 * @Description
 * App是否存在强制频繁过度索取权限
 * 2. App申请权限时是否同步告知权限使用目的
 * 判断规则：
 * c)申请权限时应同步告知用户权限申请目的，目的应明确具体且易于理解，不包含任何欺诈、诱骗、误导用户授权的描述；
 * 发现风险：
 * 建议在申请系统权限前，对权限的使用目的进行详细说明。
 */
@EnableDetectPoint
public class DetectPoint350502 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        CheckPermissionPopupResult result = permissionPopupCheck(commonDetectInfo);
        result.getImageLogsList().forEach(resultDataLogBO -> addNoInvolvedImage(commonDetectInfo, detectResult, resultDataLogBO));
        if (!result.isNonInvolved()) {
            String permissionNames = String.join("、", result.getPermissionNameList());
            detectResult.setConclusion(buildSequenceTextFormat("%s未同步告知用户其收集目的。", permissionNames));
            detectResult.setSuggestion(buildSequenceText("建议在申请系统权限前，对权限的使用目的进行详细说明。"));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        }
        return detectResult;
    }

}
