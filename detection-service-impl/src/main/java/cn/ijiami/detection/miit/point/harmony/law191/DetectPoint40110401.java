package cn.ijiami.detection.miit.point.harmony.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint110401;

/**
 * 隐私政策等收集使用规则难以阅读，如文字过小过密、颜色过淡、模糊不清，或未提供简体中文版等。
 *
 * 未发现风险：
 * 1、a.隐私政策文字最小值大于设定值
 * b.隐私政策行距最小值大于设定大小
 * c.文本为简体
 * 发现风险：
 * 1、a.隐私政策文字最小值小于或等于设定值
 * b.隐私政策行距最小值小于或等于设定大小
 * c.文本不为简体
 */
@EnableDetectPoint
public class DetectPoint40110401 extends DetectPoint110401 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }
}
