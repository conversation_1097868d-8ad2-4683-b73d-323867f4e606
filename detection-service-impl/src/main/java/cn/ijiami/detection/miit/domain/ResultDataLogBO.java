package cn.ijiami.detection.miit.domain;

import cn.ijiami.detection.enums.TerminalTypeEnum;

import java.io.Serializable;

/**
 * 检测工具文本提取实体
 * <blockquote><pre>
 * time(时间戳)
 * type(遍历模式) ： 1.正常遍历，对应前台行为检测 2.拒绝所有权限，并且登录注册截屏。3.同意全部权限，直到登录注册界面截屏，以后权限拒绝。 4.遍历到隐私政策,静默一分钟,退出。
 *               5. 摇一摇检测阶段，摇一摇后是否有跳转 6. Android应用获取权限设置页阶段
 * dataTag(数据标签) ： 1 (隐私政策界面) | 2(权限授权弹窗) | 3(权限授权同意) | 4(权限授权拒绝) | 5（注册/登录界面） | 6（个性化定推）|
 *                    7 (pdf记录) | 8 (记录操作事件，包括点击和红包诱骗弹窗) | 9 (隐私申请弹窗前相关文字内容描述) | 10（摇一摇检测前的截图）| 13（摇一摇后的截图）| 14（设置-权限列表页面）
 * imgpath(截屏)
 * xmlpath(屏幕内容)
 * runstatus(app运行状态) ：  Forground（前台） | Background（后台） | Death（死亡）
 * iosAppRestart： 1. 当前的遍历中，如果APP重启过，后续的截图会携带这个参数1
 * iosPrivacyDetailCategory: 1. 界面元素类型，通过解析界面元素获取 2. web类型，通过url获取 3. 图片类型，需要ocr识别获取
 * </blockquote></pre>
 *
 * <AUTHOR>
 * @date 2020-12-23 18:36
 */
public class ResultDataLogBO implements Serializable {

    private static final long serialVersionUID = -5788815090838414758L;

    private Long            time;
    private Integer         type;
    private Integer         dataTag;
    private String          xmlPath;
    private String          imgPath;
    private String          runStatus;
    private String          details;
    private Integer         iosPrivacyDetailCategory;
    private Integer         iosAppRestart;
    private String          context;
    private UIDumpResult    uiDumpResult;

    private TerminalTypeEnum terminalType;

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Integer getDataTag() {
        return dataTag;
    }

    public void setDataTag(Integer dataTag) {
        this.dataTag = dataTag;
    }

    public String getXmlPath() {
        return xmlPath;
    }

    public void setXmlPath(String xmlPath) {
        this.xmlPath = xmlPath;
    }

    public String getImgPath() {
        return imgPath;
    }

    public void setImgPath(String imgPath) {
        this.imgPath = imgPath;
    }

    public String getRunStatus() {
        return runStatus;
    }

    public void setRunStatus(String runStatus) {
        this.runStatus = runStatus;
    }

    public UIDumpResult getUiDumpResult() {
        return uiDumpResult;
    }

    public void setUiDumpResult(UIDumpResult uiDumpResult) {
        this.uiDumpResult = uiDumpResult;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public Integer getIosPrivacyDetailCategory() {
        return iosPrivacyDetailCategory;
    }

    public void setIosPrivacyDetailCategory(Integer iosPrivacyDetailCategory) {
        this.iosPrivacyDetailCategory = iosPrivacyDetailCategory;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    public Integer getIosAppRestart() {
        return iosAppRestart;
    }

    public void setIosAppRestart(Integer iosAppRestart) {
        this.iosAppRestart = iosAppRestart;
    }

    @Override
    public String toString() {
        return "ResultDataLogBO{" +
                "time=" + time +
                ", type=" + type +
                ", dataTag=" + dataTag +
                ", xmlPath='" + xmlPath + '\'' +
                ", imgPath='" + imgPath + '\'' +
                ", runStatus='" + runStatus + '\'' +
                ", details='" + details + '\'' +
                ", iosPrivacyDetailCategory=" + iosPrivacyDetailCategory +
                ", iosAppRestart=" + iosAppRestart +
                ", context='" + context + '\'' +
                ", uiDumpResult=" + uiDumpResult +
                '}';
    }

    public TerminalTypeEnum getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(TerminalTypeEnum terminalType) {
        this.terminalType = terminalType;
    }
}
