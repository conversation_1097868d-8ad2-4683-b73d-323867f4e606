package cn.ijiami.detection.miit.point.wechat.base;

import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.LawJudgmentHelper;
import cn.ijiami.detection.miit.point.android.law35273.DetectPoint250201;
import cn.ijiami.detection.miit.point.android.law35273.Law35273DetectPoint;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AppletDetectPoint250201.java
 * @Description 小程序
 * @createTime 2023年08月28日 17:55:00
 */
public class AppletDetectPoint250201 extends DetectPoint250201 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }

}
