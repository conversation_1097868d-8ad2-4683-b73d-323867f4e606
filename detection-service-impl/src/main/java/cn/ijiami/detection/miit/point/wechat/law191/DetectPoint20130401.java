package cn.ijiami.detection.miit.point.wechat.law191;

import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.*;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint130401;
import cn.ijiami.detection.utils.StreamUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 实际收集的个人信息或打开的可收集个人信息权限超出用户授权范围；
 *
 * 未发现风险：
 * 1、触发的个人信息行为，在隐私政策中获取到关键文本
 * 2、申请打开的个人信息权限，在隐私政策中获取到关键文本
 * 发现风险：
 * 【有隐私政策】
 * 1、触发的个人信息行为，在隐私政策中未获取到关键文本
 * 2、申请打开的个人信息权限，在隐私政策中未获取到关键文本
 * 【无隐私政策】
 * 存在个人信息行为即有风险
 */
@EnableDetectPoint
public class DetectPoint20130401 extends DetectPoint130401 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }

}
