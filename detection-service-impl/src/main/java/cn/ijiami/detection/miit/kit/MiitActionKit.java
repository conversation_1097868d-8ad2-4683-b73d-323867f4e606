package cn.ijiami.detection.miit.kit;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import cn.ijiami.detection.VO.ActionFrequencyVO;
import cn.ijiami.detection.VO.FrequencyTimeVO;
import cn.ijiami.detection.analyzer.helper.BehaviorActionConvertHelper;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.utils.DateDistance;
import cn.ijiami.detection.utils.DateUtils;
import lombok.Data;

/**
 * miit 堆栈数据套件
 *
 * <AUTHOR>
 * @date 2020-12-22 10:50
 */
public class MiitActionKit {
	
	private static final Logger LOG = LoggerFactory.getLogger(MiitActionKit.class);

    private MiitActionKit() {
    }

    /**
     * 对应21004、21005别名
     */
    public static final String APP_ASSOCIATE_START = "应用关联启动";
    
    /**
     * 对应32001
     */
    public static final String APP_OWN_START       = "应用自启动";
    
    /**
     * 涉及到固定频次的检测项目
     */
    public static final String[] actionFixedFrequencyItemNo = {"10202", "10204", "10206", "10208", "1010202", "1010204", "1010206", "1010208", "140401"};

    /**
     * 过滤并统计数据
     *
     * @param actionNougats   待的行为数据信息
     * @param ruleActionIds   行为数据过滤条件
     * @param actionDetailMap 用于补充行为数据信息
     * @param apkPackageName  应用包名
     * @return 非null集合
     */
    public static List<ActionAnalyse> filterAndCountAction(List<TPrivacyActionNougat> actionNougats, Set<Long> ruleActionIds,
                                                           Map<Long, TActionNougat> actionDetailMap, String apkPackageName) {
        if (CollectionUtils.isEmpty(actionNougats)) {
            return new ArrayList<>();
        }
        // 筛选出符合要求的行为数据
        List<TPrivacyActionNougat> afterFilter = actionNougats.stream().filter(buildRuleActionFilter(ruleActionIds)).collect(Collectors.toList());
        return analyseAndMerge(afterFilter, actionDetailMap, apkPackageName);
    }

    /**
     * 计算行为是否有周期性获取
     * @param actionNougatList
     * @param ruleActionIds
     */
    public static void setActionCycle(List<TPrivacyActionNougat> actionNougatList, Set<Long> ruleActionIds) {
        if (CollectionUtils.isEmpty(actionNougatList)) {
            return;
        }
        List<Long> timeList = actionNougatList.stream().map(TPrivacyActionNougat::getActionTimeStamp).sorted(Comparator.naturalOrder()).collect(Collectors.toList());
        Long beginTime = timeList.get(0);
        Long endTime = timeList.get(timeList.size() - 1);
        List<TPrivacyActionNougat> afterFilter = actionNougatList.stream().filter(buildRuleActionFilter(ruleActionIds)).collect(Collectors.toList());
        afterFilter.stream()
                .collect(Collectors.groupingBy(TPrivacyActionNougat::getActionId))
                .forEach((actionId, nougatList) -> {
                    statisticsSameExecutorActionCycle(nougatList, beginTime, endTime);
                });
    }

    /**
     * 计算相同执行主体的行为是否周期性获取
     * @param nougatList
     * @param beginTime
     * @param endTime
     */
    private static void statisticsSameExecutorActionCycle(List<TPrivacyActionNougat> nougatList, long beginTime, long endTime) {
        nougatList.stream().collect(Collectors.groupingBy(TPrivacyActionNougat::getExecutor)).forEach((executor, list) -> {
            List<CycleRecord> cycleRecords = getCycle(
                    list,
                    beginTime,
                    endTime
            );
            if (!cycleRecords.isEmpty()) {
                cycleRecords.forEach(record -> {
                    record.getActionNougats().forEach(nougat -> {
                        nougat.setNumberAction(record.getCount());
                        nougat.setTriggerCycleTime(record.getCycleTime() * 1000);
                    });
                });
            }
        });
    }

    private static List<CycleRecord> getCycle(List<TPrivacyActionNougat> nougatList, long beginTime, long endTime) {
        if (nougatList.size() <= 2) {
            return Collections.emptyList();
        }
        List<CycleRecord> recordList = new ArrayList<>();
        nougatList.sort((o1, o2) -> (int) (o1.getActionTimeStamp() - o2.getActionTimeStamp()));
        long cycleTime = 0;
        long firstTime = 0;
        long lastTime = 0;
        int count = 0;
        // 存同个触发周期的行为
        List<TPrivacyActionNougat> cycleNougats = new ArrayList<>();
        for (int i=1; i<nougatList.size(); i++) {
            TPrivacyActionNougat nougat = nougatList.get(i);
            TPrivacyActionNougat previous = nougatList.get(i - 1);
            // 前端显示数据的时候是以秒显示的，转换成秒去计算周期
            long interval = nougat.getActionTimeStamp() / 1000 - previous.getActionTimeStamp() / 1000;

            if (cycleTime == 0) {
                cycleTime = interval;
                firstTime = previous.getActionTimeStamp();
                lastTime = nougat.getActionTimeStamp();
                count = 1;
                cycleNougats.add(previous);
                cycleNougats.add(nougat);
            } else if (interval == 0) {
                // 相隔不足1秒的，看做是同一次获取
                cycleNougats.add(nougat);
            } else if(Math.abs(cycleTime - interval) > 0) {
                // 如果周期时间不一致，说明是一个新周期，把旧周期和循环次数存起来
                saveCycleRecord(recordList, cycleNougats, cycleTime, firstTime, lastTime, count);
                cycleTime = interval;
                count = 1;
                firstTime = previous.getActionTimeStamp();
                lastTime = nougat.getActionTimeStamp();
                // 把起始行为保存起来
                cycleNougats = new ArrayList<>(previousSameTimeNougat(nougatList, i - 1, firstTime));
                cycleNougats.add(nougat);
            } else {
                count++;
                lastTime = nougat.getActionTimeStamp();
                cycleNougats.add(nougat);
                // 跑到最后一个要把记录加进去
                if (i == nougatList.size() - 1) {
                    saveCycleRecord(recordList, cycleNougats, cycleTime, firstTime, lastTime, count);
                }
            }
        }
        return recordList;
    }

    private static void saveCycleRecord(List<CycleRecord> recordList, List<TPrivacyActionNougat> cycleNougats,
                                        long cycleTime, long firstTime, long lastTime, int count) {
        if (count < PinfoConstant.MINI_COUNT) {
            return;
        }
        Optional<CycleRecord> findSame = findSameCycleRecord(recordList, cycleTime);
        if (findSame.isPresent()) {
            findSame.get().count += count;
            findSame.get().lastTime += lastTime;
            findSame.get().actionNougats.addAll(cycleNougats);
        } else {
            recordList.add(new CycleRecord(cycleNougats, cycleTime, firstTime, lastTime, count));
        }
    }

    /**
     * 往前寻找同一秒的行为
     * @return
     */
    private static List<TPrivacyActionNougat> previousSameTimeNougat(List<TPrivacyActionNougat> nougatList, int index, long firstTime) {
        List<TPrivacyActionNougat> sameTimeList = new ArrayList<>();
        for (; index>=0; index--) {
            TPrivacyActionNougat nougat = nougatList.get(index);
            if (nougat.getActionTimeStamp() / 1000 == firstTime / 1000) {
                sameTimeList.add(nougat);
            }
        }
        return sameTimeList;
    }

    private static Optional<CycleRecord> findSameCycleRecord(List<CycleRecord> recordList, long cycleTime) {
        return recordList.stream().filter(record -> record.cycleTime == cycleTime).findFirst();
    }

    @Data
    static class CycleRecord {
        private long cycleTime;
        private long firstTime;
        private long lastTime;
        private int count;
        private List<TPrivacyActionNougat> actionNougats;

        public CycleRecord(List<TPrivacyActionNougat> actionNougats, long cycleTime, long firstTime, long lastTime, int count) {
            this.cycleTime = cycleTime;
            this.firstTime = firstTime;
            this.lastTime = lastTime;
            this.count = count;
            this.actionNougats = actionNougats;
        }
    }

    /**
     * 统计总触发次数
     *
     * @param actionAnalyses 分析后的数据
     * @return
     */
    public static int countActionAnalyse(List<ActionAnalyse> actionAnalyses) {
        if (CollectionUtils.isEmpty(actionAnalyses)) {
            return 0;
        }
        int count = actionAnalyses.stream().mapToInt(actionAnalyse -> Optional.ofNullable(actionAnalyse).map(ActionAnalyse::getFrequency).orElse(0)).sum();
        return count;
    }

    private static Predicate<TPrivacyActionNougat> buildRuleActionFilter(Set<Long> ruleActionIds) {
        return (action) -> ruleActionIds.contains(action.getActionId());
    }

    private static Function<TPrivacyActionNougat, Long> groupSameTimeFunction() {
        return b -> b.getActionTime().getTime() / 1000;
    }

    private static final List<Long> SDCARD_ACTION_ID_LIST = Arrays.asList(
            BehaviorActionConvertHelper.ConvertActionId.SDCARD_WRITE.id,
            BehaviorActionConvertHelper.ConvertActionId.SDCARD_DELETE.id,
            BehaviorActionConvertHelper.ConvertActionId.SDCARD_READ.id
    );

    /**
     * 分析同一时间内的行为数据，并组装成符合要求的数据
     *
     * @param root            格式化数据，即行为数据
     * @param actionDetailMap 补充信息，即行为详情
     * @return 非null集合
     */
    private static List<ActionAnalyse> analyseAndMerge(List<TPrivacyActionNougat> root, Map<Long, TActionNougat> actionDetailMap, String apkPackageName) {
        List<ActionAnalyse> actionAnalyses = new ArrayList<>();
        groupByActionIdAndExecutor(root, (actionId, actions) -> {
            mergeActionAnalyse(actionAnalyses, actionId, actions, actionDetailMap, apkPackageName);
        });
        return actionAnalyses;
    }

    /**
     * 根据行为类型和主体进行分组
     */
    public static void groupByActionIdAndExecutor(List<TPrivacyActionNougat> root, BiConsumer<Long, List<TPrivacyActionNougat>> action) {
        root.stream().collect(Collectors.groupingBy(TPrivacyActionNougat::getActionId)).forEach((actionId, sameIdActions) -> {
            sameIdActions.stream().collect(Collectors.groupingBy(TPrivacyActionNougat::getExecutor)).forEach((executor, executorActions) -> {
                // 操作sd卡行为需要再归类一次，根据读取的文件路径相同的归类
                if (SDCARD_ACTION_ID_LIST.contains(actionId)) {
                    executorActions.stream().collect(Collectors.groupingBy(TPrivacyActionNougat::getDetailsData)).forEach((details, fileActions) -> {
                        fileActions.stream().collect(Collectors.groupingBy(groupSameTimeFunction())).forEach((time, sameTimeActions) -> {
                            action.accept(actionId, sameTimeActions);
                        });
                    });
                } else {
                    executorActions.stream().collect(Collectors.groupingBy(groupSameTimeFunction())).forEach((time, sameTimeActions) -> {
                        action.accept(actionId, sameTimeActions);
                    });
                }
            });
        });
    }

    private static void mergeActionAnalyse(List<ActionAnalyse> actionAnalyses, Long actionId, List<TPrivacyActionNougat> actions,
                                           Map<Long, TActionNougat> actionDetailMap, String apkPackageName) {
        ActionAnalyse actionAnalyse = new ActionAnalyse();
        actionAnalyse.setActionId(actionId);
        // 行为信息
        TPrivacyActionNougat countAction = findMergeActionNougat(actions);
        actionAnalyse.setDataId(countAction.getId());
        actionAnalyse.setActionTime(countAction.getActionTime());
        actionAnalyse.setActionTimeMillis(countAction.getActionTime().getTime() / 1000);
        actionAnalyse.setExecutorType(countAction.getExecutorType());
        actionAnalyse.setExecutor(countAction.getExecutor());
        actionAnalyse.setPackageName(countAction.getPackageName());
        actionAnalyse.setActionPermission(countAction.getActionPermission());
        actionAnalyse.setActionPermissionAlias(countAction.getActionPermissionAlias());
        actionAnalyse.setBehaviorStage(countAction.getBehaviorStage());
        actionAnalyse.setSdkIds(countAction.getSdkIds());
        actionAnalyse.setDetailsData(countAction.getDetailsData());
        actionAnalyse.setStackInfo(countAction.getStackInfo());
        // 基础信息
        TActionNougat baseAction = actionDetailMap.get(actionId);
        if (Objects.nonNull(baseAction)) {
            // 默认不与个人信息相关
            Integer personal = Optional.ofNullable(baseAction.getPersonal()).orElse(0);
            actionAnalyse.setPersonal(personal.equals(1));
            actionAnalyse.setSensitive(baseAction.getSensitive());
            actionAnalyse.setActionName(specialActionName(baseAction, apkPackageName, countAction.getDetailsData()));
        }
        // 统计信息
        actionAnalyse.setFrequency(actions.size());
        // 设置周期性的参数
        if (Objects.nonNull(countAction.getNumberAction())) {
            ActionFrequencyVO frequencyVO = new ActionFrequencyVO();
            frequencyVO.setCount(countAction.getNumberAction());
            frequencyVO.setInterval(countAction.getTriggerCycleTime() / 1000);
            actionAnalyse.setActionFrequency(frequencyVO);
        }
        actionAnalyses.add(actionAnalyse);
    }

    /**
     * 获取其中一个行为作为合并项的数据
     * @return
     */
    private static TPrivacyActionNougat findMergeActionNougat(List<TPrivacyActionNougat> actions) {
        // 优先获取有周期性的行为作为样本
        Optional<TPrivacyActionNougat> cycleAction = actions.stream()
                .filter(nougat -> Objects.nonNull(nougat.getTriggerCycleTime()))
                .filter(nougat -> Objects.nonNull(nougat.getNumberAction()))
                .min(Comparator.comparingLong(o1 -> o1.getActionTime().getTime()));
        return cycleAction.orElseGet(() -> actions.stream().findFirst().get());
    }

    /**
     * 特殊行为行为信息转换
     * 在拼装数据的时候，应该拿应用的包名，比对数据详情里面（detail），包含自身的包名或者只有一个“/”，
     * 说明启动的是应用本身的组件，这时候启动server、启动activity要原样显示，
     * 不包含，说明启动的不是应用本身的组件，改名为“关联启动”
     *
     * @param baseAction
     * @param apkPackageName
     * @param details
     * @return
     */
    private static String specialActionName(TActionNougat baseAction, String apkPackageName, String details) {
        Long actionId = baseAction.getActionId();
        boolean isSpecial = (actionId == 21004 || actionId == 21005);
        if (!isSpecial) {
            return baseAction.getActionName();
        }
        // 检测任务A，
        // 21004、21005，行为结果的包名是A，即显示原本21004、21005的行为名称
        // 行为结果不等于A，即显示，应用关联启动
        if (StringUtils.isEmpty(details) || !details.contains(apkPackageName)) {
            return APP_ASSOCIATE_START;
        }
        // 启动应用本身的组件
        if (details.contains(apkPackageName) || "/".equals(details)) {
            return baseAction.getActionName();
        }
        return APP_ASSOCIATE_START;
    }
}
