package cn.ijiami.detection.miit.point.alipay.law164;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * App在征求用户同意环节，未提供明确的同意或拒绝按钮，或者使用“好的”“我知道了”等词语。
 * <blockquote><pre>
 * 截图
 * 检测结果会返回判断标识和截图，根据判断标识做判断
 *
 * 任意一个按钮即可
 * </pre></blockquote>
 *
 * <AUTHOR>
 * @date 2020-12-18 15:31
 */
@EnableDetectPoint
public class DetectPoint3010107 extends PrivacyUiDetectPoint {
    
    private static final Logger LOG = LoggerFactory.getLogger(DetectPoint3010107.class);

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return buildNonInvolved(commonDetectInfo, customDetectInfo);
    }
}
