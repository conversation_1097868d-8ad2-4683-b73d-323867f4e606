package cn.ijiami.detection.miit.point.android.law191;

import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TApplyPermission;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.enums.ResultDataLogBoTypeEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.*;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.LawJudgmentHelper;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 征得用户同意前就开始收集个人信息或打开可收集个人信息的权限；
 *
 * 未发现风险：
 * 1、隐私政策授权页面提供明确同意、拒绝按钮
 * 2、隐私政策授权前无触发收集个人信息
 * 3、隐私政策授权前无申请打开个人信息权限
 * 4、隐私政策授权前无出现cookie传输个人信息
 * 发现风险：
 * 1、隐私政策授权未提供明确同意、拒绝按钮
 * 2、隐私政策授权前触发收集个人信息
 * 3、隐私政策授权前申请打开个人信息权限
 * 4、隐私政策授权前出现cookie传输个人信息
 * 5、无隐私政策
 */
@EnableDetectPoint
public class DetectPoint130101 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        List<String> conclusionList = new ArrayList<>();
        List<String> suggestionList = new ArrayList<>();
        if (commonDetectInfo.isHasPrivacyPolicy()) {
            havePrivacyPolicyAgreeOrRefuseButton(commonDetectInfo, customDetectInfo, detectResult);
            if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE) {
                conclusionList.add("隐私中政策未提供明确同意、拒绝按钮");
                suggestionList.add("建议在征求用户同意环节，明确提供“同意”与“拒绝”的按钮。不要使用“好的”“我知道了”“我再想想”等模糊或者有多意性的表述。");
            }
        } else {
            conclusionList.add("未检测到隐私政策");
            suggestionList.add(String.format("%s中隐私政策通过弹窗、文本链接、附件、常见问题（FAQs）等界面或形式展示。", executor(commonDetectInfo)));
        }
        // 隐私政策授权前收集检查
        List<ActionAnalyse> actionAnalyseList = filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT);
        List<ActionAnalyse> collectActionList = actionAnalyseList.stream()
                .filter(ActionAnalyse::getPersonal)
                .collect(Collectors.toList());
        if (!collectActionList.isEmpty()) {
            String prefix;
            if (commonDetectInfo.isHasPrivacyPolicy()) {
                prefix = "隐私政策授权前收集";
            } else {
                prefix = "未检测到隐私政策，存在收集";
            }
            conclusionList.add(prefix + collectActionList.stream().map(ActionAnalyse::getActionName).distinct().collect(Collectors.joining("、")));
        }
        // 隐私政策授权前申请检查，只检查正常的遍历流程，其他的流程
        Set<String> applyActionNameList = new HashSet<>();
        List<ResultDataLogBO> commonUiList = commonDetectInfo.getResultDataLogs().stream()
                .filter(resultDataLogBO -> resultDataLogBO.getType() == ResultDataLogBoTypeEnum.COMMON.type)
                .collect(Collectors.toList());
        // 遍历界面
        for (ResultDataLogBO resultDataLogBO : commonUiList) {
            if (isPolicyPopup(resultDataLogBO)) {
                break;
            }
            if (isPermissionPopup(commonDetectInfo.getTerminalTypeEnum(), resultDataLogBO)) {
                TApplyPermission permission = findApplyPermissionRegex(commonDetectInfo, resultDataLogBO.getUiDumpResult());
                if (!applyActionNameList.contains(permission.getApplyName())) {
                    // 申请个人信息权限界面
                    addNoComplianceImage(commonDetectInfo, detectResult, resultDataLogBO);
                    applyActionNameList.add(permission.getApplyName());
                }
            }
        }
        if (!applyActionNameList.isEmpty()) {
            conclusionList.add((commonDetectInfo.isHasPrivacyPolicy() ? "隐私政策授权前" : "未检测到隐私政策") + "申请打开" +
                    String.join("、", applyActionNameList));
        }
        // 出现cookie传输个人信息
        List<TPrivacySensitiveWord> sensitiveWordList = getSensitiveWords(commonDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT)
                .stream()
                .filter(s -> StringUtils.isNotBlank(s.getCookie()) && !s.getCookie().equals(PinfoConstant.DETAILS_EMPTY))
                .collect(Collectors.toList());
        if (!sensitiveWordList.isEmpty()) {
            conclusionList.add((commonDetectInfo.isHasPrivacyPolicy() ? "隐私政策授权前出现" : "未检测到隐私政策，存在") + "cookie传输个人信息");
        }
        if (!sensitiveWordList.isEmpty() || !applyActionNameList.isEmpty() || !collectActionList.isEmpty()) {
            suggestionList.add(String.format("建议对%s隐私政策授权前的个人信息行为、个人信息权限申请进行整改。", executor(commonDetectInfo)));
        }
        if (!conclusionList.isEmpty()) {
            sortScreenshot(detectResult);
            detectResult.setConclusion(buildSequenceText(conclusionList));
            detectResult.setAnalysisResult(collectActionList);
            detectResult.setSensitiveWordResult(sensitiveWordList.stream().map(this::buildActionNetwork).collect(Collectors.toList()));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            detectResult.setSuggestion(buildSequenceText(suggestionList));
        }
        return detectResult;
    }

}
