package cn.ijiami.detection.miit.point.android.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.LawJudgmentHelper;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint120101;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint140401;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350602.java
 * @Description
 * App是否存在违规嵌入使用第三方SDK情况
 * 2. 是否明示三方SDK收集使用个人信息情况
 * 判断规则：
 * d)应向用户告知嵌入的第三方SDK名称、SDK收集的个人信息种类、使用目的及申请的系统权限、申请目的等，并取得用户同意。
 * 发现风险：
 * 【无隐私政策】
 * 1、未检测到隐私政策
 * 【有隐私政策】
 * 1、隐私中政策中未对【SDK名称列表】进行说明。
 */
@EnableDetectPoint
public class DetectPoint350602 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        List<String> conclusionList = new ArrayList<>();
        List<String> suggestionList = new ArrayList<>();
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            conclusionList.add("未检测到隐私政策");
        } else {
            // 隐私政策截图截图地址
            detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
            // 找出sdk触发的个人行为
            CheckSdkNameResult result = checkAllBehaviorStageSdkName(commonDetectInfo, customDetectInfo);
            if (!result.isNonInvolved()) {
                conclusionList.add(String.format("隐私中政策中未对%s进行说明。", result.getNoMatchSdkNameList()));
                suggestionList.add(String.format("请在隐私政策中补充对%s的描述。", result.getNoMatchSdkNameList()));
            }
            detectResult.setAnalysisResult(result.getAnalysisResult());
            detectResult.setPrivacyPolicyFragment(result.getPrivacyPolicyFragment());
        }
        // 没有触发个人信息行为的SDK，不涉及
        setDetectResultByConclusion(commonDetectInfo, detectResult, conclusionList, suggestionList);
        return detectResult;
    }

}
