package cn.ijiami.detection.miit.point.android.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static cn.ijiami.detection.miit.constants.DetectPointIdentifyItems.POINT_PERSONAL_INFO;

/**
 * 未提供有效的更正、删除个人信息及注销用户账号功能；
 *
 * 未发现风险：
 * 1、隐私政策对【删除、更正（修改）、查询（访问）、注销个人信息】描述
 */
@EnableDetectPoint
public class DetectPoint160101 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        if (commonDetectInfo.isHasPrivacyPolicy()) {
            List<String> conclusionList = new ArrayList<>();
            List<String> suggestionList = new ArrayList<>();
            checkHowToModifyPersonalInfo(commonDetectInfo, detectResult, conclusionList, suggestionList);
            if (conclusionList.isEmpty()) {
                detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
            } else {
                detectResult.setConclusion(buildSequenceText(conclusionList));
                detectResult.setSuggestion(buildSequenceText(suggestionList));
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            }
        } else {
            detectResult.setConclusion(buildSequenceText("未检测到隐私政策"));
            detectResult.setSuggestion(buildSequenceText("请在隐私政策中提供有效的删除/更正（修改）/查询（访问）/注销个人信息途径"));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        }
        return detectResult;
    }

}
