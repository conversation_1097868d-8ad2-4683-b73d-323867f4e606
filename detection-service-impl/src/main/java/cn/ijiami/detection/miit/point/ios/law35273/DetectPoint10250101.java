package cn.ijiami.detection.miit.point.ios.law35273;

import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import cn.ijiami.detection.miit.point.android.law35273.DetectPoint250101;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 5.1.b
 * 个人信息控制者不应隐瞒产品或服务所具有的收集个人信息的功能
 *
 * 判断规则
 * 发现风险
 * 【有隐私政策】
 * 1 使用cookie及其同类技术收集个人信息，未在隐私政策中声明。
 * 2 第三方SDK收集的个人信息与隐私政策不一致
 * 3 APP收集的个人信息与隐私政策不一致
 * 【无隐私政策】
 * APP或者SDK有收集个人信息
 * @createTime 2022年03月24日 17:32:00
 */
@EnableDetectPoint
public class DetectPoint10250101 extends DetectPoint250101 {

}
