package cn.ijiami.detection.miit.point.android;

import static cn.ijiami.detection.helper.DetectPointCommonHelper.*;
import static cn.ijiami.detection.miit.constants.DetectPointIdentifyItems.POINT_PERSONAL_INFO;
import static cn.ijiami.detection.utils.CommonUtil.strAndContains;
import static cn.ijiami.detection.utils.CommonUtil.strOrContains;
import static cn.ijiami.detection.utils.ConstantsUtils.APPLY_POLICY_DETAIL_MAX_TEXT_LENGTH;
import static cn.ijiami.detection.utils.ResultDataLogUtils.getOcrText;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.ijiami.detection.VO.CheckList;
import cn.ijiami.detection.enums.*;
import cn.ijiami.detection.helper.DetectPointCommonHelper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.core.type.TypeReference;

import cn.ijiami.detection.DTO.ocr.RecognizeData;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TApplyPermission;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TPrivacySensitiveWord;
import cn.ijiami.detection.entity.TPrivacySharedPrefs;
import cn.ijiami.detection.miit.constants.DetectPointIdentifyItems;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.ActionNetwork;
import cn.ijiami.detection.miit.domain.ActionOutSide;
import cn.ijiami.detection.miit.domain.ActionSharedPerfs;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.domain.ResultDataLogIosDetailsBO;
import cn.ijiami.detection.miit.domain.UIComponent;
import cn.ijiami.detection.miit.domain.UIDumpResult;
import cn.ijiami.detection.miit.enums.MiitDataTypeEnum;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.enums.MiitRunStatusEnum;
import cn.ijiami.detection.miit.enums.MiitUIClassEnum;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;
import cn.ijiami.detection.miit.kit.MiitLogKit;
import cn.ijiami.detection.miit.kit.MiitWordKit;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.StreamUtils;
import cn.ijiami.detection.utils.UIDistinguishUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public abstract class PrivacyUiDetectPoint extends AbstractDetectPoint {

    /**
     * 例如：不同意并退出APP
     */
    private static final int ANDROID_MAX_BUTTON_TEXT = 12;
    private static final int IOS_MAX_BUTTON_TEXT = 12;

    /**
     * 隐私政策界面是否有同意和拒绝按钮
     * @param commonDetectInfo
     * @param customDetectInfo
     * @param detectResult
     */
    protected void havePrivacyPolicyAgreeOrRefuseButton(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo, DetectResult detectResult) {
        Map<String, String> keyWork = customDetectInfo.getKeyWordRegex();
        String agreeRegex = keyWork.get("point_10107_agree");
        String refuseRegex = keyWork.get("point_10107_refuse");
        // 申请权限页面/个性化推送页面
        List<ResultDataLogBO> applyResultDataLogBOList = commonDetectInfo.getResultDataLogs().stream().filter(resultDataLogBO ->
                resultDataLogBO.getUiDumpResult() != null
                        && (resultDataLogBO.getUiDumpResult().getUiType() == MiitUITypeEnum.APPLY_POLICY.getValue()
                        || (resultDataLogBO.getUiDumpResult().getUiType() == MiitUITypeEnum.POLICY_DETAIL.getValue()
                        && StringUtils.length(resultDataLogBO.getUiDumpResult().getFullText()) < APPLY_POLICY_DETAIL_MAX_TEXT_LENGTH)
                        || resultDataLogBO.getUiDumpResult().getUiType() == MiitUITypeEnum.OTHER.getValue()
                        || resultDataLogBO.getUiDumpResult().getUiType() == MiitUITypeEnum.LOGIN_RIGISTER.getValue())
                        && strOrContains(resultDataLogBO.getUiDumpResult().getFullText(), "隐私", "信息保护政策"))
                .collect(Collectors.toList());
        boolean noInvolved = false;
        // 遍历界面
        for (ResultDataLogBO resultDataLogBO : applyResultDataLogBOList) {
            // 排除登录注册页面
            if (isLoginRegister(commonDetectInfo, resultDataLogBO)) {
                continue;
            }
            // 申请隐私政策页面
            UIDumpResult uiDumpResult = resultDataLogBO.getUiDumpResult();
            // 排除无按钮非申请页面
            List<RecognizeData> ocrResultList = null;
            if (!hasAgreeOrRefuseButtonByUi(uiDumpResult, commonDetectInfo, agreeRegex, refuseRegex)) {
                ocrResultList = ocrImage(resultDataLogBO, commonDetectInfo);
                if (!findAgreeOrRefuseButtonByOcr(ocrResultList, commonDetectInfo, agreeRegex, refuseRegex)) {
                    continue;
                }
            }
            // ui元素里找按钮，如果ui元素没找到按钮，那就去ocr识别
            if (findAgreeAndRefuseButtonByUi(uiDumpResult, commonDetectInfo, agreeRegex, refuseRegex)
                    || findAgreeAndRefuseButtonByOcr(ocrResultList, commonDetectInfo, agreeRegex, refuseRegex)) {
                // 有明确同意/拒绝确定
                noInvolved = true;
                addNoInvolvedImage(commonDetectInfo, detectResult, resultDataLogBO);
            } else {
                addNoComplianceImage(commonDetectInfo, detectResult, resultDataLogBO);
            }
        }

        if (noInvolved) {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        }
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE) {
            for (ResultDataLogBO applyResultDataLogBO : applyResultDataLogBOList) {
                addNoComplianceImage(commonDetectInfo, detectResult, applyResultDataLogBO);
            }
        }
    }

    private boolean hasAgreeOrRefuseButtonByUi(UIDumpResult uiDumpResult, CommonDetectInfo commonDetectInfo, String agreeRegex, String refuseRegex) {
        // 排除无按钮非申请页面
        boolean hasAgreeOrRefuseButton = false;
        for (int index = 0; index < uiDumpResult.getUiComponentList().size(); index++) {
            UIComponent uiComponent = uiDumpResult.getUiComponentList().get(index);
            // 排除返回键
            if (StringUtils.isNotBlank(uiComponent.getResourceId())
                    && StringUtils.contains(uiComponent.getResourceId().toLowerCase(), "back")) {
                continue;
            }
            if (StringUtils.isNotBlank(uiComponent.getContentDesc())
                    && StringUtils.contains(uiComponent.getContentDesc().toLowerCase(), "back")) {
                continue;
            }
            String fullContent = uiComponent.getText() + uiComponent.getContentDesc();
            // 判断是否有同意或取消按钮

            if (isButtonTextByUi(uiComponent, commonDetectInfo) && (hasAgreeButton(fullContent, agreeRegex) || hasRefuseButton(fullContent, refuseRegex))) {
                hasAgreeOrRefuseButton = true;
            }

            // 排除返回按键等导航栏控件
            if (hasAgreeOrRefuseButton) {
                boolean hasText = false;
                for (int i = 0; i < index; i++) {
                    UIComponent beforeElement = uiDumpResult.getUiComponentList().get(i);
                    if (StringUtils.isNotBlank(beforeElement.getText()) || StringUtils.isNotBlank(beforeElement.getContentDesc())) {
                        hasText = true;
                        break;
                    }
                }
                if (!hasText) {
                    hasAgreeOrRefuseButton = false;
                }
            }
        }
        return hasAgreeOrRefuseButton;
    }

    private boolean findAgreeOrRefuseButtonByOcr(List<RecognizeData> ocrResultList, CommonDetectInfo commonDetectInfo,
                                                 String agreeRegex, String refuseRegex) {
        for (RecognizeData result:ocrResultList) {
            for (RecognizeData.DataDTO data:result.getData()) {
                if (!isButtonTextByOcr(data, commonDetectInfo)) {
                    return false;
                }
                if (hasAgreeButton(data.getText(), agreeRegex) || hasRefuseButton(data.getText(), refuseRegex)) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean findAgreeAndRefuseButtonByUi(UIDumpResult uiDumpResult, CommonDetectInfo commonDetectInfo, String agreeRegex, String refuseRegex) {
        boolean hasRefuse = false;
        boolean hasAgree = false;
        for (UIComponent uiComponent : uiDumpResult.getUiComponentList()) {
            String fullContent = StringUtils.removeAll(uiComponent.getText() + uiComponent.getContentDesc(), "\\s");
            // 按钮文本不会太长
            if (!isButtonTextByUi(uiComponent, commonDetectInfo)) {
                continue;
            }

            boolean isAgree = MiitWordKit.checkTextMeetTheRegex(fullContent, agreeRegex);
            boolean isRefuse = MiitWordKit.checkTextMeetTheRegex(fullContent, refuseRegex);
            if (!hasAgree && isAgree) {
                hasAgree = true;
            }

            if (!hasRefuse && isRefuse) {
                hasRefuse = true;
            }
            if (hasAgree && hasRefuse) {
                break;
            }
        }
        return hasRefuse && hasAgree;
    }

    private boolean findAgreeAndRefuseButtonByOcr(List<RecognizeData> ocrResultList, CommonDetectInfo commonDetectInfo, String agreeRegex, String refuseRegex) {
        if (CollectionUtils.isEmpty(ocrResultList)) {
            return false;
        }
        boolean hasRefuse = false;
        boolean hasAgree = false;
        for (RecognizeData ocrResult:ocrResultList) {
            for (RecognizeData.DataDTO data:ocrResult.getData()) {
                if (!isButtonTextByOcr(data, commonDetectInfo)) {
                    continue;
                }
                if (MiitWordKit.checkTextMeetTheRegex(data.getText(), agreeRegex)) {
                    hasAgree = true;
                }
                if (MiitWordKit.checkTextMeetTheRegex(data.getText(), refuseRegex)) {
                    hasRefuse = true;
                }
                if (hasAgree && hasRefuse) {
                    break;
                }
            }
        }
        return hasRefuse && hasAgree;
    }

    private List<RecognizeData> ocrImage(ResultDataLogBO resultDataLogBO, CommonDetectInfo commonDetectInfo) {
        if (CollectionUtils.isNotEmpty(resultDataLogBO.getUiDumpResult().getOcrList())) {
            return resultDataLogBO.getUiDumpResult().getOcrList();
        }
        String imgPath = commonDetectInfo.getFilePath() + File.separator + resultDataLogBO.getImgPath();
        try {
            List<RecognizeData> ocrList = commonDetectInfo.getOcrService().extractText(imgPath);
            resultDataLogBO.getUiDumpResult().setOcrList(ocrList);
            String ocrText = ocrList.stream()
                    .flatMap(data -> data.getData().stream().map(RecognizeData.DataDTO::getText))
                    .collect(Collectors.joining());
            resultDataLogBO.getUiDumpResult().setOcrText(ocrText);
            return ocrList;
        } catch (Exception e) {
            e.getMessage();
        }
        return Collections.emptyList();
    }

    private boolean isButtonTextByUi(UIComponent uiComponent, CommonDetectInfo commonDetectInfo) {
        String buttonText = StringUtils.isNotBlank(uiComponent.getText()) ? uiComponent.getText() : uiComponent.getContentDesc();
        if (StringUtils.isBlank(buttonText)) {
            return false;
        }
        // 有时候按钮文本会带有数值，例如 同意(2)，导致误判。所以移除掉原本数字的判断
        if (Pattern.compile("[.:：]").matcher(buttonText).find()) {
            return false;
        }
        if (commonDetectInfo.getTerminalTypeEnum() == TerminalTypeEnum.IOS) {
            // ios只有XCUIElementTypeButton才是按钮
            return StringUtils.contains(uiComponent.getClassName(), "Button") ||
                    strLength(uiComponent.getText()) <= IOS_MAX_BUTTON_TEXT;
        } else {
            if (StringUtils.equals(uiComponent.getClassName(), MiitUIClassEnum.BUTTON_VIEW.getValue())) {
                return true;
            }
            // 按钮文本里有勾选，不可能是按钮
            if (notButtonText(buttonText)) {
                return false;
            }
            return strLength(buttonText) <= ANDROID_MAX_BUTTON_TEXT;
        }
    }

    private boolean isButtonTextByOcr(RecognizeData.DataDTO data, CommonDetectInfo commonDetectInfo) {
        // 按钮文本里有勾选，不可能是按钮
        if (notButtonText(data.getText())) {
            return false;
        }
        if (commonDetectInfo.getTerminalTypeEnum() == TerminalTypeEnum.IOS) {
            // ios只有XCUIElementTypeButton才是按钮
            return strLength(data.getText()) <= IOS_MAX_BUTTON_TEXT;
        } else {
            return strLength(data.getText()) <= ANDROID_MAX_BUTTON_TEXT;
        }
    }

    private boolean notButtonText(String buttonText) {
        return StringUtils.contains(buttonText, "勾选") || StringUtils.contains(buttonText, "是否");
    }

    private int strLength(String str) {
        return Objects.isNull(str) ? 0 : str.trim().length();
    }

    private boolean hasAgreeButton(String fullContent, String agreeRegex) {
        return MiitWordKit.checkTextMeetTheRegex(fullContent, agreeRegex);
    }

    private boolean hasRefuseButton(String fullContent, String refuseRegex) {
        return MiitWordKit.checkTextMeetTheRegex(fullContent, refuseRegex);
    }

    protected boolean isLoginRegister(CommonDetectInfo commonDetectInfo, ResultDataLogBO resultDataLogBO) {
        return (commonDetectInfo.getTerminalTypeEnum() == TerminalTypeEnum.IOS && isLoginIosRegister(resultDataLogBO)) ||
                (commonDetectInfo.getTerminalTypeEnum() == TerminalTypeEnum.ANDROID && isLoginAndroidRegister(resultDataLogBO));
    }

    /**
     * 判断是否是登录注册页面
     *
     * @param resultDataLogBO
     * @return
     */
    private boolean isLoginAndroidRegister(ResultDataLogBO resultDataLogBO) {
        // 估算行高
        int lineHeight = resultDataLogBO.getUiDumpResult().getScreenHeight() / 40;
        UIDumpResult uiDumpResult = resultDataLogBO.getUiDumpResult();
        boolean isLogin = false;
        boolean isRegister = false;
        boolean isEdit = false;
        for (UIComponent uiComponent : uiDumpResult.getUiComponentList()) {
            String text = StringUtils.isBlank(uiComponent.getText()) ? uiComponent.getContentDesc() : uiComponent.getText();
            // 文本判断
            if ((StringUtils.contains(text, "登录"))
                    && uiComponent.getText().length() <= 10) {
                isLogin = true;
            }
            if ((StringUtils.contains(text, "注册"))
                    && uiComponent.getText().length() <= 10) {
                isRegister = true;
            }
            // 判断是否有文本输入框，包含文本框就是登录注册页面，文本输入框高度要小于3行
            if (StringUtils.equals(uiComponent.getClassName(), MiitUIClassEnum.EDIT_TEXT.getValue())
                    && uiComponent.getY2() - uiComponent.getY1() < lineHeight * 3) {
                isEdit = true;
            }
            if (isLogin && isRegister) {
                return true;
            } else if (isEdit) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否是登录注册页面
     *
     * @param resultDataLogBO
     * @return
     */
    private boolean isLoginIosRegister(ResultDataLogBO resultDataLogBO) {
        // 有时候会在登录界面弹出确认框，所以先根据文字长度来判断
        if (StringUtils.length(resultDataLogBO.getUiDumpResult().getFullText()) > 250) {
            return false;
        }
        // 估算行高
        int lineHeight = resultDataLogBO.getUiDumpResult().getScreenHeight() / 40;
        UIDumpResult uiDumpResult = resultDataLogBO.getUiDumpResult();
        boolean isLogin = false;
        boolean isRegister = false;
        for (UIComponent uiComponent : uiDumpResult.getUiComponentList()) {
            String text = StringUtils.isBlank(uiComponent.getText()) ? uiComponent.getContentDesc() : uiComponent.getText();
            // 文本判断
            if ((StringUtils.contains(text, "登录"))
                    && uiComponent.getText().length() <= 10 && uiComponent.isVisible()) {
                isLogin = true;
            }
            if ((StringUtils.contains(text, "注册"))
                    && uiComponent.getText().length() <= 10 && uiComponent.isVisible()) {
                isRegister = true;
            }
            if (isLogin && isRegister) {
                return true;
            }
            // 判断是否有文本输入框，包含文本框就是登录注册页面，文本输入框高度要小于2行
            if (StringUtils.equals(uiComponent.getXmlTag(), MiitUIClassEnum.IOS_EDIT_TEXT.getValue())
                    && uiComponent.getY2() - uiComponent.getY1() < lineHeight * 2) {
                return true;
            }
            if (StringUtils.equals(uiComponent.getXmlTag(), MiitUIClassEnum.IOS_EDIT_TEXT.getValue())
                    && StringUtils.contains(text, "请输入")
                    && uiComponent.getText().length() <= 10
                    && uiComponent.isVisible()) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否有默认选中隐私条款
     * @param commonDetectInfo
     * @param customDetectInfo
     * @param detectResult 声明
     */
    protected void checkPrivacyPolicySelectedByDefault(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo, DetectResult detectResult) {
        boolean hasCheckBox = false;
        List<ResultDataLogBO> applyResultDataLogBOList = commonDetectInfo.getResultDataLogs().stream()
                .filter(resultDataLogBO -> hasPrivacyPolicyTipsUi(resultDataLogBO, commonDetectInfo)).collect(Collectors.toList());
        // 遍历界面
        for (ResultDataLogBO resultDataLogBO : applyResultDataLogBOList) {
            // 申请隐私政策页面
            List<UIComponent> uiComponents = resultDataLogBO.getUiDumpResult().getUiComponentList();
            for (int index = 0; index < uiComponents.size(); index++) {
                UIComponent uiComponent = uiComponents.get(index);
                // 包含checkbox
                if (StringUtils.equals(uiComponent.getClassName(), MiitUIClassEnum.CHECK_BOX.getValue())
                        || (StringUtils.contains(uiComponent.getText(), "勾选") && uiComponent.getText().length() < 20)) {
                    hasCheckBox = true;
                    //  默认勾选 不合规
                    if (uiComponent.isChecked()) {
                        addNoComplianceImage(commonDetectInfo, detectResult, resultDataLogBO);
                    }
                    // 默认不勾选直接返回安全
                    else {
                        setNonInvolved(detectResult);
                    }
                    break;
                }
                if (StringUtils.equals(uiComponent.getClassName(), MiitUIClassEnum.IOS_BUTTON_VIEW.getValue())
                        && (StringUtils.containsAny(uiComponent.getText(), "复选框", "勾选") && uiComponent.getText().length() < 8)) {
                    hasCheckBox = true;
                    //  默认勾选 不合规
                    if (StringUtils.containsAny(uiComponent.getText(), "已选中", "已勾选")) {
                        addNoComplianceImage(commonDetectInfo, detectResult, resultDataLogBO);
                    }
                    // 默认不勾选直接返回安全
                    else {
                        setNonInvolved(detectResult);
                    }
                    break;
                }
                // 图片文字类型checkbox
                if (index >= 1
                        && ((StringUtils.contains(uiComponent.getText(), "同意") && uiComponent.getText().length() < 50)
                        || (StringUtils.contains(uiComponent.getContentDesc(), "同意") && uiComponent.getContentDesc().length() < 50))) {
                    UIComponent checkBoxView = uiComponents.get(index - 1);

                    // 可点击
                    if (UIDistinguishUtils.isImageCheckBoxView(checkBoxView)
                            // 判断按钮和文本是不是在同一行
                            && Math.abs(checkBoxView.getY1() - uiComponent.getY1()) <= 30) {
                        hasCheckBox = true;
                        if (checkBoxView.isSelected()) {
                            addNoComplianceImage(commonDetectInfo, detectResult, resultDataLogBO);
                        } else {
                            setNonInvolved(detectResult);
                        }
                        break;
                    }
                }
                String text = StringUtils.isBlank(uiComponent.getText()) ? uiComponent.getContentDesc() : uiComponent.getText();
                // 是否有隐私政策文本，如果有的话进入图片判断阶段。因为工具的限制有些CheckBox组件在xml中没有数据，需要通过图片识别去判断
                if (findPrivacyTextIndex(text) > 0
                        && (isPrivacyPolicyTipsLine(text) || hasPrivacyPolicyTipsText(text))
                        && StringUtils.isNotBlank(resultDataLogBO.getImgPath())
                        && StringUtils.length(text) <= 50
                        && commonDetectInfo.hasTensorflowModel()) {
                    try {
                        String imagePath = commonDetectInfo.getFilePath() + File.separator + resultDataLogBO.getImgPath();
                        // 把可能是CheckBox的图片裁切出来
                        BufferedImage image = UIDistinguishUtils.cropCheckBoxImage(imagePath, commonDetectInfo.getTerminalTypeEnum(),
                                resultDataLogBO.getUiDumpResult().getScreenWidth(), resultDataLogBO.getUiDumpResult().getScreenHeight(),
                                uiComponents, index, uiComponent.getX1(), uiComponent.getY1(),
                                uiComponent.getY2() - uiComponent.getY1());
                        // 把上一步裁切的图片进行识别是否CheckBox，以及是否选中的状态
                        CheckBoxStatusEnum status = UIDistinguishUtils.isCheckBoxChecked(
                                image,
                                commonDetectInfo.getTensorflowCheckBoxModelPath(),
                                commonDetectInfo.getTensorflowCheckedModelPath());
                        if (status == CheckBoxStatusEnum.CHECK) {
                            // 选中状态
                            hasCheckBox = true;
                            addNoComplianceImage(commonDetectInfo, detectResult, resultDataLogBO);
                        } else if (status == CheckBoxStatusEnum.UNCHECK) {
                            // 非选中状态
                            hasCheckBox = true;
                            setNonInvolved(detectResult);
                        }
                        break;
                    } catch (Exception e) {
                        log.error("image error", e);
                    }
                }
            }
            if (hasCheckBox) {
                break;
            }
            // 有时候会界面元素缺失，xml文件里没有隐私相关的字，以上检测手段都没找到checkbox，用ocr再去检测一遍，避免漏检测
            if (checkCheckBoxStatusByOcrList(resultDataLogBO, commonDetectInfo, detectResult)) {
                hasCheckBox = true;
                break;
            }
        }
        List<ResultDataLogBO> textUiList = new ArrayList<>(applyResultDataLogBOList);
        if (detectResult.getComplianceStatus() != MiitDetectStatusEnum.NON_COMPLIANCE) {
            // 检查包含隐私政策提示的登录或者注册界面是否都有CheckBox
            List<ResultDataLogBO> privacyTipsLoginUiList = getAllPrivacyTipsLoginAndRegisterUi(applyResultDataLogBOList);
            List<ResultDataLogBO> existCheckBoxLoginList = privacyTipsLoginUiList.stream()
                    .filter(ui -> existCheckBox(commonDetectInfo, ui))
                    .collect(Collectors.toList());
            if (!privacyTipsLoginUiList.isEmpty() && existCheckBoxLoginList.size() == privacyTipsLoginUiList.size()) {
                hasCheckBox = true;
                setNonInvolved(detectResult);
            }
            // 去掉有checkbox的登录界面
            textUiList.removeAll(existCheckBoxLoginList);
        }
        // 不包含弹出框
        if (!hasCheckBox) {
            // 判断语义内容
            for (ResultDataLogBO resultDataLogBO : textUiList) {
                if (Objects.isNull(resultDataLogBO.getUiDumpResult())) {
                    continue;
                }
                //获取整个页面的文本
                String pageText = resultDataLogBO.getUiDumpResult().getFullText();
                boolean secondCheck = true; //是否需要二次校验内容
                for (UIComponent uiComponent : resultDataLogBO.getUiDumpResult().getUiComponentList()) {
                    String fullText = uiComponent.getText() + uiComponent.getContentDesc();
                    if (fullText.length() > 50) {
                        continue;
                    }

                    boolean isCheckBoxWork = checkFullText(fullText, commonDetectInfo, customDetectInfo, detectResult, resultDataLogBO);
                    if(isCheckBoxWork) {
                        secondCheck = false;
                        break;
                    }
                }

                if (StringUtils.isNoneBlank(pageText) && secondCheck) {
                    //用户注册协议   隐私协议
                    secondCheckFullText(pageText.replaceAll("\n", ""), commonDetectInfo, customDetectInfo,
                            detectResult, resultDataLogBO);
                }
            }
        }
        // 不涉及 添加图片登录注册/申请权限截图
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_INVOLVED) {
            for (ResultDataLogBO resultDataLogBO : applyResultDataLogBOList) {
                addNoInvolvedImage(commonDetectInfo, detectResult, resultDataLogBO);
            }
        }
    }

    private boolean checkCheckBoxStatusByOcrList(ResultDataLogBO resultDataLogBO, CommonDetectInfo commonDetectInfo, DetectResult detectResult) {
        if (CollectionUtils.isEmpty(resultDataLogBO.getUiDumpResult().getOcrList())) {
            return false;
        }
        for (RecognizeData ocr:resultDataLogBO.getUiDumpResult().getOcrList()) {
            for (RecognizeData.DataDTO dataDTO:ocr.getData()) {
                if ((isPrivacyPolicyTipsLine(dataDTO.getText()) || hasPrivacyPolicyTipsText(dataDTO.getText()))
                        && StringUtils.isNotBlank(resultDataLogBO.getImgPath())
                        && commonDetectInfo.hasTensorflowModel()) {
                    try {
                        String imagePath = commonDetectInfo.getFilePath() + File.separator + resultDataLogBO.getImgPath();
                        BufferedImage image = UIDistinguishUtils.cropCheckBoxImage(imagePath, dataDTO);
                        CheckBoxStatusEnum status = UIDistinguishUtils.isCheckBoxChecked(
                                image,
                                commonDetectInfo.getTensorflowCheckBoxModelPath(),
                                commonDetectInfo.getTensorflowCheckedModelPath());
                        if (status == CheckBoxStatusEnum.CHECK) {
                            addNoComplianceImage(commonDetectInfo, detectResult, resultDataLogBO);
                            return true;
                        } else if (status == CheckBoxStatusEnum.UNCHECK) {
                            setNonInvolved(detectResult);
                            return true;
                        }
                    } catch (IOException e) {
                        e.getMessage();
                    }
                }
            }
        }
        return false;
    }

    private List<ResultDataLogBO> getAllPrivacyTipsLoginAndRegisterUi(List<ResultDataLogBO> resultDataLogBOList) {
        return resultDataLogBOList.stream()
                .filter(resultDataLogBO -> resultDataLogBO.getUiDumpResult().getUiType() == MiitUITypeEnum.LOGIN_RIGISTER.getValue())
                .filter(resultDataLogBO -> resultDataLogBO.getUiDumpResult().getUiComponentList().stream()
                        .anyMatch(uiComponent -> StringUtils.length(uiComponent.getText()) < 100 && findPrivacyTextIndex(uiComponent.getText()) >= 0))
                .collect(Collectors.toList());
    }

    private boolean existCheckBox(CommonDetectInfo commonDetectInfo, ResultDataLogBO resultDataLogBO) {
        List<UIComponent> uiComponentList = resultDataLogBO.getUiDumpResult().getUiComponentList();
        for (int index = 0; index < uiComponentList.size(); index++) {
            UIComponent uiComponent = uiComponentList.get(index);
            String buttonText = StringUtils.isNotBlank(uiComponent.getText()) ? uiComponent.getText() : uiComponent.getContentDesc();
            // 排除登录注册按钮，不能通过是否空字符去判断，因为iOS界面元素，checkbox按钮文本里有可能是“口”、“o”等字符
            // 为什么要把《和》也加进排除字符，因为有些登录界面的《隐私政策》是通过多个TextView拼接的，会把这些TextView都设置为可点击的
            if (strOrContains(buttonText, "登录", "注册", "《", "》", "协议")) {
                continue;
            }
            // 如果按钮是可点击（说明不是默认勾选的）或者是IOS按钮（因为ios的按钮无法通过xml文件判断是否可点击、可选中，只要是按钮就认为是非勾选的），判断周围的文本是否隐私条款
            if ((isIosCheckboxButton(uiComponent) || uiComponent.isClickable())) {
                // 后面的文本是否同意文本，并且挨在一起，以屏幕高度去计算，因为有可能是横屏的界面
                int boundX = resultDataLogBO.getUiDumpResult().getScreenHeight() - uiComponent.getX2();
                List<UIComponent> afterComponent = uiComponentList.stream()
                        .filter(c -> isRightComponent(c, uiComponent, boundX) && isGuidePrivacyPolicyText(c)).collect(Collectors.toList());
                if (afterComponent.stream().anyMatch(c -> StringUtils.contains(c.getText(), "同意"))
                        && afterComponent.stream().anyMatch(c -> strOrContains(c.getText(), "隐私", "协议"))) {
                    // 找出是同一行。用来判断图片类型按钮，是否为隐私条款的选中按钮
                    return true;
                }
                List<UIComponent> beforeComponent = uiComponentList.stream()
                        .filter(c -> isLeftComponent(c, uiComponent, boundX) && isGuidePrivacyPolicyText(c)).collect(Collectors.toList());
                if (beforeComponent.stream().anyMatch(c -> StringUtils.contains(c.getText(), "同意"))
                        && beforeComponent.stream().anyMatch(c -> strOrContains(c.getText(), "隐私", "协议"))) {
                    // 找出是同一行。用来判断图片类型按钮，是否为隐私条款的选中按钮
                    return true;
                }
            }
        }
        return false;
    }

    private boolean hasPrivacyPolicyTipsUi(ResultDataLogBO resultDataLogBO, CommonDetectInfo commonDetectInfo) {
        if (resultDataLogBO.getUiDumpResult() == null) {
            return false;
        }
        UIDumpResult result = resultDataLogBO.getUiDumpResult();
        String fullText = result.getFullText() + getOcrText(resultDataLogBO);
        if (hasPrivacyPolicyTipsText(fullText)
                && hasWord(fullText, "隐私", "个人信息保护", "声明")) {
            if (commonDetectInfo.getTerminalTypeEnum() == TerminalTypeEnum.ANDROID) {
                //  Android的数据需要把申请隐私政策页面纳入进来
                return (result.getUiType() == MiitUITypeEnum.APPLY_POLICY.getValue()
                        || result.getUiType() == MiitUITypeEnum.LOGIN_RIGISTER.getValue());
            } else {
                return (result.getUiType() == MiitUITypeEnum.APPLY_POLICY.getValue()
                        || result.getUiType() == MiitUITypeEnum.LOGIN_RIGISTER.getValue())
                        && StringUtils.length(result.getFullText()) < 1000;
            }
        }
        return false;
    }

    private int getUiCenterY(UIComponent uiComponent) {
        return (uiComponent.getY2() + uiComponent.getY1()) / 2;
    }

    private void setNonInvolved(DetectResult detectResult) {
        if (detectResult.getScreenshots() == null) {
            detectResult.setScreenshots(new HashSet<>());
        }
        detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
    }

    private boolean hasIosAgreeAndRefuseButton(ResultDataLogBO resultDataLogBO, CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        if (CollectionUtils.isEmpty(resultDataLogBO.getUiDumpResult().getUiComponentList())) {
            return false;
        }
        if (commonDetectInfo.getTerminalTypeEnum() != TerminalTypeEnum.IOS) {
            return false;
        }
        Map<String, String> keyWork = customDetectInfo.getKeyWordRegex();
        String agreeRegex = keyWork.get("point_10107_agree");
        String refuseRegex = keyWork.get("point_10107_refuse");
        boolean agree = resultDataLogBO.getUiDumpResult().getUiComponentList().stream().anyMatch(component -> isButtonTextByUi(component, commonDetectInfo)
                && MiitWordKit.checkTextMeetTheRegex(component.getText(), agreeRegex));
        boolean refuse = resultDataLogBO.getUiDumpResult().getUiComponentList().stream().anyMatch(component -> isButtonTextByUi(component, commonDetectInfo)
                && MiitWordKit.checkTextMeetTheRegex(component.getText(), refuseRegex));
        return agree && refuse;
    }

    private static boolean hasWord(String fullText, String... words) {
        if (StringUtils.isBlank(fullText) || words.length == 0) {
            return false;
        }
        return Arrays.stream(words).anyMatch(fullText::contains);
    }

    private static boolean checkWord(String fullText,String word1,String word2){
        return fullText.indexOf(word1) < fullText.indexOf(word2);
    }

    private boolean checkFullText(String fullText, CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                                  DetectResult detectResult,ResultDataLogBO resultDataLogBO){
        fullText = fullText.replaceAll("用户注册协议", "");
        // 有登录和注册按钮，文本里带有阅读，但是阅读文本前后没有可点击按钮
        if ((strOrContains(fullText, "登录","注册"))
                && (contains(fullText, "阅读") && aboutNotClickableComponent("阅读", resultDataLogBO))) {
            addNoComplianceImage(commonDetectInfo, detectResult, resultDataLogBO);
            return true;
        }
        if (hasPrivacyPolicyTipsText(fullText)
                && !hasIosAgreeAndRefuseButton(resultDataLogBO, commonDetectInfo, customDetectInfo)) {
            addNoComplianceImage(commonDetectInfo, detectResult, resultDataLogBO);
            return true;
        }
        return false;
    }

    /**
     * 是否隐私协议提示的那一行
     * @param text
     * @return
     */
    private boolean isPrivacyPolicyTipsLine(String text) {
        return StringUtils.contains(text, "《") && StringUtils.contains(text, "》");
    }

    /**
     * 是否包含隐私协议提示的文本
     * @param fullText
     * @return
     */
    private boolean hasPrivacyPolicyTipsText(String fullText) {
        return strOrContains(fullText, "登录", "注册", "使用", "点击按钮", "阅读", "继续") && (
                strOrContains(fullText, "即同意", "表示同意", "即视为") ||
                        strAndContains(fullText, "同意", "意味着") ||
                        strAndContains(fullText, "同意", "并注册") ||
                        strAndContains(fullText, "即为", "同意")
        ) &&  (checkWordSeq(fullText, "登录", "即同意") ||
                checkWordSeq(fullText, "登录", "即视为") ||
                checkWordSeq(fullText, "登录", "并视为") ||
                checkWordSeq(fullText, "登录","阅读并同意") ||
                checkWordSeq(fullText, "登录","并同意") ||

                checkWordSeq(fullText, "注册", "即同意") ||
                checkWordSeq(fullText, "注册","表示同意") ||
                checkWordSeq(fullText, "注册","即视为") ||
                checkWordSeq(fullText, "注册","阅读并同意") ||
                checkWordSeq(fullText, "注册","并同意") ||

                checkWordSeq(fullText, "同意","意味着") ||
                checkWordSeq(fullText, "同意","并注册") ||
                checkWordSeq(fullText, "即为","同意"));
    }

    protected static boolean checkWordSeq(String fullText, String word1, String word2) {
        return contains(fullText, word1) && contains(fullText, word2)  && checkWord(fullText, word1,word2);
    }

    protected static boolean contains(CharSequence seq, CharSequence searchSeq) {
        return StringUtils.contains(seq, searchSeq);
    }

    protected static boolean isIosCheckboxButton(UIComponent uiComponent) {
        // 选中框按钮并不会很大
        int buttonMaxSize = 40;
        // 普通按钮类型，有文字，且不是中文
        boolean isNormalCheckbox = StringUtils.equals(uiComponent.getClassName(), MiitUIClassEnum.IOS_BUTTON_VIEW.getValue())
                && uiComponent.getY2() - uiComponent.getY1() < buttonMaxSize
                && uiComponent.getX2() - uiComponent.getX1() < buttonMaxSize;
        // 图片按钮，且按钮宽高在24以内
        boolean isImageCheckbox = StringUtils.equals(uiComponent.getClassName(), MiitUIClassEnum.IOS_IMAGE_VIEW.getValue())
                && uiComponent.getY2() - uiComponent.getY1() < buttonMaxSize
                && uiComponent.getX2() - uiComponent.getX1() < buttonMaxSize;
        return isNormalCheckbox || isImageCheckbox;
    }

    /**
     * 周围没有可选中或者可点击按钮
     * @param text
     * @param resultDataLogBO
     * @return
     */
    private boolean aboutNotClickableComponent(String text, ResultDataLogBO resultDataLogBO) {
        List<UIComponent> componentList = resultDataLogBO.getUiDumpResult().getUiComponentList();
        for (int i=0; i<componentList.size(); i++) {
            UIComponent component = componentList.get(i);
            if (StringUtils.isNotBlank(component.getText()) && text.contains(component.getText())) {
                return rightNotClickableOrCheckedComponent(component, componentList, i) && leftNotClickableOrCheckedComponent(component, componentList, i);
            }
        }
        return false;
    }

    /**
     * 右边是不可点击元素或者默认选中按钮
     * @param currentComponent
     * @param componentList
     * @param index
     * @return
     */
    private boolean rightNotClickableOrCheckedComponent(UIComponent currentComponent, List<UIComponent> componentList, int index) {
        if (componentList.size() <= index + 1) {
            return true;
        }
        UIComponent targetComponent = componentList.get(index + 1);
        if (isRightComponent(targetComponent, currentComponent, 20)) {
            return !targetComponent.isClickable() || targetComponent.isChecked() || targetComponent.isSelected();
        }
        return true;
    }

    /**
     * 左边是不可点击元素或者默认选中按钮
     * @param currentComponent
     * @param componentList
     * @param index
     * @return
     */
    private boolean leftNotClickableOrCheckedComponent(UIComponent currentComponent, List<UIComponent> componentList, int index) {
        if (componentList.size() > index - 1) {
            return true;
        }
        UIComponent targetComponent = componentList.get(index - 1);
        if (isLeftComponent(targetComponent, currentComponent, 20)) {
            return !targetComponent.isClickable() || targetComponent.isChecked() || targetComponent.isSelected();
        }
        return true;
    }

    /**
     * 目标控件是否为当前控件的右下方一定区域
     *  * 例如：        ---------------------
     *      *   当前控件丨                    丨
     *      *         丨   控件在这个区域      丨
     *      *         丨                    丨
     * @param targetComponent
     * @param currentComponent
     * @return
     */
    private boolean isRightComponent(UIComponent targetComponent, UIComponent currentComponent, int boundX) {
        // 因为设备的屏幕可能大小不一，当前控件的高度，加上5像素的误差，作为高度基准
        int bound = (currentComponent.getY2() - currentComponent.getY1()) * 4;
        int height = targetComponent.getY2() - targetComponent.getY1();
        int y = targetComponent.getY2() - currentComponent.getY1();
        int x = targetComponent.getX2() - currentComponent.getX2();
        return height < bound && y > 0 && y < bound
                && x > 0 && x < boundX;
    }

    /**
     * 目标控件是否为当前控件的左下方一定区域
     * 例如：    ---------------------
     *         丨                   丨 当前控件
     *         丨   控件在这个区域     丨
     *         丨                   丨
     * @param targetComponent
     * @param currentComponent
     * @return
     */
    private boolean isLeftComponent(UIComponent targetComponent, UIComponent currentComponent, int boundX) {
        // 因为设备的屏幕可能大小不一，当前控件的高度，加上5像素的误差，作为高度基准
        int bound = (currentComponent.getY2() - currentComponent.getY1()) * 4;
        int height = targetComponent.getY2() - targetComponent.getY1();
        int y = targetComponent.getY2() - currentComponent.getY1();
        int x = currentComponent.getX1() - targetComponent.getX1();
        return height < bound && y > 0 && y < bound
                && x > 0 && x < boundX;
    }

    /**
     * 是否进入隐私政策的引导文本
     * @param component
     * @return
     */
    private boolean isGuidePrivacyPolicyText(UIComponent component) {
        int textLength = StringUtils.length(component.getText());
        // 需要有一定的文本长度
        return textLength > 0
                && textLength < 65;
    }

    /**
     * 针对勾选内容出现多行显示情况判断-比如哈罗出行
     * （登录
     * 即表示同意
     * 《用户协议》
     * 《隐私协议》
     * ）
     * @param fullText
     * @param commonDetectInfo
     * @param detectResult
     * @param resultDataLogBO
     */
    private void secondCheckFullText(String fullText, CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo, DetectResult detectResult,ResultDataLogBO resultDataLogBO){
        int privacyTextIndex = findPrivacyTextIndex(fullText);
        if (privacyTextIndex >= 0) {
            int descTextEndIndex = fullText.indexOf("《");
            String subText;
            // 截取隐私协议前的说明文字
            if (descTextEndIndex > 0 && descTextEndIndex < privacyTextIndex) {
                subText = subDescText(fullText, descTextEndIndex);
            } else {
                subText = subDescText(fullText, privacyTextIndex);
            }
            checkFullText(subText, commonDetectInfo, customDetectInfo, detectResult, resultDataLogBO);
        }
    }

    private String subDescText(String fullText, int endIndex) {
        int descMaxLength = 18;
        return fullText.substring((endIndex > descMaxLength ? endIndex - descMaxLength : 0), endIndex);
    }

    private int findPrivacyTextIndex(String fullText) {
        if (StringUtils.contains(fullText, "按钮")) {
            return -1;
        }
        return findAnyWordsIndex(fullText, "协议", "隐私", "条款", "政策");
    }

    private int findAnyWordsIndex(String fullText, String ...words) {
        int findTextIndex = -1;
        if (StringUtils.isBlank(fullText)) {
            return findTextIndex;
        }
        for (String word:words) {
            int index = fullText.indexOf(word);
            if (index >= 0) {
                if (findTextIndex == -1 || findTextIndex > index) {
                    findTextIndex = index;
                }
            }
        }
        return findTextIndex;
    }

    /**
     * 往前找是否有相关文字内容描述的界面
     * @param resultDataLogBOList
     * @param beginIndex
     * @return
     */
    protected ResultDataLogBO findBeforeAuthorPopup(List<ResultDataLogBO> resultDataLogBOList, int beginIndex) {
        // 往前找是否有相关文字内容描述的界面
        for (; beginIndex>=0; beginIndex--) {
            ResultDataLogBO resultDataLogBO = resultDataLogBOList.get(beginIndex);
            if (equalsUiType(resultDataLogBO, Arrays.asList(MiitUITypeEnum.POLICY_DETAIL, MiitUITypeEnum.BEFORE_AUTHOR_POPUP, MiitUITypeEnum.APPLY_POLICY))
                    // 超过1500个字的是隐私详情了
                    && resultDataLogBO.getUiDumpResult().getFullText().length() < 1500) {
                return resultDataLogBO;
            }
        }
        return null;
    }
    /**
     * 往前找是否有相关文字内容描述的界面
     * @param resultDataLogBOList
     * @param beginIndex
     * @return
     */
    protected ResultDataLogBO findBeforePolicyPopup(List<ResultDataLogBO> resultDataLogBOList, int beginIndex) {
        // 往前找是否有相关文字内容描述的界面
        for (; beginIndex >= 0; beginIndex--) {
            ResultDataLogBO result = resultDataLogBOList.get(beginIndex);
            if (isPolicyPopup(result)) {
                return result;
            }
        }
        return null;
    }

    protected boolean uiFillTextContains(ResultDataLogBO result, String text) {
        if (result.getUiDumpResult() == null) {
            return false;
        }
        return result.getUiDumpResult().getFullText().contains(text);
    }

    protected boolean isPolicyPopup(ResultDataLogBO result) {
        return equalsUiType(result, Arrays.asList(MiitUITypeEnum.POLICY_DETAIL, MiitUITypeEnum.APPLY_POLICY, MiitUITypeEnum.BEFORE_AUTHOR_POPUP))
                || equalsUiType(result, MiitUITypeEnum.OTHER) && uiFillTextContains(result, "权限");
    }

    protected boolean isPermissionPopup(TerminalTypeEnum terminalTypeEnum, ResultDataLogBO result) {
        return equalsUiType(result, Arrays.asList(MiitUITypeEnum.AGREE_PERMISSION, MiitUITypeEnum.APPLY_PERMISSION, MiitUITypeEnum.DISAGREE_PERMISSION))
                && (terminalTypeEnum == TerminalTypeEnum.ANDROID || isIosPrivacyPermission(result));
    }

    protected boolean isClickTextEvent(ResultDataLogBO result) {
        if (result.getDataTag() == MiitDataTypeEnum.UI_EVENT.getValue()) {
            ResultDataLogIosDetailsBO detailsBO = CommonUtil.jsonToBean(result.getDetails(),
                    new TypeReference<ResultDataLogIosDetailsBO>() {
                    });
            if (detailsBO != null
                    && !isApplyPermissionDialogButtonText(detailsBO)
                    && !isApplyUpdateAppDialogButtonText(detailsBO)) {
                return StringUtils.isNotBlank(detailsBO.getText());
            }
        }
        return false;
    }

    protected boolean isApplyUpdateAppDialogButtonText(ResultDataLogIosDetailsBO detailsBO) {
        return strOrContains(detailsBO.getText(), "忽略", "版本", "同意", "授权");
    }

    /**
     * 检查频繁申请权限
     *
     * @param commonDetectInfo
     * @param detectResult
     */
    protected void checkRepeatedlyApplyPermission(CommonDetectInfo commonDetectInfo, DetectResult detectResult) {
        DetectPointCommonHelper.checkRepeatedlyApplyPermission(commonDetectInfo, detectResult);
    }

    /**
     * 检查频繁申请权限
     * @param commonDetectInfo
     * @param detectResult
     */
    protected void checkRepeatedlyApplyPermission(CommonDetectInfo commonDetectInfo, DetectResult detectResult, boolean allowImageDuplication) {
        DetectPointCommonHelper.checkRepeatedlyApplyPermission(commonDetectInfo, detectResult, allowImageDuplication);
    }
    /**
     * 是否点击了权限弹框的两个按钮之一
     * @param resultDataLogBO
     * @return
     */
    protected boolean isClickApplyPermissionDialogEvent(ResultDataLogBO resultDataLogBO) {
        if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_EVENT.getValue()) {
            ResultDataLogIosDetailsBO detailsBO = CommonUtil.jsonToBean(resultDataLogBO.getDetails(),
                    new TypeReference<ResultDataLogIosDetailsBO>() {
                    });
            if (detailsBO != null) {
                return isApplyPermissionDialogButtonText(detailsBO);
            }
        }
        return false;
    }

    /**
     * 同意隐私弹框
     * @param resultDataLogBO
     * @param customDetectInfo
     * @return
     */
    protected boolean isClickPrivacyPolicyDialogAgreeButton(ResultDataLogBO resultDataLogBO, CustomDetectInfo customDetectInfo) {
        if (resultDataLogBO.getDataTag() == MiitDataTypeEnum.UI_EVENT.getValue()) {
            ResultDataLogIosDetailsBO detailsBO = CommonUtil.jsonToBean(resultDataLogBO.getDetails(),
                    new TypeReference<ResultDataLogIosDetailsBO>() {
                    });
            if (detailsBO != null) {
                // 没有文本，无法进行判断，默认是隐私弹框的点击
                if (StringUtils.isEmpty(detailsBO.getText())) {
                    return true;
                }
                Map<String, String> keyWork = customDetectInfo.getKeyWordRegex();
                String agreeRegex = keyWork.get("point_10107_agree");
                // 点击隐私弹窗的同意按钮
                if (MiitWordKit.checkTextMeetTheRegex(detailsBO.getText(), agreeRegex)) {
                    return true;
                }
                // 点击隐私文本
                return StringUtils.contains(detailsBO.getText(), "隐私");
            }
        }
        return false;
    }

    /**
     * 检查拒绝权限后。重启后是否再次申请获取权限
     * @param commonDetectInfo
     * @param customDetectInfo
     * @param detectResult
     */
    protected void checkRestartApplyPermission(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo, DetectResult detectResult) {
        checkRestartApplyPermission(commonDetectInfo, customDetectInfo, detectResult, Collections.emptyList());
    }

    /**
     * 检查拒绝权限后。重启后是否再次申请获取权限
     * @param commonDetectInfo
     * @param detectResult
     * @param ignorePermissionNames
     * @return
     */
    protected void checkRestartApplyPermission(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo,
                                               DetectResult detectResult, List<String> ignorePermissionNames) {
        // 遍历界面
        for (int index = 0; index < commonDetectInfo.getResultDataLogs().size(); index++) {
            //权限拒绝页面
            ResultDataLogBO refuseResult = commonDetectInfo.getResultDataLogs().get(index);
            if (refuseResult.getUiDumpResult() != null
                    && refuseResult.getUiDumpResult().getUiType() == MiitUITypeEnum.DISAGREE_PERMISSION.getValue()
                    && refuseResult.getType() == ResultDataLogBoTypeEnum.PERMISSION_REJECT.type) {
                // 申请权限页面(获取权限文本)
                String permissionText = refuseResult.getUiDumpResult().getPermissionText();
                if (ignorePermissionNames.stream().anyMatch(permissionText::contains)) {
                    continue;
                }
                for (int reApplyIndex = index + 1; reApplyIndex < commonDetectInfo.getResultDataLogs().size(); reApplyIndex++) {
                    // 再次申请权限页面
                    ResultDataLogBO reApplyResult = commonDetectInfo.getResultDataLogs().get(reApplyIndex);
                    // type变化 应用重启
                    if (reApplyResult.getType().intValue() == refuseResult.getType().intValue()
                            || reApplyResult.getType() != ResultDataLogBoTypeEnum.PERMISSION_AGREE.type) {
                        continue;
                    }
                    /*
                     * 有操作页面的点击事件，不用再往下检查，因为不符合拒绝后重新打开应用自动弹出权限申请的条件。后续就算有同样的权限弹窗也很可能是该动作触发的
                     * 如果是点击隐私弹框的同意按钮是允许的，因为有些应用启动后会弹出隐私弹框，需要点同意才能继续
                     */
                    if (isClickPageEvent(reApplyResult)
                            && !isClickPrivacyPolicyDialogAgreeButton(reApplyResult, customDetectInfo)) {
                        break;
                    }
                    // 频繁申请权限(重启应用后)
                    if (reApplyResult.getUiDumpResult() != null &&
                            StringUtils.equals(reApplyResult.getUiDumpResult().getPermissionText(), permissionText)) {
                        addNoComplianceImage(commonDetectInfo, detectResult, refuseResult);
                        addNoComplianceImage(commonDetectInfo, detectResult, reApplyResult);
                        break;
                    }
                }
            }
        }
    }


    protected boolean equalsUiType(ResultDataLogBO resultDataLogBO, MiitUITypeEnum type) {
        return equalsUiType(resultDataLogBO, Collections.singletonList(type));
    }

    protected boolean equalsUiType(ResultDataLogBO resultDataLogBO, List<MiitUITypeEnum> typeList) {
        return resultDataLogBO.getUiDumpResult() != null &&
                typeList.stream().anyMatch(miitUITypeEnum -> miitUITypeEnum.getValue() == resultDataLogBO.getUiDumpResult().getUiType());
    }

    /**
     * 根据截图时间排序
     * @param detectResult
     */
    protected void sortScreenshot(DetectResult detectResult) {
        if (CollectionUtils.isEmpty(detectResult.getScreenshots())) {
            return;
        }
        Pattern pattern = Pattern.compile("/([0-9]+)/screen\\.png");
        Set<String> sortedScreenshots = detectResult.getScreenshots().stream()
                .sorted(Comparator.comparingLong(path -> {
                    Matcher matcher = pattern.matcher(path);
                    if (matcher.find()) {
                        return Long.parseLong(matcher.group(1));
                    } else {
                        return 0L;
                    }
                }))
                .collect(Collectors.toCollection(LinkedHashSet::new));
        detectResult.setScreenshots(sortedScreenshots);
    }

    protected List<ActionNetwork> getActionNetworkList(List<TPrivacySensitiveWord> sensitiveWordList) {
        return sensitiveWordList.stream().map(this::buildActionNetwork).collect(Collectors.toList());
    }

    protected ActionNetwork buildActionNetwork(TPrivacySensitiveWord word) {
        ActionNetwork actionNetwork = new ActionNetwork();
        actionNetwork.setActionId(word.getId());
        actionNetwork.setActionTime(word.getActionTime());
        actionNetwork.setAddress(word.getAddress());
        actionNetwork.setAttributively(word.getAttributively());
        actionNetwork.setCode(word.getCode());
        actionNetwork.setBehaviorStage(word.getBehaviorStage());
        actionNetwork.setCookie(word.getCookie());
        actionNetwork.setExecutor(word.getExecutor());
        actionNetwork.setExecutorType(word.getExecutorType());
        actionNetwork.setHost(word.getHost());
        actionNetwork.setIp(word.getIp());
        actionNetwork.setPort(word.getPort());
        actionNetwork.setProtocol(word.getProtocol());
        actionNetwork.setPackageName(word.getPackageName());
        actionNetwork.setMethod(word.getMethod());
        actionNetwork.setSdkIds(word.getSdkIds());
        actionNetwork.setUrl(word.getUrl());
        return actionNetwork;
    }

    protected List<ActionSharedPerfs> getActionPrefsList(List<TPrivacySharedPrefs> sensitiveWordList) {
        return sensitiveWordList.stream().map(this::buildActionPrefs).collect(Collectors.toList());
    }

    protected ActionSharedPerfs buildActionPrefs(TPrivacySharedPrefs prefs) {
        ActionSharedPerfs actionNetwork = new ActionSharedPerfs();
        actionNetwork.setActionId(prefs.getId());
        actionNetwork.setActionTime(prefs.getActionTime());
        actionNetwork.setBehaviorStage(prefs.getBehaviorStage());
        actionNetwork.setExecutor(prefs.getExecutor());
        actionNetwork.setExecutorType(prefs.getExecutorType());
        actionNetwork.setPackageName(prefs.getPackageName());
        actionNetwork.setSdkIds(prefs.getSdkIds());
        actionNetwork.setCode(prefs.getContent());
        return actionNetwork;
    }

    protected List<ActionOutSide> getActionOutsideList(List<TPrivacyOutsideAddress> outsideAddressList) {
        return outsideAddressList.stream().map(this::buildActionOutSide).collect(Collectors.toList());
    }

    protected ActionOutSide buildActionOutSide(TPrivacyOutsideAddress address) {
        ActionOutSide actionOutSide = new ActionOutSide();
        actionOutSide.setActionId(address.getId());
        actionOutSide.setActionTime(address.getActionTime());
        actionOutSide.setAddress(address.getAddress());
        actionOutSide.setOutside(address.getOutside());
        actionOutSide.setBehaviorStage(address.getBehaviorStage());
        actionOutSide.setCookie(address.getCookie());
        actionOutSide.setExecutor(address.getExecutor());
        actionOutSide.setExecutorType(address.getExecutorType());
        actionOutSide.setHost(address.getHost());
        actionOutSide.setIp(address.getIp());
        actionOutSide.setPort(address.getPort());
        actionOutSide.setProtocol(address.getProtocol());
        actionOutSide.setPackageName(address.getPackageName());
        actionOutSide.setMethod(address.getRequestMethod());
        actionOutSide.setSdkIds(address.getSdkIds());
        actionOutSide.setUrl(address.getUrl());
        return actionOutSide;
    }

    /**
     * 检查拒绝权限后是否退出APP
     * @param commonDetectInfo
     * @return
     */
    protected List<ResultDataLogBO> checkRejectPermissionQuitApp(CommonDetectInfo commonDetectInfo) {
        // 申请权限的界面
        List<ResultDataLogBO> applyList = new ArrayList<>();
        // 遍历界面
        for (int index = 0; index < commonDetectInfo.getResultDataLogs().size(); index++) {
            ResultDataLogBO resultDataLogBO = commonDetectInfo.getResultDataLogs().get(index);
            if (resultDataLogBO.getUiDumpResult() != null
                    && resultDataLogBO.getUiDumpResult().getUiType() == MiitUITypeEnum.DISAGREE_PERMISSION.getValue()
                    && isIosPrivacyPermission(resultDataLogBO)) {
                // 拒绝权限后应用退出
                if (StringUtils.equals(resultDataLogBO.getRunStatus(), MiitRunStatusEnum.Death.getValue())) {
                    applyList.add(resultDataLogBO);
                }
            }
        }
        return applyList;
    }

    private static final List<String> IGNORE_LIST = Arrays.asList("电话", "存储");

    /**
     * 所有行为阶段的截图中，是否有app开始的时候是否连续弹出申请权限检查
     * @param commonDetectInfo
     * @param actionNameList
     * @param detectResult
     * @return
     */
    protected boolean checkAllBehaviorStageContinuousApplyPermission(CommonDetectInfo commonDetectInfo,
                                                                     List<String> actionNameList,
                                                                     DetectResult detectResult) {
        return commonDetectInfo.getResultDataLogs()
                .stream()
                // 可能某个阶段的截图不全，所以要4个阶段都检测一遍
                .collect(Collectors.groupingBy(ResultDataLogBO::getType))
                .values()
                .stream()
                .anyMatch(uiList -> checkContinuousApplyPermission(commonDetectInfo, uiList, actionNameList, detectResult));
    }

    /**
     * app开始的时候是否连续弹出申请权限检查
     * @param commonDetectInfo
     * @param uiList
     * @param actionNameList
     * @param detectResult
     * @return
     */
    protected boolean checkContinuousApplyPermission(
            CommonDetectInfo commonDetectInfo,
            List<ResultDataLogBO> uiList,
            List<String> actionNameList,
            DetectResult detectResult) {
        // app开始的时候是否连续弹出申请权限检查
        boolean isContinuousApply = false;
        // 是否已经遍历到权限页面
        boolean startPermission = false;
        for (ResultDataLogBO resultDataLogBO : uiList) {
            // 如果是非权限申请界面、非隐私弹窗界面、有文字的点击事件，说明已经不是在开始阶段了
            if (!isPolicyPopup(resultDataLogBO)
                    && !isPermissionPopup(commonDetectInfo.getTerminalTypeEnum(), resultDataLogBO)
                    && isClickTextEvent(resultDataLogBO)) {
                break;
            }
            if (isPermissionPopup(commonDetectInfo.getTerminalTypeEnum(), resultDataLogBO)) {
                startPermission = true;
                TApplyPermission permission = findApplyPermissionRegex(commonDetectInfo, resultDataLogBO.getUiDumpResult());
                if (IGNORE_LIST.stream().noneMatch(ignore -> permission.getApplyName().contains(ignore))
                        && !actionNameList.contains(permission.getApplyName())) {
                    isContinuousApply = true;
                    addNoComplianceImage(commonDetectInfo, detectResult, resultDataLogBO);
                    actionNameList.add(permission.getApplyName());
                }
            } else if (startPermission) {
                // 权限截图检查开始后，遇到第一个非权限截图，可以结束检查了
                break;
            }
        }
        return isContinuousApply;
    }

    /**
     * 检测字体大小
     * @param commonDetectInfo
     */
    protected boolean checkPrivacyPolicyTextSizeAndSpacingInvaild(CommonDetectInfo commonDetectInfo) {
        List<RecognizeData> ocrResults = commonDetectInfo.getPrivacyPolicyImgOcrResult();
        if (ocrResults == null) {
            return false;
        }
        // 上一行的下方y轴坐标点
        List<Integer> textSizeList = new ArrayList<>();
        for (RecognizeData res: ocrResults) {
            for (RecognizeData.DataDTO data:res.getData()) {
                // 在隐私文本中，且坐标点为4个，分别为左上，右上，右下，左下
                if (StringUtils.isNotBlank(data.getText())
                        && data.getTextBoxPosition().size() == 4) {
                    List<Integer> topPoint = data.getTextBoxPosition().get(0);
                    List<Integer> bottomPoint = data.getTextBoxPosition().get(2);
                    // 当前行的上下方y轴坐标点
                    int currentLineTopPointY = topPoint.get(1);
                    int currentLineBottomPointY = bottomPoint.get(1);
                    // 找出最小的字体高度
                    int textSize =  currentLineBottomPointY - currentLineTopPointY;
                    // 取小的作为字体大小
                    textSizeList.add(textSize);
                }
            }
        }
        /*
         * 计算字体平均值，把偏离出期望值的字体大小剔除掉，保留截图中字体大小相近的这些行，作为隐私政策主体，计算最终的字体大小，
         * 一开始使用ocr出的文字是否在隐私文本里来过滤出隐私政策文本的行数据，但是拿去ocr的图片是经过压缩的。
         * 图片中字体太小的情况下ocr返回的文字准确性太低，导致这些识别出的文本行被判断不包含在隐私文本内，但是实际上都是隐私文本
         */
        double averageTextSize = textSizeList.stream().collect(Collectors.averagingInt(size -> size));
        // 方差
        double variance = textSizeList.stream().mapToDouble(size -> Math.pow(size - averageTextSize, 2)).sum() / textSizeList.size();
        // 标准差
        double standardDeviation = Math.sqrt(variance);
        // 只取2个标准差内的字体大小
        List<Integer> minTextSizeList = textSizeList.stream().filter(size -> Math.abs(size - averageTextSize) <= 2 * standardDeviation).collect(Collectors.toList());
        double minTextSize = minTextSizeList.stream()
                .collect(Collectors.averagingInt(size -> size));
        // 这里有可能因为ocr的坐标标记问题导致，上下行坐标点有重叠，minTextLineSpacing 为负的，所以不判断行间距
        return minTextSize < commonDetectInfo.getPrivacyPolicyMinTextSize() && minTextSize > 0;
    }

    /**
     * 检查隐私政策中是否有个人信息的更改、删除、注销途径
     * @param commonDetectInfo
     * @param detectResult
     * @param conclusionList
     * @param suggestionList
     */
    protected void checkHowToModifyPersonalInfo(CommonDetectInfo commonDetectInfo, DetectResult detectResult,
                                                List<String> conclusionList, List<String> suggestionList) {
        // 先进行语义识别
        if (commonDetectInfo.isNlpSuccess()) {
            List<DetectPointIdentifyItems.DetectPoint> noIdentifyItemList = commonDetectInfo.getNlpResponse().noIdentifyItemList(POINT_PERSONAL_INFO.items);
            // 语义识别里面没有找到的项目，判断是否有正则匹配规则，有的话进行正则匹配
            List<String> nonWordList = noIdentifyItemList
                    .stream()
                    .filter(item -> StringUtils.isNotBlank(item.regex))
                    .filter(item -> !Pattern.compile(item.regex).matcher(commonDetectInfo.getPrivacyPolicyContent()).find())
                    .map(item -> item.nlpItemName)
                    .collect(Collectors.toList());
            if (!nonWordList.isEmpty()) {
                conclusionList.add(String.format("%s未在隐私中说明", String.join("、", nonWordList)));
                suggestionList.add(String.format("请在隐私政策中提供有效的%s途径", nonWordList));
            }
            detectResult.setPrivacyPolicyFragment(commonDetectInfo.getNlpResponse().getIdentifyFragment(POINT_PERSONAL_INFO.items));
        } else {
            matcherWord(commonDetectInfo, detectResult, conclusionList, suggestionList);
        }
    }

    private void matcherWord(CommonDetectInfo commonDetectInfo, DetectResult detectResult,
                             List<String> conclusionList, List<String> suggestionList) {
        // 隐私政策截图截图地址
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        List<String> nonWordList = DetectPointIdentifyItems.allItems()
                .stream()
                .filter(item -> !Pattern.compile(item.regex).matcher(commonDetectInfo.getPrivacyPolicyContent()).find())
                .map(item -> item.nlpItemName)
                .distinct()
                .collect(Collectors.toList());
        if (!nonWordList.isEmpty()) {
            conclusionList.add(String.format("%s未在隐私中说明", String.join("、", nonWordList)));
            suggestionList.add(String.format("请在隐私政策中提供有效的%s途径", nonWordList));
        }
        Set<String> keyWordList = DetectPointIdentifyItems.allItems().stream().map(item -> item.regex).collect(Collectors.toSet());
        detectResult.setPrivacyPolicyFragment(MiitWordKit.defaultKeywordExtractionFromContent(commonDetectInfo.getPrivacyPolicyContent(), keyWordList));
    }

    /**
     * 检测是否有不在隐私文本内的收集隐私的行为
     * @param commonDetectInfo
     * @param customDetectInfo
     * @param detectResult
     * @param actionAnalyseList
     * @return
     */
    protected List<String> notInPrivacyPolicyAction(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo, DetectResult detectResult, List<ActionAnalyse> actionAnalyseList) {
        Set<String> actionRules = new HashSet<>();
        Map<Long, String> decideRules = customDetectInfo.getActionWithKeyRegex();
        List<ActionAnalyse> collectActionList = actionAnalyseList.stream()
                .filter(ActionAnalyse::getPersonal)
                .filter(StreamUtils.distinctByKey(ActionAnalyse::getActionId))
                .filter(a ->  {
                    String keyWord = decideRules.get(a.getActionId());
                    if (StringUtils.isBlank(keyWord)) {
                        return false;
                    }
                    if (StringUtils.isBlank(commonDetectInfo.getPrivacyPolicyContent())) {
                        return true;
                    }
                    actionRules.add(keyWord);
                    return !Pattern.compile(keyWord).matcher(commonDetectInfo.getPrivacyPolicyContent()).find();
                })
                .collect(Collectors.toList());
        detectResult.setPrivacyPolicyFragment(MiitWordKit.defaultKeywordExtractionFromContent(commonDetectInfo.getPrivacyPolicyContent(), actionRules));
        detectResult.setAnalysisResult(collectActionList);
        return collectActionList.stream().map(ActionAnalyse::getActionName)
                .distinct().collect(Collectors.toList());
    }


    /**
     * 检测是否有不在隐私文本内的收集隐私的权限
     * @param commonDetectInfo
     * @param detectResult
     * @return
     */
    protected List<String> notInPrivacyPolicyPermission(CommonDetectInfo commonDetectInfo, DetectResult detectResult) {
        Set<String> applyActionNameList = new HashSet<>();
        // 遍历界面
        for (int index = 0; index < commonDetectInfo.getResultDataLogs().size(); index++) {
            ResultDataLogBO resultDataLogBO = commonDetectInfo.getResultDataLogs().get(index);
            if (isPermissionPopup(commonDetectInfo.getTerminalTypeEnum(), resultDataLogBO) && !isPolicyPopup(resultDataLogBO)) {
                TApplyPermission permission = findApplyPermissionRegex(commonDetectInfo, resultDataLogBO.getUiDumpResult());
                if (StringUtils.isNoneBlank(commonDetectInfo.getPrivacyPolicyContent()) && !Pattern.compile(permission.getRegex()).matcher(commonDetectInfo.getPrivacyPolicyContent()).find()) {
                    // 申请个人信息权限界面
                    addNoComplianceImage(commonDetectInfo, detectResult, resultDataLogBO);
                    applyActionNameList.add(permission.getApplyName());
                }
            }
        }
        return new ArrayList<>(applyActionNameList);
    }

    /**
     * 是否包含推荐内容
     * @param commonDetectInfo
     * @param customDetectInfo
     * @param detectResult
     * @return
     */
    protected boolean checkHaveRecommendContent(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo, DetectResult detectResult) {
        // 判断依据 一
        String privacyPolicyText = this.getBaseTargetPrivacyPolicyText(commonDetectInfo, customDetectInfo);
        // 判断依据 二，存放特定的截图
        Set<String> images = new HashSet<>();
        // 获取关于推送类截图
        List<ResultDataLogBO> resultDataLogs = MiitLogKit.filterByTag(commonDetectInfo.getResultDataLogs(), MiitDataTypeEnum.UI_PERSONAL);
        if (!org.springframework.util.CollectionUtils.isEmpty(resultDataLogs)) {
            // 根据时间获取对应tag的图片 字符串去重
            List<String> md5List = new ArrayList<>();
            for (ResultDataLogBO resultDataLogBO : resultDataLogs) {
                if(resultDataLogBO.getUiDumpResult()==null) {
                    continue;
                }
                String fullText = resultDataLogBO.getUiDumpResult().getFullText();
                String privacyHtmlRegex = cn.ijiami.detection.utils.SeleniumUtils.privacyHtmlRegex;
                boolean isPrivacy = cn.ijiami.detection.helper.PrivacyPolicyHtmlHelper.isPrivacyDetail(fullText, privacyHtmlRegex);
                if(StringUtils.isNoneBlank(fullText) && fullText.length()>cn.ijiami.detection.utils.ConstantsUtils.PARSER_PRIVACY_DETAIL_MAX_TEXT_LENGTH && isPrivacy) {
                	continue;
                }
                
                // 误判太多了，截图好多是隐私政策页面20210706
                if (notRecommendPage(resultDataLogBO, commonDetectInfo)) {
                    continue;
                }
                //并判断页面是否存在定推关键词
                String md5 = getMD5Str(fullText);
                Set<String> decideRuleKeys = customDetectInfo.getDecideRuleKeys();
                String targetText = MiitWordKit.defaultKeywordExtractionFromContent(fullText, decideRuleKeys);

                if (!md5List.contains(md5) && org.apache.commons.lang.StringUtils.isNotBlank(targetText)) {
                    String absolutePath = commonDetectInfo.getFilePath() + File.separator + resultDataLogBO.getImgPath();
                    if (MiitLogKit.isFileExist(absolutePath)) {
                        images.add(absolutePath);
                    }
                    md5List.add(md5);
                }
            }
        }
        detectResult.setPrivacyPolicyFragment(privacyPolicyText);
        detectResult.setScreenshots(images);
        // 存放隐私政策截图
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        // 无隐私政策，或者有隐私政策，但是没有关键词片段，直接不涉及
        if (commonDetectInfo.nonHasPrivacyPolicy() || org.apache.commons.lang.StringUtils.isBlank(privacyPolicyText)) {
            return false;
        }
        // 有涉及到的字样，且有截图，则算不涉及
        if (org.apache.commons.lang.StringUtils.isNotBlank(privacyPolicyText) && !org.springframework.util.CollectionUtils.isEmpty(images)) {
            return false;
        }
        
        
        // 没有关键词片段，直接不涉及
        if (images.isEmpty()) {
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
            return false;
        } else {
            // 有涉及到的字样，且有截图，则算不涉及
            if (org.apache.commons.lang.StringUtils.isNotBlank(privacyPolicyText) && !CollectionUtils.isEmpty(images)) {
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
                return false;
            } else {
                detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            }
        }
        
        return true;
    }

    private boolean notRecommendPage(ResultDataLogBO resultDataLogBO, CommonDetectInfo commonDetectInfo) {
        String uiText = getUiText(resultDataLogBO);
        if (commonDetectInfo.getTerminalTypeEnum() == TerminalTypeEnum.IOS) {
            return resultDataLogBO.getUiDumpResult().getUiType() != MiitUITypeEnum.OTHER.getValue()
                    || (org.apache.commons.lang.StringUtils.isNotBlank(uiText) && uiText.contains("隐私政策") && org.apache.commons.lang3.StringUtils.length(uiText) > 1000);
        } else {
            return strOrContains(uiText, "用户协议", "隐私政策") && (
                    StringUtils.length(uiText) > 1000
                            || strOrContains(uiText, "《", "》"));
        }
    }

    private String getUiText(ResultDataLogBO resultDataLogBO) {
        if (org.apache.commons.lang.StringUtils.isNotBlank(resultDataLogBO.getUiDumpResult().getFullText())) {
            // 移除url，避免影响字体长度判断
            return resultDataLogBO.getUiDumpResult().getFullText().replaceAll(PinfoConstant.URL_REGEX, "");
        } else {
            return org.apache.commons.lang.StringUtils.EMPTY;
        }
    }

    /**
     * 检测权限前是否有弹窗说明使用
     * @param commonDetectInfo
     */
    protected CheckPermissionPopupResult permissionPopupCheck(CommonDetectInfo commonDetectInfo) {
        CheckPermissionPopupResult result = new CheckPermissionPopupResult();
        List<PermissionPopupCheck> noInvolvedCheckList = new ArrayList<>();
        List<PermissionPopupCheck> noComplianceCheckList = new ArrayList<>();
        // 遍历正常流程的界面
        commonDetectInfo.getResultDataLogs()
                .stream()
                .collect(Collectors.groupingBy(ResultDataLogBO::getType))
                .values()
                // 每个阶段都要检查，避免有些阶段截图不全
                .forEach(commonUiList -> checkPermission(commonDetectInfo, commonUiList, noInvolvedCheckList, noComplianceCheckList));
        // 转换数据
        Set<String> applyActionNameList = new HashSet<>();
        List<ResultDataLogBO> noInvolvedList = new ArrayList<>();
        List<ResultDataLogBO> noComplianceList = new ArrayList<>();
        noInvolvedCheckList.forEach(noInvolved -> {
            noInvolvedList.addAll(noInvolved.getUiList());
        });
        noComplianceCheckList.stream()
                // 有可能截图不全的原因，导致1阶段不合规，2阶段却有权限说明截图为合规，所以要把合规的排除掉
                .filter(noCompliance -> noInvolvedCheckList.stream().noneMatch(noInvolved -> noCompliance.getActionName().equals(noInvolved.getActionName())))
                .forEach(noCompliance -> {
                    applyActionNameList.add(noCompliance.getActionName());
                    noComplianceList.addAll(noCompliance.getUiList());
                });
        if (noComplianceList.isEmpty()) {
            result.getImageLogsList().addAll(noInvolvedList);
            result.setNonInvolved(true);
        } else {
            result.getImageLogsList().addAll(noComplianceList);
            result.getPermissionNameList().addAll(applyActionNameList);
            result.setNonInvolved(false);
        }
        return result;
    }

    public boolean isAppQuitAfterDisagreePermission(CommonDetectInfo commonDetectInfo, DetectResult detectResult) {
        for (int index = 0; index < commonDetectInfo.getResultDataLogs().size(); index++) {
            ResultDataLogBO resultDataLogBO = commonDetectInfo.getResultDataLogs().get(index);
            if (resultDataLogBO.getUiDumpResult() != null &&
                    resultDataLogBO.getUiDumpResult().getUiType() == MiitUITypeEnum.DISAGREE_PERMISSION.getValue()) {
                // 拒绝权限后应用推出 退出
                if (StringUtils.equals(resultDataLogBO.getRunStatus(), MiitRunStatusEnum.Death.getValue())) {
                    // 获取拒绝权限界面
                    addNoComplianceImage(commonDetectInfo, detectResult, resultDataLogBO);
                    return true;
                }
            }
        }
        return false;
    }

    private boolean findApplyPermissionText(TApplyPermission applyPermission, String text) {
        if (StringUtils.isBlank(text)) {
            return false;
        }
        return Pattern.compile(applyPermission.getRegex()).matcher(text).find();
    }

    private void checkPermission(CommonDetectInfo commonDetectInfo,
                                 List<ResultDataLogBO> commonUiList,
                                 List<PermissionPopupCheck> noInvolvedList,
                                 List<PermissionPopupCheck> noComplianceList) {
        for (int index = 0; index < commonUiList.size(); index++) {
            ResultDataLogBO resultDataLogBO = commonUiList.get(index);
            if (isPermissionPopup(commonDetectInfo.getTerminalTypeEnum(), resultDataLogBO)) {
                // 进行ocr。因为有可能权限获取说明文本在本页面，但是与弹窗不在同一层工具无法提取到界面元素，需要进行文字识别
                ocrImage(resultDataLogBO, commonDetectInfo);
                // 往前找是否有权限获取说明弹窗
                ResultDataLogBO beforeAuthorPopup = findBeforeAuthorPopup(commonUiList, index - 1);
                String beforeAuthorPopupText = beforeAuthorPopup == null ? "" : beforeAuthorPopup.getUiDumpResult().getPermissionText();
                TApplyPermission applyPermission = findApplyPermissionRegex(commonDetectInfo, resultDataLogBO.getUiDumpResult());
                if (findApplyPermissionText(applyPermission, beforeAuthorPopupText)
                        || findApplyPermissionText(applyPermission, resultDataLogBO.getUiDumpResult().getOcrText())) {
                    // 获取权限弹窗之前有权限获取说明的弹窗
                    PermissionPopupCheck check = new PermissionPopupCheck(applyPermission.getApplyName());
                    if (beforeAuthorPopup != null) {
                        check.addUi(beforeAuthorPopup);
                    }
                    check.addUi(resultDataLogBO);
                    noInvolvedList.add(check);
                } else if (hasIosPermissionDesc(commonDetectInfo, resultDataLogBO)) {
                    // ios的权限获取说明可能会写在获取权限的弹窗中
                    PermissionPopupCheck check = new PermissionPopupCheck(applyPermission.getApplyName());;
                    check.addUi(resultDataLogBO);
                    noInvolvedList.add(check);
                } else if (canAddNoCompliance(noComplianceList, applyPermission)) {
                    noComplianceList.removeIf(check -> check.actionName.equals(applyPermission.getApplyName()));
                    PermissionPopupCheck check = new PermissionPopupCheck(applyPermission.getApplyName());
                    // 不合规的弹窗
                    if (beforeAuthorPopup != null) {
                        check.addUi(beforeAuthorPopup);
                    }
                    check.addUi(resultDataLogBO);
                    noComplianceList.add(check);
                }
            }
        }
    }

    private boolean hasIosPermissionDesc(CommonDetectInfo commonDetectInfo, ResultDataLogBO resultDataLogBO) {
        if (commonDetectInfo.getTerminalTypeEnum() != TerminalTypeEnum.IOS || Objects.isNull(resultDataLogBO.getUiDumpResult())) {
            return false;
        }
        List<UIComponent> uiList = resultDataLogBO.getUiDumpResult().getUiComponentList();
        Pattern pattern = getIosPermissionTextPattern();
        // 是否有描述性的中文
        Pattern descPattern = Pattern.compile("[\\u4e00-\\u9fa5]{5,}");
        for (int i=0; i<uiList.size(); i++) {
            UIComponent component = uiList.get(i);
            String requestPermissionText = StringUtils.isEmpty(component.getText()) ? component.getContentDesc() : component.getText();
            // 请求权限文本的下方是否有一行描述文字
            if (StringUtils.isNotBlank(requestPermissionText) && pattern.matcher(requestPermissionText).find() && i + 1 < uiList.size()) {
                UIComponent next = uiList.get(i + 1);
                String nextText = StringUtils.isEmpty(next.getText()) ? next.getContentDesc() : next.getText();
                return StringUtils.isNotBlank(nextText) && descPattern.matcher(nextText).find();
            }
        }
        return false;
    }

    private boolean canAddNoCompliance(List<PermissionPopupCheck> noComplianceList, TApplyPermission applyPermission) {
        return noComplianceList.stream().noneMatch(c -> {
            // 相同权限名字的数据已经存在，且数据是完整的，因为有些可能前面阶段没有截到权限说明弹窗，后面的阶段又有了
            return c.getActionName().equals(applyPermission.getApplyName()) && c.uiList.size() > 1;
        });
    }

    protected CheckSdkNameResult checkAllBehaviorStageSdkName(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        // 找出所有行为
        List<ActionAnalyse> actionAnalyseList = new ArrayList<>();
        actionAnalyseList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GRANT));
        actionAnalyseList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_FRONT));
        actionAnalyseList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_GROUND));
        actionAnalyseList.addAll(filterAndCountInvolvedActionByStage(commonDetectInfo, customDetectInfo, BehaviorStageEnum.BEHAVIOR_EXIT));
        return checkSdkName(commonDetectInfo, actionAnalyseList);
    }

    protected CheckSdkNameResult checkSdkName(CommonDetectInfo commonDetectInfo, List<ActionAnalyse> actionAnalyseList) {
        CheckSdkNameResult checkSdkNameResult = new CheckSdkNameResult();
        Set<String> noMatchSdkNameList = new HashSet<>();
        List<ActionAnalyse> collectList = new ArrayList<>();
        Set<String> privacySdkNameList = new HashSet<>();
        Set<String> checkListSdkNameList = new HashSet<>();
        Set<String> checkListRowList = new HashSet<>();
        actionAnalyseList.stream()
                .filter(ActionAnalyse::getPersonal)
                .filter(a -> ExecutorTypeEnum.SDK.getValue().equals(a.getExecutorType()))
                .forEach(a -> {
                    String[] names = a.getExecutor().split(",");
                    for (String name : names) {
                        Optional<CheckList.Row> sdkRow = findCheckListBySdkName(commonDetectInfo, name);
                        if (sdkRow.isPresent()) {
                            checkListRowList.add(sdkRow.get().privacyPolicyFragment());
                            continue;
                        }
                        Optional<String> sdkKeyword = findKeywordInCheckListText(commonDetectInfo, name);
                        if (sdkKeyword.isPresent()) {
                            checkListSdkNameList.add(sdkKeyword.get());
                            continue;
                        }
                        Optional<String> policyKeyword = findKeywordInPolicyContent(commonDetectInfo, name);
                        if (policyKeyword.isPresent()) {
                            privacySdkNameList.add(policyKeyword.get());
                        } else {
                            noMatchSdkNameList.add(name);
                            collectList.add(a);
                        }
                    }
                });
        StringBuilder privacyPolicyFragment = new StringBuilder();
        // 截取隐私片段
        if (!checkListRowList.isEmpty()) {
            privacyPolicyFragment.append("\n");
            privacyPolicyFragment.append(StringUtils.join(checkListRowList, "\n"));
        }
        if (!checkListSdkNameList.isEmpty()) {
            privacyPolicyFragment.append("\n");
            privacyPolicyFragment.append(MiitWordKit.defaultKeywordExtractionFromContent(commonDetectInfo.getThirdPartySharingChecklist().getFullText(), checkListSdkNameList));
        }
        if (!privacySdkNameList.isEmpty()) {
            privacyPolicyFragment.append("\n");
            privacyPolicyFragment.append(MiitWordKit.defaultKeywordExtractionFromContent(commonDetectInfo.getPrivacyPolicyContent(), privacySdkNameList));
        }
        if (!noMatchSdkNameList.isEmpty()) {
            checkSdkNameResult.getNoMatchSdkNameList().addAll(noMatchSdkNameList);
            checkSdkNameResult.setNonInvolved(false);
        } else {
            checkSdkNameResult.setNonInvolved(true);
        }
        checkSdkNameResult.setPrivacyPolicyFragment(privacyPolicyFragment.toString());
        checkSdkNameResult.setAnalysisResult(collectList);
        return checkSdkNameResult;
    }

    protected void setDetectResultByConclusion(CommonDetectInfo commonDetectInfo, DetectResult detectResult, String conclusion) {
        setDetectResultByConclusion(commonDetectInfo, detectResult, Collections.singletonList(conclusion), Collections.emptyList());
    }

    protected void setDetectResultByConclusion(CommonDetectInfo commonDetectInfo, DetectResult detectResult, List<String> conclusionList, List<String> suggestionList) {
        if (conclusionList.isEmpty()) {
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        } else {
            detectResult.setConclusion(buildSequenceText(conclusionList));
            if (suggestionList.isEmpty()) {
                suggestionList.add(getDefaultSuggestion(commonDetectInfo));
            }
            detectResult.setSuggestion(buildSequenceText(suggestionList));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        }
    }

    static class PermissionPopupCheck {

        private String actionName;
        private List<ResultDataLogBO> uiList = new ArrayList<>();

        public PermissionPopupCheck(String actionName) {
            this.actionName = actionName;
        }

        public void addUi(ResultDataLogBO ui) {
            uiList.add(ui);
        }

        public String getActionName() {
            return actionName;
        }

        public List<ResultDataLogBO> getUiList() {
            return uiList;
        }
    }

    @Data
    public static class CheckNonePrivacyActionResult {

        private Set<String> privacyPolicyFragmentKeyword = new HashSet<>();
        private Set<String> checkListRowInfo = new HashSet<>();
        private Set<String> checkListTextKeyword = new HashSet<>();
        private List<String> conclusionList = new ArrayList<>();
        private List<String> suggestionList = new ArrayList<>();
        private List<ActionAnalyse> analysisResult = new ArrayList<>();
        private List<ResultDataLogBO> imageLogsList = new ArrayList<>();
        private boolean isNonInvolved;

    }

    @Data
    public static class CheckPermissionPopupResult {

        private List<String> permissionNameList = new ArrayList<>();
        private List<ActionAnalyse> analysisResult = new ArrayList<>();
        private List<ResultDataLogBO> imageLogsList = new ArrayList<>();
        private boolean isNonInvolved;

    }

    @Data
    public static class CheckSdkNameResult {
        private String privacyPolicyFragment;
        private List<String> noMatchSdkNameList = new ArrayList<>();
        private List<ActionAnalyse> analysisResult = new ArrayList<>();
        private boolean isNonInvolved;

    }
}
