package cn.ijiami.detection.miit.point.android.law164;

import cn.ijiami.detection.helper.PermissionNameHelper;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.domain.ResultDataLogBO;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.enums.MiitRunStatusEnum;
import cn.ijiami.detection.miit.enums.MiitUITypeEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

/**
 * APP首次启动时，向用户索取电话、通讯录、定位、短信、录音、相机、存储、日历等权限，用户拒绝授权后，应用退出或关闭（应用陷入弹窗循环，无法正常使用）。
 * <p>
 * <blockquote><pre>
 * 截图
 * 检测结果会返回判断标识和截图，判断启动应用后，拒绝所有权限授权弹窗，应用是否退出，如果应用退出，则合规，否则违规
 * </pre></blockquote>
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint20101 extends AbstractDetectPoint {


    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        
        // 遍历界面
        for (int index = 0; index < commonDetectInfo.getResultDataLogs().size(); index++) {
            ResultDataLogBO resultDataLogBO = commonDetectInfo.getResultDataLogs().get(index);
            if (resultDataLogBO.getUiDumpResult() != null &&
                    resultDataLogBO.getUiDumpResult().getUiType() == MiitUITypeEnum.DISAGREE_PERMISSION.getValue()) {
                if (index + 1 < commonDetectInfo.getResultDataLogs().size()) {
                    ResultDataLogBO refuseResult = commonDetectInfo.getResultDataLogs().get(index + 1);
                    // 拒绝权限后应用推出 退出不合规
                    if (StringUtils.equals(refuseResult.getRunStatus(), MiitRunStatusEnum.Death.getValue())) {
                        addNoComplianceImage(commonDetectInfo, detectResult, resultDataLogBO);
                    }
                }
            }
        }

        // 不涉及 添加图片
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_INVOLVED) {
            savePermissionImage(commonDetectInfo, detectResult);
        }
        
        //判断是否只包含存储权限，如果是只是存在存储就不违规
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE && detectResult.getRequestPermissionNames()!= null &&
        		detectResult.getRequestPermissionNames().size()==1 && detectResult.getRequestPermissionNames().contains(PermissionNameHelper.FILTERWORD)) {
        	detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        }
        
        if(detectResult.getRequestPermissionNames()!=null && detectResult.getRequestPermissionNames().size()>0 && detectResult.getRequestPermissionNames().contains(PermissionNameHelper.FILTERWORD)) {
        	Set<String> set = detectResult.getRequestPermissionNames();
        	set.remove(PermissionNameHelper.FILTERWORD);
        	detectResult.setRequestPermissionNames(set);
        }
        
        return detectResult;
    }

}
