package cn.ijiami.detection.miit.point.android.law41391;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.law191.DetectPoint130801;

import java.util.HashSet;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint350703.java
 * @Description
 * 其他要求
 * 3. App是否存在欺骗误导用户提供业务功能无关的个人信息或权限
 * 判断规则：
 * e)不应通过积分、奖励、优惠、红包等方式，欺骗误导用户提供与App业务功能无关的个人信息或权限；
 * 发现风险：
 * App是否存在欺骗误导用户提供业务功能无关的个人信息或权限
 */
@EnableDetectPoint
public class DetectPoint350703 extends DetectPoint130801 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.buildNonInvolved(commonDetectInfo, customDetectInfo);
        checkRedEnvelopeUi(commonDetectInfo, detectResult);
        if (detectResult.getComplianceStatus() == MiitDetectStatusEnum.NON_COMPLIANCE) {
            detectResult.setConclusion(buildSequenceTextFormat(
                    "%s是否存在欺骗误导用户提供业务功能无关的个人信息或权限", executor(commonDetectInfo)));
            detectResult.setSuggestion(buildSequenceTextFormat(
                    "建议%s向用户明示需要运行应用自启动或关联启动行为的场景、目的、规则及必要性。且在用户明示同意后才依法产生相关行为", executor(commonDetectInfo)));
        }
        return detectResult;
    }

}
