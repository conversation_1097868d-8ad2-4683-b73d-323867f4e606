package cn.ijiami.detection.miit.domain;

/**
 * UI控件组件
 */
public class UIComponent {

    // 控件文本
    private String text;

    // ios控件label
    private String label;

    // 控件类型
    private String className;

    // 包名
    private String packageName;

    // 能否选中(复选框)
    private boolean checked;

    // 能否点击
    private boolean clickable;

    // 是否选中
    private boolean selected;

    // 是否包含reourceId
    private boolean hasResourceId;

    // resourceId
    private String resourceId;

    // 隐私政策文本
    private String contentDesc;

    // xmlTag
    private String xmlTag;

    private int x1;

    private int y1;

    private int x2;

    private int y2;
    // 元素是否可见
    private boolean visible;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public boolean isChecked() {
        return checked;
    }

    public void setChecked(boolean checked) {
        this.checked = checked;
    }

    public boolean isClickable() {
        return clickable;
    }

    public void setClickable(boolean clickable) {
        this.clickable = clickable;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getContentDesc() {
        return contentDesc;
    }

    public void setContentDesc(String contentDesc) {
        this.contentDesc = contentDesc;
    }

    public boolean isHasResourceId() {
        return hasResourceId;
    }

    public void setHasResourceId(boolean hasResourceId) {
        this.hasResourceId = hasResourceId;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public String getXmlTag() {
        return xmlTag;
    }

    public void setXmlTag(String xmlTag) {
        this.xmlTag = xmlTag;
    }

    public int getX1() {
        return x1;
    }

    public void setX1(int x1) {
        this.x1 = x1;
    }

    public int getY1() {
        return y1;
    }

    public void setY1(int y1) {
        this.y1 = y1;
    }

    public int getX2() {
        return x2;
    }

    public void setX2(int x2) {
        this.x2 = x2;
    }

    public int getY2() {
        return y2;
    }

    public void setY2(int y2) {
        this.y2 = y2;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public boolean isVisible() {
        return visible;
    }

    public void setVisible(boolean visible) {
        this.visible = visible;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }
}
