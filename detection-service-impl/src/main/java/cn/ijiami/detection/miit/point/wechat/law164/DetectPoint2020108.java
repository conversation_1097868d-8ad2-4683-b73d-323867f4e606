package cn.ijiami.detection.miit.point.wechat.law164;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint20108;

/**
 * APP 未见提供相关业务功能或服务，不应申请通讯录、定位、短信、录音、相机、日历等权限。
 * 检测结论：APP运行时未曾触发【行为权限】弹窗，在AndroidManifest 声明或者代码中调用，属于过度申请权限。
 * 整改建议：
 * 建议根据APP实际情况删减不必要的权限，相关业务功能有使用【权限】等权限需在隐私政策中清晰明示APP收集的目的、方式、范围。
 * 注意:自动化测试过程中未曾触发以上权限弹窗进行权限申请，如APP无需申请以上权限，请不要在AndroidManifest 声明或者代码中调用，建议删除涉及到的代码。
 **/
@EnableDetectPoint
public class DetectPoint2020108 extends DetectPoint20108 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }

}
