package cn.ijiami.detection.miit.point.harmony.law35273;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.point.wechat.base.AppletDetectPoint250301;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectPoint200501.java
 * @Description
 * 35273检测
 * 5.3.a
 * 不应通过捆绑产品或服务各项业务功能的方式，要求个人信息主体一次性接受并授权同意各项业务功能收集个人信息的请求；
 *
 * 判断规则
 * 发现风险
 * targetSDKversion小于23
 * 应用启动后，连续弹出权限授权窗口
 * 拒绝权限后，应用退出
 */
@EnableDetectPoint
public class DetectPoint40250301 extends AppletDetectPoint250301 {

}
