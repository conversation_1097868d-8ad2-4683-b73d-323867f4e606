package cn.ijiami.detection.miit.point.ios.law164;

import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.ActionAnalyse;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.kit.MiitActionKit;
import cn.ijiami.detection.miit.point.android.AbstractDetectPoint;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10206;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * APP未见向用户告知且未经用户同意，在静默状态下或在后台运行时，存在按照一定频次收集位置信息、IMEI、通讯录、短信、图片等信息的行为，非服务所必需且无合理应用场景，超出与收集个人信息时所声称的目的具有直接或合理关联的范围。
 * <p>
 * <blockquote><pre>
 * 1、有隐私政策
 * 2、同意隐私政策前，检测位置信息、IMEI、通讯录、短信、图片等信息的行为是否超过每秒1次的频率
 * 3、将APP退至后台，检测位置信息、IMEI、通讯录、短信、图片等信息的行为是否超过每秒1次的频率
 *
 * 1、无隐私政策
 * 2、将APP退至后台，检测位置信息、IMEI、通讯录、短信、图片等信息的行为是否超过每秒1次的频率
 *
 * 分两种情况
 * 1、无隐私政策
 * 2、共XXX次（位置信息、IMEI、通讯录、短信、图片行为）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「后台行为」
 *
 * 1、隐私政策截图
 * 3、共XXX次（位置信息、IMEI、通讯录、短信、图片行为）
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 触发时间 主体类型 主体名称 个人信息行为名称 频次；
 * 「授权前行为」「后台行为」
 * 涉及行为：110025, 110024, 110027, 110026, 110035, 110036, 110047, 100001, 110051, 100003, 100002, 100005, 100004,
 * 100007, 100006, 100010, 100013, 100014, 100017, 100016, 110005, 100021, 110004, 110007, 110006, 110008
 *
 *
 * </pre></blockquote>
 *
 *
 * 场景一：修改
 * 1、没有有隐私政策
 * 2、某一类行为存在固定频率触发（只要存在3次以上即存在风险，时间间隔中间存在其他行为或者其他次数，都不管）
 * 例如：固定3秒触发获取mac地址行为
 * 【精准到秒即可】
 * 【授权前行为】【后台行为】
 *
 * 场景二：修改
 * 1、有隐私政策
 * 2、有关键词
 * 2、某一类行为存在固定频率触发（只要存在3次以上即存在风险，时间间隔中间存在其他行为或者其他次数，都不管）
 * 例如：固定3秒触发获取mac地址行为
 * 【精准到秒即可】
 * 【授权前行为】
 *
 * 场景三：修改
 * 1、有隐私政策
 * 2、没有关键词
 * 2、某一类行为存在固定频率触发（只要存在3次以上即存在风险，时间间隔中间存在其他行为或者其他次数，都不管）
 * 例如：固定3秒触发获取mac地址行为
 * 【精准到秒即可】
 * 【授权前行为】【后台行为】
 *
 *
 * <AUTHOR>
 * @date 2020/12/22 17:42
 **/
@EnableDetectPoint
public class DetectPoint1010206 extends DetectPoint10206 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }
}
