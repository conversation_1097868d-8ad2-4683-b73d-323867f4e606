package cn.ijiami.detection.miit.point.ios.law164;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.point.android.law164.DetectPoint10108;

/**
 * App在征求用户同意环节，设置为默认勾选。
 * <blockquote><pre>
 * 截图
 *
 * 检测结果会返回判断标识和截图，根据判断标识做判断
 * 判断checkbox=ture
 * </pre></blockquote>
 */
@EnableDetectPoint
public class DetectPoint1010108 extends DetectPoint10108 {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        return super.doDetect(commonDetectInfo, customDetectInfo);
    }
}
