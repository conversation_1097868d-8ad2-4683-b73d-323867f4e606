package cn.ijiami.detection.miit.point.android.law191;

import cn.ijiami.detection.miit.annotion.EnableDetectPoint;
import cn.ijiami.detection.miit.domain.CommonDetectInfo;
import cn.ijiami.detection.miit.domain.CustomDetectInfo;
import cn.ijiami.detection.miit.domain.DetectResult;
import cn.ijiami.detection.miit.enums.MiitDetectStatusEnum;
import cn.ijiami.detection.miit.point.android.PrivacyUiDetectPoint;
import com.github.houbb.opencc4j.util.ZhConverterUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 隐私政策等收集使用规则难以阅读，如文字过小过密、颜色过淡、模糊不清，或未提供简体中文版等。
 *
 * 未发现风险：
 * 1、a.隐私政策文字最小值大于设定值
 * b.隐私政策行距最小值大于设定大小
 * c.文本为简体
 * 发现风险：
 * 1、a.隐私政策文字最小值小于或等于设定值
 * b.隐私政策行距最小值小于或等于设定大小
 * c.文本不为简体
 */
@EnableDetectPoint
public class DetectPoint110401 extends PrivacyUiDetectPoint {

    @Override
    public DetectResult doDetect(CommonDetectInfo commonDetectInfo, CustomDetectInfo customDetectInfo) {
        DetectResult detectResult = this.getBaseDetectResult(commonDetectInfo, customDetectInfo);
        // 不存在隐私政策, 不合规
        if (commonDetectInfo.nonHasPrivacyPolicy()) {
            detectResult.setConclusion(buildSequenceText("未检测到隐私政策"));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
            return detectResult;
        }
        List<String> conclusionList = new ArrayList<>();
        // 隐私政策截图截图地址
        detectResult.setPrivacyScreenshot(commonDetectInfo.getPrivacyPolicyImg());
        int length = StringUtils.length(commonDetectInfo.getPrivacyPolicyContent());
        // 简体中文少于一半，不合规
        if (ZhConverterUtil.simpleList(commonDetectInfo.getPrivacyPolicyContent()).size() < length / 2) {
            conclusionList.add("检测到隐私政策，但隐私政策文本难以阅读，未提供简体中文版");
        }
        if (conclusionList.isEmpty()) {
            detectResult.setConclusion(buildSequenceText("该检测项未发现风险"));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_INVOLVED);
        } else {
            detectResult.setConclusion(buildSequenceText(conclusionList));
            detectResult.setSuggestion(buildSequenceText("请根据实际情况调整隐私政策文本中的文字大小、行距、间距及颜色"));
            detectResult.setComplianceStatus(MiitDetectStatusEnum.NON_COMPLIANCE);
        }
        return detectResult;
    }
}
