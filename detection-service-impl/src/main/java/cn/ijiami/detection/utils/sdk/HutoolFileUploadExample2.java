package cn.ijiami.detection.utils.sdk;


import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import cn.hutool.core.io.FileUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

public class HutoolFileUploadExample2 {

    public static void main(String[] args) throws IOException, InterruptedException {
        // 文件路径
        String filePath = "E:\\weibingtie\\全国SDK\\1804.txt";
        String shouye = FileUtil.readString(new File(filePath), "UTF-8");
        JSONObject objectList = JSONUtil.parseObj(shouye);
        JSONArray list = objectList.getJSONObject("data").getJSONArray("list");

        String[] title = {"companyName","sdkName","packageName", "typeName", "version","sdkPackageSize", "officialWeb", "privacyPolicyUrl","description"};
        //excel文件名
        String fileName = "SDK列表.xls";
        //sheet名
        String sheetName = "SDK列表";
        String content[][] = new String[list.size()][title.length];

        for (int i = 0; i < list.size(); i++) {
            JSONObject jsonObject = list.getJSONObject(i);
            String recordId = jsonObject.getStr("recordId");
            Integer msgType = jsonObject.getInt("msgType");
            try {
                JSONObject map = new JSONObject();
                map.set("recordId",recordId);
                map.set("type",msgType);
                String get = "https://sdk.caict.ac.cn/api/sdk/info/get";
                // 使用Hutool发送POST请求上传文件
                HttpRequest request = HttpRequest.post(get)
                        .header("Referer", "https://sdk.caict.ac.cn/official/")
                        .body(map.toString());
                // 发送请求并获取响应
                HttpResponse response = request.execute();
                JSONObject details = JSONUtil.parseObj(response.body());
                JSONObject data = details.getJSONObject("data");
                System.out.println(recordId+"===>"+i+"===>"+data.getStr("sdkName"));
                content[i][0] = data.getStr("companyName");
                content[i][1] = data.getStr("sdkName");
                content[i][2] = data.getStr("packageName");
                content[i][3] = data.getStr("typeName");
                content[i][4] = data.getStr("version");
                content[i][5] = data.getStr("sdkPackageSize");
                content[i][6] = data.getStr("officialWeb");
                content[i][7] = data.getStr("privacyPolicyUrl");
                content[i][8] = data.getStr("description");
                Thread.sleep(1000*3);
                HSSFWorkbook wb = ExcelUtil.getHSSFWorkbook(sheetName, title, content, null);
                wb.write(new FileOutputStream("E:\\weibingtie\\全国SDK\\" + fileName));
            }catch (Exception e){
                System.out.println("报错："+recordId);
            }

        }

    }
}
