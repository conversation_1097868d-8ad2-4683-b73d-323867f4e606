//package cn.ijiami.detection.utils;
//
//import java.util.Arrays;
//import java.util.List;
//import java.util.concurrent.TimeUnit;
//
//import org.apache.commons.lang3.StringUtils;
//import org.lionsoul.ip2region.xdb.Searcher;
//import org.springframework.stereotype.Component;
//
//import cn.ijiami.framework.core.config.IjiamiCommonProperties;
//
//@Component
//public class IP2RegionUtils {
//	
//	public Searcher searcher = null;
//	
//    public IP2RegionUtils() {
//		 IjiamiCommonProperties commonProperties = (IjiamiCommonProperties) AppContextHandel.getApplicationContext().getBean("ijiamiCommonProperties");
//		 String dbPath = commonProperties.getProperty("ijiami.tools.path")+"/ip2region.xdb";
//		 byte[] cBuff = null;
//        try {
//            cBuff = Searcher.loadContentFromFile(dbPath);
//        } catch (Exception e) {
//            System.out.printf("failed to load content from `%s`: %s\n", dbPath, e);
//        }
//        try {
//            searcher = Searcher.newWithBuffer(cBuff);
//        } catch (Exception e) {
//            System.out.printf("failed to create content cached searcher: %s\n", e);
//        }
//    }
//	
//	/**
//	 * 获取地址
//	 * @param ip
//	 * @return
//	 */
//	public String getAddress(String ip){
//		 try {
//			long sTime = System.nanoTime();
//			 String region = searcher.search(ip).trim();
//			 if(StringUtils.isBlank(region)) {
//				return ""; 
//			 }
//			 region = region.replaceAll("0", "").replaceAll("\\|", " ");
//			 long cost = TimeUnit.NANOSECONDS.toMicros((long) (System.nanoTime() - sTime));
//			 System.out.printf("{region: %s, ioCount: %d, took: %d μs}\n", region, searcher.getIOCount(), cost);
//			 return region;
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return null;
//	}
//	
//	public static void main(String[] args) {
//        String dbPath = "E:/tools/ip2region.xdb";
//        // 1、从 dbPath 加载整个 xdb 到内存。
//        byte[] cBuff;
//        try {
//            cBuff = Searcher.loadContentFromFile(dbPath);
//        } catch (Exception e) {
//            System.out.printf("failed to load content from `%s`: %s\n", dbPath, e);
//            return;
//        }
//
//        // 2、使用上述的 cBuff 创建一个完全基于内存的查询对象。
//        Searcher searcher;
//        try {
//            searcher = Searcher.newWithBuffer(cBuff);
//        } catch (Exception e) {
//            System.out.printf("failed to create content cached searcher: %s\n", e);
//            return;
//        }
//
//        // 3、查询
//    	try {
//			String t = "***********,**************,***********,**************,**************,***********,**************,***********,**************,**************,*******,*******";
//			List<String> list = Arrays.asList(t.split(","));
//			for (String string : list) {
//				 String ip = string;
//			     long sTime = System.nanoTime();
//			     String region = searcher.search(ip).trim();
//			     if(StringUtils.isBlank(region)) {
//			    	return; 
//			     }
//			     region = region.replaceAll("0", "").replaceAll("\\|", " ");
//			     long cost = TimeUnit.NANOSECONDS.toMicros((long) (System.nanoTime() - sTime));
////			     System.out.println(region);
//			     System.out.printf("{region: %s, ioCount: %d, took: %d μs}\n", region, searcher.getIOCount(), cost);
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//        // 备注：并发使用，用整个 xdb 数据缓存创建的查询对象可以安全的用于并发，也就是你可以把这个 searcher 对象做成全局对象去跨线程访问。
//    }
//}
