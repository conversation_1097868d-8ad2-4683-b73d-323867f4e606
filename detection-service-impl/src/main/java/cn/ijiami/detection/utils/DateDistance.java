package cn.ijiami.detection.utils;


import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.alibaba.fastjson.JSON;

import cn.ijiami.detection.VO.ActionFrequencyVO;

/**
 * 两个时间差计算
 */
public class DateDistance {
	
	
	private static final Logger LOG = LoggerFactory.getLogger(DateDistance.class);
	
    public static void main(String[] args) throws Exception {
    	
//    	String ss = "你好的是非得失${action},|ds鼎折覆餗防守打法";
//    	String s[] = ss.split("\\|");
//    	System.out.println(JSON.toJSONString(s));
//    	System.out.println(ss.replaceAll("\\$\\{action}", "123"));
    	
        List<String> list = new ArrayList<>();
        
  
//        list.add("2021-04-14 09:11:35");
//        list.add("2021-04-14 09:13:08");
//        list.add("2021-04-14 09:11:31");
//        list.add("2021-04-14 09:13:03");
//        list.add("2021-04-14 09:11:36");
//        list.add("2021-04-14 09:11:04");
//        list.add("2021-04-14 09:11:21");
//        list.add("2021-04-14 09:11:37");
//        list.add("2021-04-14 09:11:37");
        
//        list.add("2021-11-58 11:57:40");
        list.add("2021-04-15 19:46:13");
//        list.add("2021-11-58 11:58:20");
        list.add("2021-04-15 19:45:43");
        list.add("2021-04-15 19:46:43");
        list.add("2021-04-15 19:45:13");
        
        
        Collections.sort(list);
        for (String string : list) {
			System.out.println(string);
		}
        System.out.println(JSON.toJSONString(isExistCommonTime(list)));
        
        
//        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
//        System.out.println(df.parse("2021-04-14 09:11:37"));
//        System.out.println(df.parse("2021-04-14 09:11:37".replace(" ", " ")));
//        System.out.println(df.parse("2021-04-14 09:11:37".replaceAll("[[\\s-:punct:]]","")));
        
    }
    
    /**
     * 20220926-固定频率周期改为4次
     * @param timeList
     * @return
     */
    public static ActionFrequencyVO isExistCommonTime(List<String> timeList){
    	
    	if(timeList== null || timeList.size()<=2) {
    		return null;
    	}
    	
    	ActionFrequencyVO vo = new ActionFrequencyVO();
    	Collections.sort(timeList);
        List<Long> t = new ArrayList<>();
        for (int i = 0; i <=timeList.size(); i++) {
			if(i==(timeList.size()-1)){
				break;
			}
			t.add(getDistanceTime(timeList.get(i), timeList.get(i+1)));
		}
        int c = 0;
        Long comm = null;
        List<Integer> indexs = new ArrayList<>();
        List<String> indexsTime = new ArrayList<>();
        LOG.info("---->2固定频率时间排序：{}",t);
        for (int i = 0; i <t.size(); i++) {
        	if(i==0) {
        		comm = t.get(i);
        		continue;
        	}
        	if(t.get(i)==0){
        		comm = t.get(i);
        		continue;
        	}
        	if(t.get(i)==comm){
        		c++;
        		comm = t.get(i);
        		//记录下标
        		if(c==3) {
        			vo.setCount(c);
            		vo.setInterval(comm);
            		indexs.add(i-3);
        			indexs.add(i-2);
        			indexs.add(i-1);
        			indexs.add(i);
        			
        			indexsTime.add(timeList.get(i-3));
        			indexsTime.add(timeList.get(i-2));
        			indexsTime.add(timeList.get(i-1));
        			indexsTime.add(timeList.get(i));
        		}
        		continue;
        	}
        	if(c>=3) {
        		vo.setIndes(indexs);
                vo.setTiimes(indexsTime);
    			return vo;
    		}
        	comm = t.get(i);
        	c=0;
		}
        if(c==0) {
        	return null;
        }
        vo.setIndes(indexs);
        vo.setTiimes(indexsTime);
		return vo;
    }
        
//    public static ActionFrequencyVO isExistCommonTime(List<String> timeList){
//    	
//    	if(timeList== null || timeList.size()<=2) {
//    		return null;
//    	}
//    	
//    	ActionFrequencyVO vo = new ActionFrequencyVO();
//    	Collections.sort(timeList);
////    	System.out.println("---->1固定频率时间排序："+timeList);
//        List<Long> t = new ArrayList<>();
//        for (int i = 0; i <=timeList.size(); i++) {
//			if(i==(timeList.size()-1)){
//				break;
//			}
//			t.add(getDistanceTime(timeList.get(i), timeList.get(i+1)));
//		}
//        int c = 0;
//        Long comm = null;
//        LOG.info("---->2固定频率时间排序：{}",t);
//        for (int i = 0; i <t.size(); i++) {
//        	if(i==0) {
//        		comm = t.get(i);
//        		continue;
//        	}
//        	if(t.get(i)==0){
//        		comm = t.get(i);
//        		continue;
//        	}
//        	if(t.get(i)==comm){
//        		c++;
//        		comm = t.get(i);
//        		vo.setCount(c);
//        		vo.setInterval(comm);
//        		continue;
//        	}
//        	if(c>=1) {
//    			return vo;
//    		}
//        	comm = t.get(i);
//        	c=0;
//		}
//        if(c==0) {
//        	return null;
//        }
//		return vo;
//    }
    
    /** 
     * 两个时间之间相差距离多少天 
     * @param one 时间参数 1： 
     * @param two 时间参数 2： 
     * @return 相差天数 
     */ 
    public static long getDistanceDays(String starttime, String endtime) throws Exception{  
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd");  
        Date one;  
        Date two;  
        long days=0;  
        try {  
            one = df.parse(starttime);  
            two = df.parse(endtime);  
            long time1 = one.getTime();  
            long time2 = two.getTime();  
            long diff ;  
            if(time1<time2) {  
                diff = time2 - time1;  
            } else {  
                diff = time1 - time2;  
            }  
            days = diff / (1000 * 60 * 60 * 24);  
        } catch (ParseException e) {  
            e.getMessage();  
        }  
        return days;//返回相差多少天  
    }  
          
        
    /** 
     * 两个时间相差多少秒 
     * @param str1 时间参数 1 格式：1990-01-01 12:00:00 
     * @param str2 时间参数 2 格式：2009-01-01 12:00:00 
     */  
    public static long getDistanceTime(String starttime, String endtime) {  
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");  
        Date one;  
        Date two;  
        long sec = 0;  
        try {  
            one = df.parse(starttime.replace(" ", " "));  
            two = df.parse(endtime.replace(" ", " "));  
            long time1 = one.getTime();  
            long time2 = two.getTime();  
            long diff ;  
            if(time1<time2) {  
                diff = time2 - time1;  
            } else {  
                diff = time1 - time2;  
            }  
            sec = (diff/1000);
        } catch (ParseException e) {  
            e.getMessage();  
        }  
        return sec;  
    }  
}
