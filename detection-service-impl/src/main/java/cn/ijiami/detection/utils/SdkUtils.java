package cn.ijiami.detection.utils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.ijiami.detection.VO.CheckList;
import cn.ijiami.detection.entity.interfaces.Sdk;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;

import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.entity.TSdkLibraryPackage;
import lombok.Data;

public class SdkUtils {
	private static final Pattern NUMBER_VERSION_PATTERN = Pattern.compile("^[0-9]+(\\.[0-9]+)*$");

	public static boolean checkStackInfoIsConcatAppPackage(String appPackageName, List<String> stackInfoList){
		if(CollectionUtils.isEmpty(stackInfoList)) {
			return false;
		}
		for (String string : stackInfoList) {
			if(string.startsWith(appPackageName)){
				return true;
			}
			break;
		}
		
		return false;
	}

	public static String removeSdkSuffix(String sdkName) {
		// 检查输入是否为空或空字符串
		if (StringUtils.isEmpty(sdkName)) {
			return "";
		}
		// 定义后缀
		final String sdkSuffix = "SDK";
		// 移除末尾的 "SDK" 后缀（如果存在）
		if (StringUtils.endsWithIgnoreCase(sdkName, sdkSuffix) && sdkName.length() > sdkSuffix.length() + 1) {
			sdkName = sdkName.substring(0, sdkName.length() - sdkSuffix.length());
		}
		// 清理换行符、回车符并去除首尾空格
		return sdkName.replaceAll("[\r\n]", "").trim();
	}

	public static boolean actionKeyWordInSdk(String keyword, CheckList.Row sdkRow) {
		if (StringUtils.isNotBlank(sdkRow.getPermission()) && Pattern.compile(keyword).matcher(sdkRow.getPermission()).find()) {
			return true;
		}
		if (StringUtils.isNotBlank(sdkRow.getPurpose()) && Pattern.compile(keyword).matcher(sdkRow.getPurpose()).find()) {
			return true;
		}
		return false;
	}

    public static List<TargetSdkInfo> findTargetSdk(List<TSdkLibrary> sdkLibraries, List<String> stackInfoList) {
        if (CollectionUtils.isEmpty(stackInfoList)) {
            return Collections.emptyList();
        }

        Map<List<String>, TargetSdkInfo> sdkInfoMap = new HashMap<>();

        for (TSdkLibrary sdk : sdkLibraries) {
            if (CollectionUtils.isEmpty(sdk.getPackageList())) {
                continue;
            }

            List<String> packageNameList = findPackageNameList(sdk, stackInfoList);
            if (!packageNameList.isEmpty()) {
                TargetSdkInfo sdkInfo = new TargetSdkInfo();
                sdkInfo.setId(sdk.getId());
                sdkInfo.setName(sdk.getName());
                sdkInfo.setVersion(sdk.getVersion());
                sdkInfo.setCreatedTime(sdk.getCreatedTime());
                sdkInfo.setPackageName(packageNameList);

                // 是否有同包名的sdk已经添加
                TargetSdkInfo sameSdk = sdkInfoMap.get(packageNameList);
                if (sameSdk != null) {
                    // 当前sdk版本新，替换原来的同包名sdk
                    if (compareSdkVersion(sdkInfo, sameSdk)) {
                        sdkInfoMap.remove(packageNameList);
                        sdkInfoMap.put(packageNameList, sdkInfo);
                    }
                } else {
                    sdkInfoMap.put(packageNameList, sdkInfo);
                }
            }
        }

        return new ArrayList<>(sdkInfoMap.values());
    }

	/**
	 * 判断新sdk1是不是比sdk2版本高
	 *
	 * @param lib1
	 * @param lib2
	 * @return
	 */
	public static boolean compareSdkVersion(Sdk lib1, Sdk lib2) {
		// 优先版本号判断
		if (StringUtils.isNotBlank(lib1.getVersion())
				&& StringUtils.isNotBlank(lib2.getVersion())
				&& NUMBER_VERSION_PATTERN.matcher(lib1.getVersion()).matches()
				&& NUMBER_VERSION_PATTERN.matcher(lib2.getVersion()).matches()) {
			return CommonUtil.isGtVersion(lib1.getVersion(), lib2.getVersion());
		} else if (lib1.getCreatedTime() != null && lib2.getCreatedTime() != null) {
			// 无法版本号判断就根据时间判断
			return lib1.getCreatedTime().getTime() > lib2.getCreatedTime().getTime();
		} else {
			return lib1.getId() > lib2.getId();
		}
	}
    
    private static List<String> findPackageNameList(TSdkLibrary sdk, List<String> stackInfoList) {
        List<String> packageNameList = new ArrayList<>();
        for (TSdkLibraryPackage pkg : sdk.getPackageList()) {
			for (String stackInfo:stackInfoList) {
				if (stackInfo.startsWith(pkg.getPackageName())) {
					packageNameList.add(pkg.getPackageName());
				}
			}
        }
        return packageNameList;
    }

    @Data
    public static class TargetSdkInfo implements Sdk {
        private Long id;
        private String name;
		private String version;
		private Date createdTime;
        private List<String> packageName;
    }

    public static void main(String[] args) {
    	
    	List<TSdkLibraryPackage> packageList = new ArrayList<>();
    	TSdkLibraryPackage p = new TSdkLibraryPackage();
    	p.setPackageName("butterknife");
    	packageList.add(p);
    	
    	p = new TSdkLibraryPackage();
    	p.setPackageName("com.cmonbaby.butterknife");
    	packageList.add(p);
    	
    	p = new TSdkLibraryPackage();
    	p.setPackageName("butterknife.ButterKnife");
    	packageList.add(p);
    	
    	
    	List<TSdkLibrary> sdkLibraries = new ArrayList<>();
    	
    	TSdkLibrary t = new TSdkLibrary();
    	t.setId(1L);
    	t.setName("butterknife");
    	t.setPackageName("butterknife");
    	t.setPackageList(packageList);
    	
    	sdkLibraries.add(t);
    	
    	String txt = "android.app.ApplicationPackageManager.getPackageInfoAsUser(ApplicationPackageManager.java:171)<---android.app.ApplicationPackageManager.getPackageInfo(ApplicationPackageManager.java:149)<---com.iflytek.chinese.mandarin_simulation_test.utils.MyUtils.isInstallApp(MyUtils.java:1967)<---com.iflytek.chinese.mandarin_simulation_test.ui.AboutUsActivity.buttonClick(AboutUsActivity.java:84)<---com.iflytek.chinese.mandarin_simulation_test.ui.AboutUsActivity_ViewBinding$4.doClick(AboutUsActivity_ViewBinding.java:76)<---butterknife.internal.DebouncingOnClickListener.onClick(DebouncingOnClickListener.java:18)<---android.view.View.performClick(View.java:6294)<---android.view.View$PerformClick.run(View.java:24770)<---android.os.Handler.handleCallback(Handler.java:790)<---android.os.Handler.dispatchMessage(Handler.java:99)<---android.os.Looper.loop(Looper.java:164)<---android.app.ActivityThread.main(ActivityThread.java:6843)<---java.lang.reflect.Method.invoke(Native Method)<---com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:438)<---com.android.internal.os.ZygoteInit.main(ZygoteInit.java:807)";
    	List<TargetSdkInfo> list = findTargetSdk(sdkLibraries, Arrays.asList(txt.split("<---")));
    	System.out.println(JSON.toJSONString(list));
//    	
//    	System.out.println(checkStackInfoIsConcatAppPackage("com.iflytek.chinese.mandarin_simulation_test", txt));
//    	
//    	txt = "android.telephony.TelephonyManager.getSimOperatorNumericForPhone(TelephonyManager.java:2331)<---android.telephony.TelephonyManager.getSimOperatorNumeric(TelephonyManager.java:2318)<---android.telephony.TelephonyManager.getSimOperatorNumeric(TelephonyManager.java:2302)<---android.telephony.TelephonyManager.getSimOperator(TelephonyManager.java:2264)<---java.lang.reflect.Method.invoke(Native Method)<---com.mob.tools.utils.ReflectHelper.a(Unknown Source:468)<---com.mob.tools.utils.ReflectHelper.invokeInstanceMethod(Unknown Source:497)<---com.mob.tools.a.b.r(Unknown Source:476)<---com.mob.tools.a.j$38.a(Unknown Source:293)<---com.mob.tools.a.j$38.b(Unknown Source:290)<---com.mob.tools.a.j.a(Unknown Source:1508)<---com.mob.tools.a.j.c(Unknown Source:290)<---com.mob.tools.a.j.t(Unknown Source:285)<---com.mob.tools.b.a.b(Unknown Source:280)<---com.mob.tools.b.a.a(Unknown Source:207)<---com.mob.tools.a.g$60.a(Unknown Source:328)<---com.mob.tools.a.g$60.b(Unknown Source:325)<---com.mob.tools.a.g.a(Unknown Source:34)<---com.mob.tools.a.g.t(Unknown Source:325)<---com.mob.tools.utils.DH$RequestBuilder.a(Unknown Source:303)<---com.mob.tools.utils.DH$RequestBuilder.a(Unknown Source:259)<---com.mob.tools.utils.DH$RequestBuilder.a(Unknown Source:177)<---com.mob.tools.utils.DH$RequestBuilder$1.run(Unknown Source:207)<---com.mob.tools.utils.DH$RequestBuilder.request(Unknown Source:243)<---com.mob.commons.x.a(Unknown Source:131)<---com.mob.commons.x.f(Unknown Source:115)<---com.mob.commons.b.b(Unknown Source:785)<---com.mob.commons.b.a(Unknown Source:41)<---com.mob.commons.b$7.onResponse(Unknown Source:697)<---com.mob.tools.utils.DH$RequestBuilder$1.run(Unknown Source:223)<---com.mob.tools.utils.DH$RequestBuilder.request(Unknown Source:243)<---com.mob.commons.b.b(Unknown Source:692)<---com.mob.commons.b.c(Unknown Source:617)<---com.mob.commons.b.b(Unknown Source:285)<---com.mob.commons.b.a(Unknown Source:59)<---com.mob.commons.u$2$1.a(Unknown Source:158)<---com.mob.commons.m.a(Unknown Source:66)<---com.mob.commons.m.a(Unknown Source:29)<---com.mob.commons.u$2.a(Unknown Source:152)<---com.mob.tools.utils.f.run(Unknown Source:25)";
//    	System.out.println(checkStackInfoIsConcatAppPackage("com.iflytek.chinese.mandarin_simulation_test", txt));
	}
}
