package cn.ijiami.detection.utils;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class ReportSupportUitls {

    /**
     * 根据表达式获取检测点Id列表
     * @param expression
     * @return
     */
    public static List<Integer> splitExpression(String expression){
        //(a413 == 0 &&  a441 == 0 && dt==3)
        List<Integer> pointIds = new ArrayList<>();
        for (String exp : expression.split("&&")) {
            int startIndex = exp.indexOf('a');
            int endIndex = exp.indexOf("=");
            if(startIndex<0 || startIndex>endIndex){
                continue;
            }
            String substring = exp.substring(startIndex + 1, endIndex).trim();
            Integer pointId = Integer.parseInt(substring);
            pointIds.add(pointId);
        }
        return pointIds;
    }

}
