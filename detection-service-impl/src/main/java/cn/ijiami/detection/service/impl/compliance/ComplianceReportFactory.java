package cn.ijiami.detection.service.impl.compliance;

import cn.ijiami.detection.enums.TerminalTypeEnum;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

@Service
public class ComplianceReportFactory implements ApplicationContextAware {

    private static ApplicationContext applicationContext;
    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
    public static AbstractComplianceReport createReportBean(TerminalTypeEnum terminalTypeEnum){
        AbstractComplianceReport abstractDownloadReport = null;
        switch (terminalTypeEnum){
            case IOS:
                abstractDownloadReport = applicationContext.getBean("iosComplianceDownloadReport", AbstractComplianceReport.class);
                break;
            case ANDROID:
                abstractDownloadReport = applicationContext.getBean("androidComplianceDownloadReport", AbstractComplianceReport.class);
                break;
            case WECHAT_APPLET:
                abstractDownloadReport = applicationContext.getBean("wechatAppletComplianceDownloadReport", AbstractComplianceReport.class);
                break;
            case ALIPAY_APPLET:
                abstractDownloadReport = applicationContext.getBean("wechatAppletComplianceDownloadReport", AbstractComplianceReport.class);
                break;
            default:
                return null;
        }
        return abstractDownloadReport;
    }


}
