package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.VO.UserPermissionVO;
import cn.ijiami.detection.entity.TPermission;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TPermissionMapper;
import cn.ijiami.detection.service.api.IPermissionService;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 敏感权限接口实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional
@CacheConfig(cacheNames = {"privacy-detection:permission"})
public class PermissionServiceImpl implements IPermissionService {

    private final TPermissionMapper permissionMapper;

    public PermissionServiceImpl(TPermissionMapper permissionMapper) {
        this.permissionMapper = permissionMapper;
    }

    @Override
   // @Cacheable(key = "'findPermissionByPage:'+#p0.page+','+#p0.rows+','+#p0.name+','+#p0.grade+','+#p0.type")
    public PageInfo<UserPermissionVO> findPermissionByPage(TPermission permission) {
        if (permission.getPage() != null && permission.getRows() != null) {
            PageHelper.startPage(permission.getPage(), permission.getRows());
        }
        List<UserPermissionVO> permissionVOList = permissionMapper.selectPermissionByPage(permission);
        return new PageInfo<>(permissionVOList);
    }

    @Override
    @Cacheable(key = "'findAllPermission'")
    public List<TPermission> findAllPermission() {
        return permissionMapper.findByTerminalType(null, TerminalTypeEnum.ANDROID.getValue());
    }

    @Override
    @Cacheable(key = "'findPermissions'")
    public String findPermissions() {
        StringBuilder sb = new StringBuilder();
        ;
        for (TPermission permission : permissionMapper.findByTerminalType(null, TerminalTypeEnum.ANDROID.getValue())) {
            sb.append("@ijm");
            sb.append(permission.getName());
            sb.append("|");
            sb.append(permission.getRemark());
            sb.append("|");
            sb.append(permission.getHarm());
            sb.append("|");
            sb.append(permission.getGrade().getValue());
            sb.append("|");
            sb.append(permission.getGoogle());
            sb.append("|");
            sb.append(permission.getSystem());
            sb.append("|");
            sb.append(!permission.getSystem());
            sb.append("|");
            sb.append(permission.getType().getValue());
            sb.append("\n");
        }

        sb.append("@ijmsuccess");
        return sb.toString();
    }
}
