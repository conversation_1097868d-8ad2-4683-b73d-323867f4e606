package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.VO.ccrc.CCRCDTO;
import cn.ijiami.detection.entity.TCcrcTask;
import cn.ijiami.detection.mapper.TCcrcTaskMapper;
import cn.ijiami.detection.service.api.ICcrcTaskService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @date 2019/10/30 11:54
 */
@Service
public class ICcrcTaskServiceImpl implements ICcrcTaskService {

    private final TCcrcTaskMapper ccrcTaskMapper;

    public ICcrcTaskServiceImpl(TCcrcTaskMapper ccrcTaskMapper) {
        this.ccrcTaskMapper = ccrcTaskMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(CCRCDTO ccrcdto) {
        Example example = new Example(TCcrcTask.class);
        example.createCriteria().andEqualTo("apkId", ccrcdto.getApkId());
        TCcrcTask ccrcTask = ccrcTaskMapper.selectOneByExample(example);
        if (ccrcTask == null) {
            ccrcTask = new TCcrcTask();
            BeanUtils.copyProperties(ccrcdto, ccrcTask);
            ccrcTaskMapper.insertSelective(ccrcTask);
        } else {
            BeanUtils.copyProperties(ccrcdto, ccrcTask);
            ccrcTaskMapper.updateByExample(ccrcTask, example);
        }
    }
}
