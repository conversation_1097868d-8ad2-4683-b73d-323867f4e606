package cn.ijiami.detection.service.impl.compliance;

import static cn.ijiami.base.common.context.UserContextHolder.getCurrentUserId;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.googlecode.aviator.AviatorEvaluator;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.ijiami.detection.DTO.compliance.ComplianceAssessItemDto;
import cn.ijiami.detection.DTO.compliance.ComplianceAssessPointDto;
import cn.ijiami.detection.DTO.compliance.ComplianceItemCategoryDto;
import cn.ijiami.detection.DTO.compliance.ComplianceItemPointResultDto;
import cn.ijiami.detection.DTO.compliance.CompliancePermissionGroupDto;
import cn.ijiami.detection.DTO.compliance.CompliancePermissionItemDto;
import cn.ijiami.detection.DTO.compliance.ComplianceSdkCountDto;
import cn.ijiami.detection.DTO.compliance.CustomAssessPointResultDto;
import cn.ijiami.detection.VO.PermissionVO;
import cn.ijiami.detection.VO.compliance.ComplianceBaseVo;
import cn.ijiami.detection.config.IjiamiCommonProperties;
import cn.ijiami.detection.dao.TaskDAO;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.compliance.PermissionSceneEntity;
import cn.ijiami.detection.entity.compliance.TcomplianceAssessItem;
import cn.ijiami.detection.entity.compliance.TcomplianceAssessPoint;
import cn.ijiami.detection.entity.compliance.TcomplianceAssessPointResultEntity;
import cn.ijiami.detection.entity.compliance.TcomplianceAssessPointTemplateEntity;
import cn.ijiami.detection.entity.compliance.TcomplianceDetectAssessRel;
import cn.ijiami.detection.entity.compliance.TcomplianceItemPoint;
import cn.ijiami.detection.entity.compliance.TcomplianceItemPointResult;
import cn.ijiami.detection.entity.compliance.TcomplianceNode;
import cn.ijiami.detection.entity.compliance.TcompliancePersonalInfo;
import cn.ijiami.detection.entity.compliance.TcomplianceSdk;
import cn.ijiami.detection.enums.ComplianceSdkInfoEnum;
import cn.ijiami.detection.enums.ConditionTypeEnum;
import cn.ijiami.detection.enums.ShowPointColorEnum;
import cn.ijiami.detection.enums.TaskDetectionTypeEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TPermissionMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.mapper.compliance.ComplianceAssessItemMapper;
import cn.ijiami.detection.mapper.compliance.ComplianceAssessPointMapper;
import cn.ijiami.detection.mapper.compliance.ComplianceAssessPointResultMapper;
import cn.ijiami.detection.mapper.compliance.ComplianceAssessPointTemplateMapper;
import cn.ijiami.detection.mapper.compliance.ComplianceDetectAssessRelMapper;
import cn.ijiami.detection.mapper.compliance.ComplianceItemPointMapper;
import cn.ijiami.detection.mapper.compliance.ComplianceItemPointResultMapper;
import cn.ijiami.detection.mapper.compliance.CompliancePersonalInfoMapper;
import cn.ijiami.detection.mapper.compliance.ComplianceSdkMapper;
import cn.ijiami.detection.mapper.compliance.PermissionSceneMapper;
import cn.ijiami.detection.query.compliance.TaskChangeQuery;
import cn.ijiami.detection.service.api.IAssetsService;
import cn.ijiami.detection.service.api.IDynamicTaskContextService;
import cn.ijiami.detection.service.api.IPrivacyActionNougatService;
import cn.ijiami.detection.service.api.compliance.IcomplianceInstanceService;
import cn.ijiami.detection.service.compliance.IcomplianceNodeService;
import cn.ijiami.detection.utils.ReportSupportUitls;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

@Slf4j
@Service
public class ComplianceInstanceServiceImpl implements IcomplianceInstanceService {
    @Autowired
    private TTaskMapper taskMapper;
    @Autowired
    private IDynamicTaskContextService dynamicTaskContextService;
    @Autowired
    private TaskDAO taskDao;
    @Resource
    private ComplianceItemPointResultMapper complianceItemPointResultMapper;
    @Autowired
    private CompliancePersonalInfoMapper compliancePersonalInfoMapper;
    @Autowired
    private PermissionSceneMapper permissionSceneMapper;
    @Autowired
    private ComplianceAssessPointResultMapper complianceAssessPointResultMapper;
    @Autowired
    private IAssetsService assetsService;
    @Autowired
    private TPermissionMapper permissionMapper;
    @Autowired
    private IPrivacyActionNougatService iPrivacyActionNougatService;
    @Autowired
    private IcomplianceNodeService complianceNodeService;
    @Autowired
    private ComplianceAutoDetectorManager complianceAutoDetectorManager;
    @Autowired
    private ComplianceAssessItemMapper complianceAssessItemMapper;
    @Autowired
    private ComplianceAssessPointMapper complianceAssessPointMapper;
    @Autowired
    private ComplianceItemPointMapper complianceItemPointMapper;
    @Autowired
    private ComplianceDetectAssessRelMapper complianceDetectAssessRelMapper;
    @Autowired
    private ComplianceSdkMapper complianceSdkMapper;
    @Autowired
    private IjiamiCommonProperties ijiamiCommonProperties;
    @Autowired
    private ComplianceAssessPointTemplateMapper complianceAssessPointTemplateMapper;

    /**
     * 收集使用的个人信息类型
     * 实际触发使用的权限是否超出隐私政策所描述范围
     * 隐私政策中权限描述是否超出实际触发使用的权限
     */
    private final String OTHER_SPECIAL_ID = "104,404,101,401,102,402";

    @Override
    public void changeStatusAndCopyParentTask(TaskChangeQuery query){
        // 校验任务是否存在
        TTask tTask = taskMapper.selectByPrimaryKey(query.getTaskId());
        if (Objects.isNull(tTask)) {
            return;
        }
        Optional<TaskChangeQuery> taskOptional = Optional.ofNullable(query);
        // 获取任务状态
        Integer status = taskOptional.map(TaskChangeQuery::getStatus).orElse(0);

        //启动
        if(status == 1){
            dynamicTaskContextService.removeTaskContext(tTask);
            taskDao.updateExportDetectionWaiting(tTask,query.getDeviceSerial(),tTask.getDynamicDeviceType());
            //这里做子任务数据迁移
            if(0 != tTask.getParentTaskId()){
                subtaskDataMigration(tTask.getParentTaskId(),tTask.getTaskId());
            }
        }
        //中断
        if(status == 2){
            taskDao.stopExportDetection(tTask);
        }
        //暂存
        if(status == 3){
            taskDao.updateExportPausuStatus(tTask.getTaskId());
        }
    }

    /**
     * 通过查询父任务的检测数据复制到子任务中
     * @param parentTaskId
     * @param taskId
     */
    private void subtaskDataMigration(long parentTaskId,long taskId){
        //迁移满足的检测结果
        Example example2 = new Example(TcomplianceItemPointResult.class);
        example2.createCriteria().andEqualTo("instanceId",parentTaskId).andEqualTo("detectPointResult",true).andNotEqualTo("itemCode","otherItem");
        List<TcomplianceItemPointResult> results = complianceItemPointResultMapper.selectByExample(example2);
        List<String> special = Arrays.asList("collectSdkInfoPoint","personalOutBusinessPoint","privacyPolicyAuthorityIfBeyondScope","collectPersonalTypePoint","collectPersonalBehaviorPoint");
        for (TcomplianceItemPointResult result : results) {
            result.setId(null);
            result.setTaskId(taskId);
            //特殊项要黄色
            if (special.contains(result.getPointCode())) {
                result.setColor(ShowPointColorEnum.YELLOW);
            } else {
                result.setColor(ShowPointColorEnum.GREEN);
            }
            //特殊项处理
            if("collectPersonalBehaviorPoint".equals(result.getPointCode())){
                JSONObject json = JSONObject.parseObject(result.getProperties());
                json.remove("actionNougatIds");
                result.setProperties(json.toJSONString());
            }
        }
        if(CollectionUtil.isNotEmpty(results)){
            complianceItemPointResultMapper.insertList(results);
        }
        //迁移个人信息场景
        Example example3 = new Example(TcompliancePersonalInfo.class);
        example3.createCriteria().andEqualTo("task_id",parentTaskId);
        List<TcompliancePersonalInfo> tCompliancePersonalInfos = compliancePersonalInfoMapper.selectByExample(example3);
        for (TcompliancePersonalInfo item : tCompliancePersonalInfos) {
            item.setId(null);
            item.setTaskId(taskId);
            item.setIsDeclare(false);
            item.setIsMark(false);
        }
        if(CollectionUtil.isNotEmpty(tCompliancePersonalInfos)){
            compliancePersonalInfoMapper.insertList(tCompliancePersonalInfos);
        }
        //迁移权限场景
        Example example4 = new Example(PermissionSceneEntity.class);
        example4.createCriteria().andEqualTo("task_id",parentTaskId);
        List<PermissionSceneEntity> permissionSceneEntities = permissionSceneMapper.selectByExample(example4);
        for (PermissionSceneEntity item : permissionSceneEntities) {
            item.setResultId(null);
            item.setTaskId(taskId);
            item.setIsDeclare(false);
            permissionSceneMapper.insertSelective(item);
        }
        //迁移自定义检测项
        Example example5 = new Example(TcomplianceAssessPointResultEntity.class);
        example5.createCriteria().andEqualTo("taskId",parentTaskId);
        List<TcomplianceAssessPointResultEntity> tcomplianceAssessPointResultEntityList = complianceAssessPointResultMapper.selectByExample(example5);
        for (TcomplianceAssessPointResultEntity item : tcomplianceAssessPointResultEntityList) {
            item.setId(null);
            item.setTaskId(taskId);
            if(item.getIsSatisfy()){
                item.setColor(ShowPointColorEnum.GREEN);
            }else{
                item.setColor(ShowPointColorEnum.RED);
            }
        }
        if(CollectionUtil.isNotEmpty(tcomplianceAssessPointResultEntityList)){
            complianceAssessPointResultMapper.insertList(tcomplianceAssessPointResultEntityList);
        }
    }

    /**
     * 查询权限场景
     * @param complianceBaseVo
     * @return
     */
    @Override
    public List<CompliancePermissionGroupDto> queryPermission(ComplianceBaseVo complianceBaseVo) {
        Long taskId = complianceBaseVo.getTaskId();
        TerminalTypeEnum terminalTypeEnum = complianceBaseVo.getTerminalType();

        List<CompliancePermissionGroupDto> compliancePermissionGroupDTOS = new ArrayList<>();
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        String permissionStr = assetsService.getAssetsById(task.getAssetsId()).getPermissions();
        //当前资产配置声明的权限列表
        List<String> permissionList = new ArrayList<>();
        if (StringUtils.isNotBlank(permissionStr)) {
            permissionList = JSON.parseArray(permissionStr, String.class);
        }
        List<PermissionVO> permissionVos = new ArrayList<>();
        if (permissionList != null && permissionList.size() > 0) {
            permissionVos = permissionMapper.findPermissionByPermissNames(permissionList,terminalTypeEnum.getValue());//权限组对应得权限列表
        }
        //拿行为数据(已触发)的所有权限和权限数据进行对比
        List<String> permissionNames = iPrivacyActionNougatService.queryActionPermissionByTaskId(taskId);
        Map<String, CompliancePermissionGroupDto> map = new HashMap<>();
        for (PermissionVO permissionVo : permissionVos) {
            String groupName = permissionVo.getGroupName();
            String permissionName = permissionVo.getName();
            if (!map.containsKey(groupName)) {
                CompliancePermissionGroupDto groupDto = new CompliancePermissionGroupDto();
                groupDto.setCompliancePermissionItemDtos(new ArrayList<>());
                groupDto.setHasTrigger(false);
                groupDto.setPermissionGroupCode(permissionVo.getGroupCode());
                groupDto.setPermissionGroupName(permissionVo.getGroupName());
                map.put(groupName, groupDto);
            }
            CompliancePermissionGroupDto groupDto = map.get(groupName);
            boolean hasTrigger = false;
            //说明该权限在行为表中触发
            if (permissionNames.contains(permissionName)) {
                hasTrigger = true;
                groupDto.setHasTrigger(true);
            }
            groupDto.getCompliancePermissionItemDtos().add(createPermissionItemDto(permissionVo, hasTrigger));
        }
        compliancePermissionGroupDTOS.addAll(map.values());
        return compliancePermissionGroupDTOS;
    }

    /**
     * 构建权限数据
     * @param permissionVO
     * @param flag
     * @return
     */
    private CompliancePermissionItemDto createPermissionItemDto(PermissionVO permissionVO, Boolean flag) {
        CompliancePermissionItemDto compliancePermissionItem = new CompliancePermissionItemDto();
        compliancePermissionItem.setPermissionName(permissionVO.getName());
        compliancePermissionItem.setPermissionCode(permissionVO.getPermissionCode());
        compliancePermissionItem.setLogogramName(permissionVO.getAliasName() + "-" + permissionVO.getRemark());
        compliancePermissionItem.setHasTrigger(flag);
        return compliancePermissionItem;
    }

    /**
     * 下一步判断或者完成任务
     * @param complianceBaseVo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ComplianceItemCategoryDto> nextStep(ComplianceBaseVo complianceBaseVo) {
        Long taskId = complianceBaseVo.getTaskId();
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        String currentNodeCode = task.getCurrentNodeCode();
        if(TaskDetectionTypeEnum.DEPTH_SPECIAL.getValue()==task.getDetectionType()){
            currentNodeCode = complianceBaseVo.getNodeCode();
        }
        TcomplianceNode nextNode = complianceNodeService.getNextNode(currentNodeCode);

        TTask updateTask = new TTask();
        //有下一步更新节点信息
        if (null != nextNode) {
            //更新下阶段信息
            updateTask.setTaskId(task.getTaskId());
            updateTask.setCurrentNodeCode(nextNode.getNodeCode());
            taskMapper.updateByPrimaryKeySelective(updateTask);
            complianceBaseVo.setNodeCode(nextNode.getNodeCode());
            return complianceNodeService.getNodeInfo(complianceBaseVo);
        }
        // 没有下一步完成任务 设置完成状态
        taskDao.updateExportTaskSuccess(task.getTaskId(),currentNodeCode);

        //插入需要后台判断的检测点
        List<TcomplianceItemPointResult> autoDetectionResult = complianceAutoDetectorManager.getAutoDetectionResult(taskId,task.getTerminalType().getValue());
        if(CollectionUtil.isNotEmpty(autoDetectionResult)){
            //先清空旧数据
            Example example = new Example(TcomplianceItemPointResult.class);
            example.createCriteria().andEqualTo("taskId",taskId)
                    .andEqualTo("categoryCode","otherCategory").andEqualTo("itemCode","otherItem");

            complianceItemPointResultMapper.deleteByExample(example);
            try {
                complianceItemPointResultMapper.insertList(autoDetectionResult);
            }catch (Exception e){}

        }
        return null;
    }

    /**
     * 获取超限权限
     * @param complianceBaseVo
     * @return
     */
    @Override
    public List<PermissionSceneEntity> queryPermissionGroup(ComplianceBaseVo complianceBaseVo) {
        //任务id
        Long taskId = complianceBaseVo.getTaskId();
        if(taskId == null){
            return Collections.emptyList();
        }
        //取出权限组检测结果
        Example example = new Example(PermissionSceneEntity.class);
        example.selectProperties("resultId","itemCode","permissonGroupName","properties","isDeclare");
        example.createCriteria().andEqualTo("taskId",taskId);
        List<PermissionSceneEntity> list = permissionSceneMapper.selectByExample(example);
        //封装数据返回
        for (PermissionSceneEntity item : list) {
            JSONObject jsonObject1 = JSON.parseObject(item.getProperties());
            JSONObject jsonObject2 = new JSONObject();
            jsonObject2.put("screenImg",jsonObject1.getJSONArray("screenImg"));
            item.setProperties(null);
            item.setAttribute(jsonObject2);
        }
        Long parentTaskId = complianceBaseVo.getParentTaskId();
        //覆盖父任务
        if(parentTaskId != null && parentTaskId > 0){
            Example example2 = new Example(PermissionSceneEntity.class);
            example2.selectProperties("resultId","itemCode","isDeclare");
            example2.createCriteria().andEqualTo("taskId",parentTaskId);
            Map<String, PermissionSceneEntity> map = permissionSceneMapper.selectByExample(example2).stream().collect(Collectors.toMap(PermissionSceneEntity::getItemCode, Function.identity()));
            for (PermissionSceneEntity item : list) {
                if(!map.containsKey(item.getItemCode())){
                    continue;
                }
                item.setIsDeclare(map.get(item.getItemCode()).getIsDeclare());
            }
        }
        return list;
    }

    /**
     * 报告预览
     * @param complianceBaseVo
     * @return
     * @throws Exception
     */
    @Override
    public List<ComplianceAssessItemDto> reportPreview(ComplianceBaseVo complianceBaseVo) throws Exception {
        List<ComplianceAssessItemDto> complianceAssessItemDtos;
        Long taskId = complianceBaseVo.getTaskId();
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            throw new Exception("任务不存在");
        }

        Integer condition = complianceBaseVo.getCondition();
        Integer terminalType = complianceBaseVo.getTerminalType().getValue();
        //父任务检测id
        Long parentTaskId = complianceBaseVo.getParentTaskId();
        if(condition == null || condition == 0 || condition == ConditionTypeEnum.ALL_DETECTION.getValue()){
            complianceAssessItemDtos = getAllCondition(taskId,terminalType,TaskDetectionTypeEnum.getItem(task.getDetectionType()));//获取全部检测项
        }else if(condition == ConditionTypeEnum.NOT_FILL_DETECTION.getValue()){
            complianceAssessItemDtos = getNotFillCondition(taskId,terminalType,TaskDetectionTypeEnum.getItem(task.getDetectionType()));//获取本次违规检测项
        }else if(condition == ConditionTypeEnum.LAST_NOT_DETECTION.getValue()){
            complianceAssessItemDtos = getLastNotFillCondition(taskId,terminalType,parentTaskId,TaskDetectionTypeEnum.getItem(task.getDetectionType()));//获取上次违规检测项(展示本次结果)
        }else{
            complianceAssessItemDtos = getNewNotFillCondition(taskId,terminalType,parentTaskId,TaskDetectionTypeEnum.getItem(task.getDetectionType()));//获取本次新增违规项
        }
        return complianceAssessItemDtos;
    }

    /**
     * 报告预览展示所有结果
     * @param taskId
     * @param terminalType
     * @return
     * @throws Exception
     */
    private List<ComplianceAssessItemDto> getAllCondition(Long taskId,Integer terminalType,TaskDetectionTypeEnum detectionTypeEnum) throws Exception {
        //查询所有评估项数据
        List<TcomplianceAssessItem> tComplianceAssessItems = complianceAssessItemMapper.findAllByTerminaType();
        //查询所有评估点
        List<TcomplianceAssessPoint> tComplianceAssessPoints = complianceAssessPointMapper.findAllByTerminalType(terminalType);
        //根据评估项id分组，获取到每个评估项下面所有的评估点
        Map<Long, List<TcomplianceAssessPoint>> pointMap = tComplianceAssessPoints.stream().sorted(Comparator.comparingInt(TcomplianceAssessPoint::getSort)).collect(Collectors.groupingBy(TcomplianceAssessPoint::getAssessItemId));

        //获取所有检测项
        List<TcomplianceItemPoint> tComplianceItemPoints = complianceItemPointMapper.findAllByTerminalType(terminalType);
        //根据检测项id分组
        Map<Long, TcomplianceItemPoint> itemPointMap = tComplianceItemPoints.stream().collect(Collectors.toMap(TcomplianceItemPoint::getId, Function.identity()));

        //获取所有评估点下面配置的检测项数据（中间表）
        List<TcomplianceDetectAssessRel> tComplianceDetectAssessRels = complianceDetectAssessRelMapper.findAllByTerminalType(terminalType);

        //根据评估点id进行分组，获取每个评估点下面的所有检测项数据
        Map<Long, List<TcomplianceDetectAssessRel>> pointRelMap = tComplianceDetectAssessRels.stream().collect(Collectors.groupingBy(TcomplianceDetectAssessRel::getAssessPointId));
        //获取当前实例的检测项结果
        List<ComplianceItemPointResultDto> tComplianceItemPointResultDtos = complianceItemPointResultMapper.findtItemPointResultByTaskId(taskId, terminalType);
        //根据检测点id进行分组，获取每个检测项的结果集合
        Map<Long, List<ComplianceItemPointResultDto>> resultDtoMap = tComplianceItemPointResultDtos.stream()
                .collect(Collectors.groupingBy(ComplianceItemPointResultDto::getPointId));

        //组装评估项集合做展示
        ArrayList<ComplianceAssessItemDto> complianceAssessItemDtos = new ArrayList<>();
        //循环遍历每个评估项
        for (TcomplianceAssessItem tComplianceAssessItem : tComplianceAssessItems) {
            //拿出评估项id
            Long itemId = tComplianceAssessItem.getId();
            //拿出评估项分类id
            Long assessCategoryId = tComplianceAssessItem.getAssessCategoryId();
            //创建单个评估项对象
            ComplianceAssessItemDto complianceAssessItemDto = new ComplianceAssessItemDto();
            //设置评估项基础值
            complianceAssessItemDto.setName(tComplianceAssessItem.getName()).setAssessCategoryId(assessCategoryId).setIsSatisfy(true).setSort(tComplianceAssessItem.getSort());
            //组装每个评估点集合
            ArrayList<ComplianceAssessPointDto> complianceAssessPointDtos = new ArrayList<>();
            //防止异常，判断是否存在该评估项
            if (pointMap.containsKey(itemId)) {
                //通过评估项id，获取评估点集合
                List<TcomplianceAssessPoint> complianceAssessPoints = pointMap.get(itemId);
                //循环遍历评估点
                for (TcomplianceAssessPoint point : complianceAssessPoints) {
                    //拿到当前评估点
                    Long pointId = point.getId();
                    //拿到评估点配置的属性
                    String pointProperties = point.getProperties();
                    //拿到评估点配置的是否用表达式判断
                    Integer judgmentStatus = point.getJudgmentStatus();//表达式判断状态
                    //定义一个评估点对象
                    ComplianceAssessPointDto complianceAssessPointDto = new ComplianceAssessPointDto();
                    //组装检测项集合
                    ArrayList<TcomplianceItemPoint> complianceItemPoints = new ArrayList<>();
                    //设置当前评估点基础信息
                    complianceAssessPointDto.setName(point.getName()).setAssessStandard(point.getAssessStandard()).setIsSatisfy(true)
                            .setTestMainPoint(point.getTestMainPoint()).setTestWay(point.getTestWay());
                    //判断当前评估点是否有配置与检测项的关系
                    if (pointRelMap.containsKey(pointId)) {
                        //通过评估点获取检测项列表
                        List<TcomplianceDetectAssessRel> detectAssessRels = pointRelMap.get(pointId);
                        //合规结论
                        Set<String> suggestOfTrueSet = new HashSet<>();
                        //不合规结论
                        Set<String> suggestOfFalseSet = new HashSet<>();
                        //默认合规结论
                        Set<String> suggestOfTrueSetDefault = new HashSet<>();
                        //默认不合规结论
                        Set<String> suggestOfFalseSetDefault = new HashSet<>();
                        //用于存储单条检测点的整改建议
                        Set<String> rectifySet = new HashSet<>();
                        //专项检测没有结果需要跳过
                        if (TaskDetectionTypeEnum.DEPTH_SPECIAL == detectionTypeEnum && !checkHasSpecialPointResult(detectAssessRels, resultDtoMap)) {
                            continue;
                        }

                        //校验评估点结果是否满足
                        Boolean pointResult;
                        //开启表达式评估
                        if (1 == judgmentStatus) {
                            pointResult = checkPointResult(pointProperties, detectAssessRels, resultDtoMap, detectionTypeEnum);
                        } else {
                            pointResult = checkPointResultByDefault(detectAssessRels, resultDtoMap);
                        }

                        //组装结论
                        buildAssessResult(detectAssessRels, resultDtoMap, pointProperties, pointResult, judgmentStatus, taskId, suggestOfTrueSet, suggestOfFalseSet, itemPointMap, suggestOfTrueSetDefault, suggestOfFalseSetDefault, detectionTypeEnum);
                        //组装建议
                        buildSuggest(detectAssessRels, itemPointMap, resultDtoMap, pointResult, rectifySet, complianceAssessItemDto, complianceAssessPointDto, complianceItemPoints);
                        //整合数据
                        buildData(point, pointResult, complianceAssessPointDto, suggestOfFalseSetDefault, suggestOfFalseSet, suggestOfTrueSet, suggestOfTrueSetDefault, rectifySet);
                    } else {
                        complianceAssessPointDto.setAssessResult("");
                        complianceAssessPointDto.setRectifySuggest("");
                        complianceAssessPointDto.setIsSatisfy(null);
                    }
                    //每个评估点添加检测项集合数据
                    complianceAssessPointDto.setComplianceItemPoints(complianceItemPoints);
                    //评估点集合数据整合
                    complianceAssessPointDtos.add(complianceAssessPointDto);
                }
            }
            //增加自定义检测点
            if (50 == itemId) {
                Example example1 = new Example(TcomplianceAssessPointResultEntity.class);
                example1.createCriteria().andEqualTo("taskId", taskId);
                List<TcomplianceAssessPointResultEntity> tComplianceAssessPointResultEntities = complianceAssessPointResultMapper.selectByExample(example1);
                for (TcomplianceAssessPointResultEntity assessPointResultEntity : tComplianceAssessPointResultEntities) {
                    //定义评估点对象
                    ComplianceAssessPointDto complianceAssessPointDto = new ComplianceAssessPointDto();
                    //给评估点塞值
                    complianceAssessPointDto.setName(assessPointResultEntity.getName())
                            .setAssessStandard(assessPointResultEntity.getAssessStandard())
                            .setIsSatisfy(assessPointResultEntity.getIsSatisfy())
                            .setTestMainPoint(assessPointResultEntity.getTestMainPoint())
                            .setTestWay(assessPointResultEntity.getTestWay());
                    //不满足需要展示建议，满足不展示
                    if (!assessPointResultEntity.getIsSatisfy()) {
                        complianceAssessPointDto.setRectifySuggest(assessPointResultEntity.getRectifySuggest());
                        //评估项设置为不满足
                        complianceAssessItemDto.setIsSatisfy(false);
                        complianceAssessPointDto.setAssessResultOfFalse(assessPointResultEntity.getConclusion());
                    } else {
                        complianceAssessPointDto.setRectifySuggest("");
                        complianceAssessPointDto.setAssessResult(assessPointResultEntity.getConclusion());
                    }
                    //组装检测项集合
                    ArrayList<TcomplianceItemPoint> complianceItemPoints = new ArrayList<>();
                    //构建虚拟检测项对象
                    TcomplianceItemPoint tComplianceItemPoint = new TcomplianceItemPoint();
                    //构建虚拟检测项结果
                    ComplianceItemPointResultDto resultDto = new ComplianceItemPointResultDto();
                    //结果数据
                    resultDto.setId(assessPointResultEntity.getId())
                            .setPointId(assessPointResultEntity.getId())
                            .setTaskId(taskId)
                            .setTaskId(null)
                            .setDetectPointResult(assessPointResultEntity.getIsSatisfy());
                    JSONObject jsonObject = new JSONObject();
                    //截图
                    jsonObject.put("screenImg", getImages(assessPointResultEntity.getScreenImg()));
                    //结论
                    jsonObject.put("suggestText", assessPointResultEntity.getConclusion());
                    jsonObject.put("result", assessPointResultEntity.getIsSatisfy() ? 1 : 2);
                    resultDto.setProperties(jsonObject.toJSONString());
                    //检测项插入虚拟值
                    tComplianceItemPoint.setResultDto(resultDto);
                    tComplianceItemPoint.setId(assessPointResultEntity.getId());
                    tComplianceItemPoint.setPointName(assessPointResultEntity.getName());
                    tComplianceItemPoint.setIsShow(true);
                    tComplianceItemPoint.setDefaultConclusion(assessPointResultEntity.getConclusion());
                    tComplianceItemPoint.setTerminalType(terminalType);
                    JSONObject json = new JSONObject();
                    //设置前段展示满足，还是是否，1满足，2是否
                    json.put("expression", 1);
                    tComplianceItemPoint.setProperties(json.toJSONString());
                    //检测项数据集合
                    complianceItemPoints.add(tComplianceItemPoint);
                    //评估点插入检测项集合
                    complianceAssessPointDto.setComplianceItemPoints(complianceItemPoints);
                    //评估点集合
                    complianceAssessPointDtos.add(complianceAssessPointDto);
                }
                //自定义为空则跳过
                if (tComplianceAssessPointResultEntities.size() == 0) {
                    continue;
                }
            }

            //每个评估项添加评估点集合数据
            complianceAssessItemDto.setComplianceAssessPointDtoList(complianceAssessPointDtos);
            //评估项数据整合
            complianceAssessItemDtos.add(complianceAssessItemDto);
        }
        return complianceAssessItemDtos.stream().sorted(Comparator.comparingInt(ComplianceAssessItemDto::getSort)).collect(Collectors.toList());
    }

    /**
     * 检查专项检测是否有结果
     */
    private Boolean checkHasSpecialPointResult(List<TcomplianceDetectAssessRel> detectAssessRels, Map<Long, List<ComplianceItemPointResultDto>> resultDtoMap){
        if(detectAssessRels==null){
            return false;
        }
        for(TcomplianceDetectAssessRel tComplianceDetectAssessRel : detectAssessRels){
            //检测项id
            Long itemPointId = tComplianceDetectAssessRel.getDetectPoinitId();
            if(resultDtoMap.containsKey(itemPointId)){
                return true;
            }
        }
        return false;
    }

    /**
     *
     * @param point
     * @param pointResult
     * @param complianceAssessPointDto
     * @param suggestOfFalseSetDefault
     * @param suggestOfFalseSet
     * @param suggestOfTrueSet
     * @param suggestOfTrueSetDefault
     * @param rectifySet
     */
    private void buildData(TcomplianceAssessPoint point,Boolean pointResult,ComplianceAssessPointDto complianceAssessPointDto,
                           Set<String> suggestOfFalseSetDefault,Set<String> suggestOfFalseSet,Set<String> suggestOfTrueSet,
                           Set<String> suggestOfTrueSetDefault,Set<String> rectifySet){
        //最终合规结论
        StringBuilder suggestBuilder = new StringBuilder();
        //最终不合规结论
        StringBuilder suggestOfFalseBuilder = new StringBuilder();
        //最终整改建议
        StringBuilder rectifyBuilder = new StringBuilder();
        //默认整改建议
        String rectifySuggestDefault = point.getRectifySuggest();
        //结论：不管满足不满足，有自定义的则展示自定义的，没有则展示默认的（个别检测点默认的结论也分满足和不满足结论
        //整改建议：检测点满足，则不展示整改建议，即使有自定义整改建议也不展示。检测点不满足，有自定义的整改建议，则自定义整改建议放前面，默认整改建议放后面，没有自定义整改建议则只展示默认整改建议。
        //获取单个检测项的结论
        StringBuilder builder = new StringBuilder();//临时变量
        builder = getRemovingSuggest(suggestOfFalseSetDefault);
        suggestOfFalseBuilder = getRemovingSuggest(suggestOfFalseSet);//不满足结论
        if(!pointResult && (suggestOfFalseBuilder == null || "".equals(suggestOfFalseBuilder.toString())) ){//如果结论为空就展示默认结论
            suggestOfFalseBuilder.append(builder);
        }

        suggestBuilder = getRemovingSuggest(suggestOfTrueSet);//满足结论
        builder = getRemovingSuggest(suggestOfTrueSetDefault);
        if(suggestBuilder ==null || "".equals(suggestBuilder.toString()) ) {//如果结论为空就展示默认结论
            suggestBuilder = builder;
        }

        complianceAssessPointDto.setAssessResultOfFalse(StringUtils.isNotBlank(suggestOfFalseBuilder.toString())?suggestOfFalseBuilder.toString():"");
        complianceAssessPointDto.setAssessResult(StringUtils.isNotBlank(suggestBuilder.toString())?suggestBuilder.toString():"");


        //获取单个检测项是否有人工写入建议，只有不满足才展示建议,人工建议放前面，默认建议放在后面，用换行符隔开
        rectifyBuilder = getRemovingSuggest(rectifySet);
        if(complianceAssessPointDto.getIsSatisfy() == true){
            complianceAssessPointDto.setRectifySuggest("");
        }else{
            if(StringUtils.isNotEmpty(rectifyBuilder) && !rectifySuggestDefault.equals(rectifyBuilder.toString())){
                rectifyBuilder.append("\r\n").append(rectifySuggestDefault);
            }
            complianceAssessPointDto.setRectifySuggest(StringUtils.isNotBlank(rectifyBuilder)?rectifyBuilder.toString():point.getRectifySuggest());
        }
    }

    /**
     * 通过特殊方法拼装评估结论
     * @param sets 收集的评估结果集合
     */
    private StringBuilder getRemovingSuggest(Set<String> sets){
        StringBuilder builder = new StringBuilder();
        //通过set集合去重后转为list然后拼装结论
        List<String> setToList = new ArrayList<>(sets);
        for(int n =0;n<setToList.size();n++){
            //只有一条的情况,不需要序号
            if(setToList.size()>1){
                builder.append(n+1).append("）").append(setToList.get(n));
            }else{
                builder.append(setToList.get(n));
            }
        }
        return builder;
    }

    /**
     * 根据表达式校验评估点结果
     * @param pointRule 评估点规则
     * @param detectAssessRels 检测点列表
     * @param resultDtoMap 检测点结果
     * @return true:满足  false:不满足
     */
    private Boolean checkPointResult(String pointRule,List<TcomplianceDetectAssessRel> detectAssessRels,
                                     Map<Long, List<ComplianceItemPointResultDto>> resultDtoMap,TaskDetectionTypeEnum detectionTypeEnum){

        if(StringUtils.isBlank(pointRule)){
            return true;
        }
        Map<String, Object> env = new HashMap<>();
        if(TaskDetectionTypeEnum.DEPTH_SPECIAL == detectionTypeEnum){
            env.put("dt",detectionTypeEnum.getValue());
        }
        for(TcomplianceDetectAssessRel tComplianceDetectAssessRel : detectAssessRels){
            //检测项id
            Long itemPointId = tComplianceDetectAssessRel.getDetectPoinitId();
            //如果没有检测结果默认跳过
            if(!resultDtoMap.containsKey(itemPointId)){
                continue;
            }

            ComplianceItemPointResultDto complianceItemPointResultDTO = resultDtoMap.get(itemPointId).get(0);
            String properties = complianceItemPointResultDTO.getProperties();
            JSONObject parseObject = JSON.parseObject(properties);
            Integer result = (Integer) parseObject.get("result");
            //1是满足或者是  2不满足
            if(result != null && result == 1){
                env.put("a" + itemPointId, 1);//默认为满足
            }else {
                env.put("a" + itemPointId, 0);//默认为不满足
            }
        }
        return (Boolean) AviatorEvaluator.execute(pointRule, env);
    }

    /**
     * 用普通的关联检测项得出结论(有一个不满足则违规)
     */
    private Boolean checkPointResultByDefault(List<TcomplianceDetectAssessRel> detectAssessRels,
                                              Map<Long, List<ComplianceItemPointResultDto>> resultDtoMap){
        for(TcomplianceDetectAssessRel tComplianceDetectAssessRel : detectAssessRels){
            //检测项id
            Long itemPointId = tComplianceDetectAssessRel.getDetectPoinitId();

            if(!resultDtoMap.containsKey(itemPointId)){
                continue;
            }
            ComplianceItemPointResultDto complianceItemPointResultDTO = resultDtoMap.get(itemPointId).get(0);
            String properties = complianceItemPointResultDTO.getProperties();
            JSONObject parseObject = JSON.parseObject(properties);
            Integer result = parseObject.getIntValue("result");
            //1是满足  2不满足
            if(1 != result){
                return false;//有一个不满足就是违规
            }
        }
        return true;//返回默认结论合规
    }

    /**
     * 构建评估结果
     * 1.只要是不合规,则把对应的检测点  合规展示成满足   /  不合规展示成不满足
     * 2.如果是合规(依次往下匹配所有检测点)  找到所有合规的检测点展示到满足结论里面
     * 3.如果是合规(依次往下找到第一个满足的条件的表达式  将对应的检测点展示?)
     *
     */
    private void buildAssessResult(List<TcomplianceDetectAssessRel> detectAssessRels,Map<Long, List<ComplianceItemPointResultDto>> resultDtoMap,
                                   String ruleString,Boolean pointResult,Integer judgmentStatus,Long taskId,
                                   Set<String> suggestOfTrueSet,Set<String> suggestOfFalseSet,
                                   Map<Long, TcomplianceItemPoint> itemPointMap,Set<String> suggestOfTrueSetDefault,Set<String> suggestOfFalseSetDefault,TaskDetectionTypeEnum detectionTypeEnum){
        //表达式合规 -> 依次往下找到第一个满足的条件的表达式  将对应的检测点展示
        if(pointResult != null && pointResult && judgmentStatus == 1){
            for (String rule : ruleString.split("\\|\\|")) {
                if(!rule.contains("==")){
                    continue;
                }
                //(a413 == 0 &&  a441 == 0)
                Boolean result = checkPointResult(rule, detectAssessRels, resultDtoMap,detectionTypeEnum);
                if(result){
                    for (long pointId : ReportSupportUitls.splitExpression(rule)) {
                        if(!resultDtoMap.containsKey(pointId)){
                            continue;
                        }
                        for (ComplianceItemPointResultDto resultDto : resultDtoMap.get(pointId)) {
                            String properties = resultDto.getProperties();
                            JSONObject parseObject = JSON.parseObject(properties);
                            String suggestText = parseObject.getString("suggestText");//评估结果
                            if(StringUtils.isBlank(suggestText)){
                                //构建默认结论
                                if(itemPointMap.containsKey(pointId)){
                                    TcomplianceItemPoint tComplianceItemPoint = itemPointMap.get(pointId);
                                    if(!suggestOfFalseSetDefault.contains(tComplianceItemPoint.getDefaultConclusion())){
                                        suggestOfTrueSetDefault.add(tComplianceItemPoint.getDefaultConclusion());
                                    }
                                }
                            }else{
                                //如果结论是满足的只显示满足
                                suggestOfTrueSet.add(suggestText);
                            }

                        }
                    }
                    break;
                }
            }
        }else {
            //评估点和检测点得对应关系
            for(TcomplianceDetectAssessRel detectAssessRel : detectAssessRels) {
                Long detectPoinitId = detectAssessRel.getDetectPoinitId();//检测点ID
                //如果检测时SDK信息则需要做特殊处理后直接返回
                if(Arrays.asList(106,406).contains(detectPoinitId.intValue())){
                    getProcessSdkStatement(taskId,suggestOfTrueSet,suggestOfFalseSet);
                    continue;
                }
                if (!resultDtoMap.containsKey(detectPoinitId)) {
                    continue;
                }
                //遍历检测点结果
                for (ComplianceItemPointResultDto resultDto : resultDtoMap.get(detectPoinitId)) {
                    Boolean detectPointResult = resultDto.getDetectPointResult();//检测点是否合规
                    String properties = resultDto.getProperties();
                    JSONObject parseObject = JSON.parseObject(properties);
                    String suggestText = (String) parseObject.get("suggestText");//评估结果
                    if(StringUtils.isBlank(suggestText)) {
                        //构建默认结论
                        if (itemPointMap.containsKey(detectPoinitId)) {
                            TcomplianceItemPoint tComplianceItemPoint = itemPointMap.get(detectPoinitId);
                            //获取默认结论
                            getConformance(detectPointResult,suggestText,detectPoinitId,tComplianceItemPoint.getDefaultConclusion(),suggestOfTrueSetDefault,suggestOfFalseSetDefault);
                        }
                    }else{
                        //如果结论是满足的只显示满足
                        if(pointResult != null && pointResult){
                            suggestOfTrueSet.add(suggestText);
                        }else{
                            //不满足的需要同时显示满足和不满足
                            if(detectPointResult){
                                suggestOfTrueSet.add(suggestText);
                            }else{
                                suggestOfFalseSet.add(suggestText);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * SDK声明检测点特殊处理
     * @param taskId
     * @param suggestOfTrueSet 满足项
     * @param suggestOfFalseSet 不满足项
     * @return
     */
    private void getProcessSdkStatement(Long taskId,Set<String> suggestOfTrueSet,Set<String> suggestOfFalseSet){
        List<ComplianceSdkCountDto> complianceSdkCountDtos = complianceSdkMapper.selectSdkstatistics(taskId);
        Example example = new Example(TcomplianceSdk.class);
        example.createCriteria().andEqualTo("taskId",taskId);
        int totalCount = complianceSdkMapper.selectCountByExample(example);//实例对应得sdk数量
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(complianceSdkCountDtos) && totalCount != 0){
            for(ComplianceSdkCountDto complianceSdkCountDto : complianceSdkCountDtos){
                Integer count = complianceSdkCountDto.getCount();
                Integer type = complianceSdkCountDto.getType();
                StringBuilder satisfyBuilder = new StringBuilder();
                StringBuilder notSatisfyBuilder = new StringBuilder();
                if(count.equals(totalCount)){
                    satisfyBuilder.append("已").append(ComplianceSdkInfoEnum.getName(type).itemValue());
                    suggestOfTrueSet.add(satisfyBuilder.toString());
                }else {
                    notSatisfyBuilder.append("未").append(ComplianceSdkInfoEnum.getName(type).itemValue());
                    suggestOfFalseSet.add(notSatisfyBuilder.toString());
                }
                if(type == 1 && count > 0){
                    totalCount = count;
                }
            }
        }
    }

    /**
     * 拼装满足与不满足结论
     * @param detectPointResult 每个检测项对应的结果
     * @param suggestText  每个检测项默认的结论
     * @param detectPoinitId 检测点
     */
    private void getConformance (Boolean detectPointResult,String suggestText, Long detectPoinitId,
                                 String suggestDefault,Set<String> suggestOfTrueSetDefault,Set<String> suggestOfFalseSetDefault){
        //不合规
        if(!detectPointResult || ((detectPoinitId == 413|| detectPoinitId == 113) && detectPointResult)){//413检测项为“是”是不合规的
            //默认结论
            if(!suggestOfFalseSetDefault.contains(suggestDefault) && !suggestDefault.equals(suggestText) &&
                    (detectPoinitId.intValue() != 106 && detectPoinitId.intValue() != 406)){
                suggestOfFalseSetDefault.add(suggestDefault);
            }
        }else{//合规
            //默认结论
            if(!suggestOfTrueSetDefault.contains(suggestDefault) && !suggestDefault.equals(suggestText) &&
                    (detectPoinitId.intValue() != 106 && detectPoinitId.intValue() != 406) ){
                suggestOfTrueSetDefault.add(suggestDefault);
            }
        }
    }

    /**
     * 组装建议
     * @param detectAssessRels
     * @param itemPointMap
     * @param resultDtoMap
     * @param pointResult
     * @param rectifySet
     * @param complianceAssessItemDto
     * @param complianceAssessPointDto
     * @param complianceItemPoints
     */
    private void buildSuggest(List<TcomplianceDetectAssessRel> detectAssessRels,Map<Long, TcomplianceItemPoint> itemPointMap,
                              Map<Long, List<ComplianceItemPointResultDto>> resultDtoMap,Boolean pointResult,
                              Set<String> rectifySet,ComplianceAssessItemDto complianceAssessItemDto,
                              ComplianceAssessPointDto complianceAssessPointDto,ArrayList<TcomplianceItemPoint> complianceItemPoints){
        //遍历检测项
        for(TcomplianceDetectAssessRel detectAssessRel : detectAssessRels) {
            //获取检测项id
            Long detectPoinitId = detectAssessRel.getDetectPoinitId();
            if (itemPointMap.containsKey(detectPoinitId)) {
                //获取当前检测项
                TcomplianceItemPoint tComplianceItemPoint = itemPointMap.get(detectPoinitId);
                if (resultDtoMap.containsKey(detectPoinitId)) {
                    //获取检测项结果
                    List<ComplianceItemPointResultDto> resultDtos = resultDtoMap.get(detectPoinitId);
                    //此方法将某些项列表小项的截图加入结果集合中展示
                    handleScreenshotIntoResult(resultDtos);

                    ComplianceItemPointResultDto resultDto = resultDtos.get(0);
                    Boolean detectPointResult = resultDto.getDetectPointResult();
                    String properties = resultDto.getProperties();
                    JSONObject parseObject = JSON.parseObject(properties);
                    String rectifySuggest = (String) parseObject.get("rectifySuggest");//整改建议

                    //如果单个检测项是满足但是也有建议也不进行展示故作处理
                    if (detectPointResult) {
                        JSONObject json = JSON.parseObject(resultDto.getProperties());
                        json.put("rectifySuggest", "");
                        resultDto.setProperties(json.toString());
                    }
                    tComplianceItemPoint.setResultDto(resultDto);

                    //获取不合规建议
                    getNonCompliance(pointResult, detectPointResult, rectifySuggest, rectifySet, complianceAssessItemDto, complianceAssessPointDto);
                }
                //添加当前检测项
                complianceItemPoints.add(tComplianceItemPoint);
            }
        }
    }

    /**
     * 将收集使用的个人信息类型的单个小项的截图加入结果集
     * @param resultDtos
     * @return
     */
    private void handleScreenshotIntoResult(List<ComplianceItemPointResultDto> resultDtos){
        for (ComplianceItemPointResultDto item : resultDtos) {
            if(!getTrueOrFalse(item.getPointId(),OTHER_SPECIAL_ID)){
                continue;
            }
            Set<String> screenshots = new HashSet<>();
            //如果检测项是收集使用的个人信息类型，图片展示为该检测项的截图+个人身份信息单项截图+个人身份信息场景截图
            if(item.getPointId() == 104L || item.getPointId() == 404L){
                //获取个人身份信息截图
                Example example = new Example(TcompliancePersonalInfo.class);
                example.createCriteria().andEqualTo("taskId",item.getTaskId());
                List<TcompliancePersonalInfo> compliancePersonalInfoList = compliancePersonalInfoMapper.selectByExample(example);
                compliancePersonalInfoList.forEach(list ->{
                    if(StringUtils.isNotBlank(list.getAttachmentUrls())){
                        String picture = list.getAttachmentUrls().replaceAll(ijiamiCommonProperties.getProperty("fastDFS.ip"),"/");
                        JSONArray pictureList = JSONArray.parseArray(picture);
                        getScreenshot(pictureList,screenshots);
                    }
                });

                //获取个人身份信息结论截图(根据个人信息项目分类会存多个结论点）
                List<TcomplianceItemPointResult> tComplianceItemPointResults = complianceItemPointResultMapper.findtItemPointResultByPointCode(item.getTaskId(),"personalWordPoint");
                tComplianceItemPointResults.stream().forEach(result ->{
                    JSONObject properties = JSON.parseObject(result.getProperties());
                    if(properties.containsKey("screenImg")) {
                        if (properties.getJSONArray("screenImg") != null && properties.getJSONArray("screenImg").size() > 0) {
                            JSONArray screeImg = properties.getJSONArray("screenImg");
                            getScreenshot(screeImg,screenshots);
                        }
                    }
                });

            }

            //如果是实际触发使用的权限是否超出隐私政策所描述范围或隐私政策中权限描述是否超出实际触发使用的权限
            //图片展示为权限场景截图+该检测项截图
            if(item.getPointId() == 101L || item.getPointId() == 401L || item.getPointId() == 102L || item.getPointId() == 402L){
                List<TcomplianceItemPointResult> tComplianceItemPointResults = complianceItemPointResultMapper.findtItemPointResultByPointCode(item.getTaskId(),"permissionPurposePoint");
                tComplianceItemPointResults.stream().forEach(result ->{
                    JSONObject properties = JSON.parseObject(result.getProperties());
                    if(properties.containsKey("screenImg")) {
                        if (properties.getJSONArray("screenImg") != null && properties.getJSONArray("screenImg").size() > 0) {
                            JSONArray screeImg = properties.getJSONArray("screenImg");
                            getScreenshot(screeImg,screenshots);
                        }
                    }
                });
            }

            //获取该检测项的截图
            JSONObject properties = JSON.parseObject(item.getProperties());
            if(properties.containsKey("screenImg")){
                if(properties.getJSONArray("screenImg") != null && properties.getJSONArray("screenImg").size()>0){
                    JSONArray screeImg= properties.getJSONArray("screenImg");
                    getScreenshot(screeImg,screenshots);
                }
            }

            if(!CollectionUtils.isEmpty(screenshots)){
                List<String> m = new ArrayList<>(screenshots);
                properties.put("screenImg",m);
            }
            item.setProperties(properties.toJSONString());
        }
    }

    /**
     * 判断是否是特殊项
     * @param detectPoinitId
     */
    private boolean getTrueOrFalse (Long detectPoinitId,String pointId){
        String[] ids = pointId.split(",");
        if(ids.length == 0){
            return false;
        }
        for(String id:ids){
            if (id.equals(detectPoinitId.toString())){
                return true;
            }
        }
        return false;
    }

    /**
     * 变更截图内容
     * @param arrays
     * @param sets
     */
    private void getScreenshot(JSONArray arrays,Set<String> sets){
        arrays.stream().forEach(array ->{
            if(!sets.contains(array)){
                sets.add(array.toString());
            }
        });
    }

    /**
     * 组装不合规时要展示的建议
     * @param pointResult 匹配的表达式结果
     * @param detectPointResult 每个检测项对应的结果
     * @param rectifySuggest 每个检测项默认的结论
     * @param rectifySet 要组装的结论集合
     * @param complianceAssessItemDto 评估项
     * @param complianceAssessPointDto 评估点
     */
    private void getNonCompliance (Boolean pointResult,Boolean detectPointResult,String rectifySuggest,Set<String> rectifySet,
                                   ComplianceAssessItemDto complianceAssessItemDto,ComplianceAssessPointDto complianceAssessPointDto){
        if(pointResult == null){//无表达式时按照常规方式判断
            if(!detectPointResult){//不合规
                if(StringUtils.isNotBlank(rectifySuggest) && !rectifySet.contains(rectifySuggest)){
                    rectifySet.add(rectifySuggest);
                }
                complianceAssessPointDto.setIsSatisfy(false);//记录评估点为不满足
                complianceAssessItemDto.setIsSatisfy(false);//记录评估项为不满足
            }
        }else if(!pointResult){//有表达式且为不合规时
            if(StringUtils.isNotBlank(rectifySuggest) && !rectifySet.contains(rectifySuggest)){
                rectifySet.add(rectifySuggest);
            }
            complianceAssessPointDto.setIsSatisfy(false);//记录评估点为不满足
            complianceAssessItemDto.setIsSatisfy(false);//记录评估项为不满足
        }
    }

    /**
     * 构建自定义评估点截图数据
     * @param screenImgJson 图片json数组
     * @return
     */
    private List<String> getImages(String screenImgJson){
        List<String> list = new ArrayList<>();
        if (StringUtils.isEmpty(screenImgJson)) {
            return list;
        }
        try {
            JSONArray screenImg = JSON.parseArray(screenImgJson);
            for(int i = 0;i < screenImg.size();i++){
                list.add(screenImg.get(i).toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("图片获取失败");
        }
        return list;
    }

    /**
     * 该方法返回当前检测实例中所有不满足的评估项跟评估点
     * @param taskId 检测任务id
     * @param terminalType 设备类型
     * @return
     * @throws Exception
     */
    private List<ComplianceAssessItemDto> getNotFillCondition(Long taskId,Integer terminalType,TaskDetectionTypeEnum detectionTypeEnum) throws Exception {
        //获取到所有结果
        List<ComplianceAssessItemDto> allDTOs = getAllCondition(taskId,terminalType,detectionTypeEnum);
        //找出所有不满足的评估项结果
        List<ComplianceAssessItemDto> ComplianceAssessItemDTOs = allDTOs.stream().filter(dtos -> !dtos.getIsSatisfy()).collect(Collectors.toList());
        ComplianceAssessItemDTOs.forEach(notfillDtos ->{
            //获取每个评估项下面的所有评估点
            List<ComplianceAssessPointDto> complianceAssessPointDTOList = notfillDtos.getComplianceAssessPointDtoList();
            //筛选出不满足的评估点
            List<ComplianceAssessPointDto> pointList = new ArrayList<>();
            pointList=complianceAssessPointDTOList.stream().filter(list -> (list.getIsSatisfy() != null && !list.getIsSatisfy())).collect(Collectors.toList());
            //重组评估项下面的不满足评估点
            notfillDtos.setComplianceAssessPointDtoList(pointList);
        });
        //返回重组后的结果
        return ComplianceAssessItemDTOs.stream().sorted(Comparator.comparingInt(ComplianceAssessItemDto::getSort)).collect(Collectors.toList());
    }

    /**
     * 该方法返回上次违规检测的本次检测结果（所有检测点均展示本次的结果）
     * @param terminalType
     * @param parentTaskId
     * @return
     * @throws Exception
     */
    public List<ComplianceAssessItemDto> getLastNotFillCondition(Long taskId,Integer terminalType,Long parentTaskId,TaskDetectionTypeEnum detectionTypeEnum) throws Exception {
        //根据父检测任务id查找父检测任务
        TTask parentTask = taskMapper.selectByPrimaryKey(parentTaskId);
        if(parentTask == null){
            log.info("查询不到父检测任务");
            return null;
        }
        //要返回的结果
        List<ComplianceAssessItemDto> list = new ArrayList<>();
        //父检测任务违规项
        List<ComplianceAssessItemDto> complianceAssessItemDtos = getNotFillCondition(parentTask.getTaskId(),terminalType,detectionTypeEnum);
        //本次检测任务所有结果
        List<ComplianceAssessItemDto> itemDtos = getAllCondition(taskId,terminalType,detectionTypeEnum);
        /**
         * 取交集例子
         * 两个List求交集，一个是 alist<ObjectA>  一个是bList<ObjectB> .
         *不管list内置的对象相同与否，都要把他们的属性值取出来做对比，对象是无法对比的。说到底还是字符串的对比。
         *下面的例子是：两个对象各自取出 三个要对比的属性值 拼接成 ‘attr1|attr2|attr3’ 这样的字符串做对比
         * List<ObjectA> resList = aList.stream().filter(item -> bList.stream().map(e -> e.getFllName() + "|" + e.getSalary() + "|" + e.getSex())
         *   .collect(Collectors.toList()).contains(item.getName() + "|" + item.getMoney() + "|" + item.getSex()))
         *  .collect(Collectors.toList());
         */

        for(int i=0;i<itemDtos.size();i++){
            //存放检测点列表交集
            List<ComplianceAssessPointDto> iList = new ArrayList<>();
            for(int j=0;j<complianceAssessItemDtos.size();j++){
                if(itemDtos.get(i).getName().equals(complianceAssessItemDtos.get(j).getName())){
                    //取出父任务的检测点集合
                    List<ComplianceAssessPointDto> cList=complianceAssessItemDtos.get(j).getComplianceAssessPointDtoList();
                    //取交集
                    iList =itemDtos.get(i).getComplianceAssessPointDtoList().stream().filter(a -> cList.stream().map(b -> b.getName())
                            .collect(Collectors.toList()).contains(a.getName())
                    ).collect(Collectors.toList());
                    break;
                }
            }
            if(iList !=null && iList.size()>0){
                itemDtos.get(i).setComplianceAssessPointDtoList(iList);
                list.add(itemDtos.get(i));
            }
        }

        return list;
    }

    /**
     * 该方法返回与父检测项对比后的新增不合规项（所有检测点均展示本次的结果）
     * @param taskId
     * @param terminalType
     * @param parentTaskId
     * @return
     * @throws Exception
     */
    public List<ComplianceAssessItemDto> getNewNotFillCondition(Long taskId,Integer terminalType,Long parentTaskId,TaskDetectionTypeEnum detectionTypeEnum) throws Exception {
        //查找出父检测任务
        TTask parentTask = taskMapper.selectByPrimaryKey(parentTaskId);
        if(parentTask == null){
            log.info("查询不到父检测任务");
            return null;
        }
        //要返回的结果
        List<ComplianceAssessItemDto> list = new ArrayList<>();

        //父检测任务违规项
        List<ComplianceAssessItemDto> complianceAssessItemDtos = getNotFillCondition(parentTask.getTaskId(),terminalType,detectionTypeEnum);
        //本次检测任务违规项
        List<ComplianceAssessItemDto> itemDtos = getNotFillCondition(taskId,terminalType,detectionTypeEnum);

        /**集合差集公式
         * List<A> differences = a.stream().filter(item -> !b.stream().map(e ->
         *     e.getCode()).collect(Collectors.toList()).contains(item.getCode())
         *   ).collect(Collectors.toList());
         */

        for(int i=0;i<itemDtos.size();i++){
            //存放检测点列表差集
            List<ComplianceAssessPointDto> iList = new ArrayList<>();
            //匹配不上的标志
            int k=0;
            for(int j=0;j<complianceAssessItemDtos.size();j++){
                //如果能匹配上
                if(itemDtos.get(i).getName().equals(complianceAssessItemDtos.get(j).getName())){
                    //取出父任务的检测点集合
                    List<ComplianceAssessPointDto> cList=complianceAssessItemDtos.get(j).getComplianceAssessPointDtoList();
                    //取差集
                    iList=itemDtos.get(i).getComplianceAssessPointDtoList().stream().filter(item -> !cList.stream().map(e -> e.getName())
                            .collect(Collectors.toList()).contains(item.getName())).collect(Collectors.toList());
                    k=0;
                    break;
                }else{
                    k++;
                }
            }
            if(k > 0){//整个循环都匹配不上的话说明是本次新增的违规项
                list.add(itemDtos.get(i));
            }else if(k == 0 && iList !=null && iList.size()>0){
                itemDtos.get(i).setComplianceAssessPointDtoList(iList);
                list.add(itemDtos.get(i));
            }
        }
        return list;
    }

    /**
     * 查询自定义检测项
     * @param taskId
     * @return
     */
    @Override
    public List<TcomplianceAssessPointResultEntity> findCustonPointList(Long taskId) {
        Example example = new Example(TcomplianceAssessPointResultEntity.class);
        example.selectProperties("id","taskId","name","conclusion","assessStandard","testMainPoint","testWay","rectifySuggest","isSatisfy","properties","screenImg");
        example.createCriteria().andEqualTo("taskId",taskId);
        return complianceAssessPointResultMapper.selectByExample(example);
    }

    /**
     * 查询固定模板
     * @param terminalType
     * @return
     */
    @Override
    public List<TcomplianceAssessPointTemplateEntity> findBaseSelectByTerminalType(Integer terminalType) {
        //android、ios公用
        if(2 == terminalType){
            terminalType = 1;
        }
        //小程序公用
        if(5 == terminalType){
            terminalType = 4;
        }
        return complianceAssessPointTemplateMapper.baseSelectByTerminalType(terminalType);
    }

    /**
     * 保存自定义检测点数据
     * @param dto
     */
    @Override
    public void createCustomAssessPointByTemplateIds(CustomAssessPointResultDto dto) {
        List<Integer> ids = dto.getIds();
        Long taskId = dto.getTaskId();
        if(ArrayUtil.isEmpty(ids)){
            return ;
        }
        Example example1 = new Example(TcomplianceAssessPointTemplateEntity.class);
        example1.createCriteria().andIn("id",ids);
        List<TcomplianceAssessPointTemplateEntity> list = complianceAssessPointTemplateMapper.selectByExample(example1);
        list.forEach(tComplianceAssessPointTemplateEntity -> {
            TcomplianceAssessPointResultEntity insert = new TcomplianceAssessPointResultEntity();
            BeanUtils.copyProperties(tComplianceAssessPointTemplateEntity,insert);
            insert.setTaskId(taskId);
            insert.setId(null);
            //先排查是否存在相同的
            int count = complianceAssessPointResultMapper.selectCount(insert);
            if(count>0){
                throw new IjiamiRuntimeException("存在重复的自定义检测项");
            }
            insert.setConclusion(tComplianceAssessPointTemplateEntity.getDefaultConclusion());
            insert.setCreateUserId(getCurrentUserId()).setCreateTime(new Date());
            insert.setColor(ShowPointColorEnum.WHITE);
            complianceAssessPointResultMapper.insertSelective(insert);
        });
    }

    @Override
    public void editCustomAssessPoint(TcomplianceAssessPointResultEntity entity) {
        TcomplianceAssessPointResultEntity query = new TcomplianceAssessPointResultEntity();
        query.setTaskId(entity.getTaskId());
        query.setName(entity.getName());
        query.setAssessStandard(entity.getAssessStandard());
        query.setTestMainPoint(entity.getTestMainPoint());
        query.setTestWay(entity.getTestWay());
        TcomplianceAssessPointResultEntity exist = complianceAssessPointResultMapper.selectOne(query);

        if(entity.getId() != null){
            if(exist!=null && !exist.getId().equals(entity.getId())){
                throw new IjiamiRuntimeException("存在重复的自定义检测项");
            }
            complianceAssessPointResultMapper.updateByPrimaryKeySelective(entity);
        }else{
            complianceAssessPointResultMapper.insert(entity);
        }
    }

    /**
     * 删除自定义检测项结果
     * @param entity
     */
    @Override
    public void deleteEntityById(TcomplianceAssessPointResultEntity entity) {
        complianceAssessPointResultMapper.delete(entity);
    }

    /**
     * 通过关键字搜索，限定5条数据
     * @param itemPointName
     * @param terminalType
     * @return
     */
    @Override
    public List<TcomplianceItemPoint> findItemPointStageByName(String itemPointName,Integer terminalType) {
        //小程序公用
        if(5 == terminalType){
            terminalType = 4;
        }
        return complianceItemPointMapper.findItemPointStageByName(itemPointName,terminalType);
    }
}
