package cn.ijiami.detection.service.impl.compliance;

import cn.ijiami.detection.entity.compliance.TcomplianceItem;
import cn.ijiami.detection.mapper.compliance.ComplianceItemMapper;
import cn.ijiami.detection.service.api.compliance.IcomplianceItemService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ComplianceItemServiceImpl implements IcomplianceItemService {

    @Autowired
    private ComplianceItemMapper complianceItemMapper;

    @Override
    public List<TcomplianceItem> queryItemByNodeCode(String nodeCode, Integer terminalType) {
        return complianceItemMapper.queryItemByNodeCode(nodeCode,terminalType);
    }
}
