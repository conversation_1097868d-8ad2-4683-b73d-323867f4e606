package cn.ijiami.detection.service.impl;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import cn.ijiami.detection.service.api.IHitShellService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;

import cn.ijiami.detection.VO.IpaShellVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.entity.TIpaShellRecord;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.enums.ShellStatusEnum;
import cn.ijiami.detection.mapper.TIpaShellRecordMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.service.api.IShellSendMessage;
import cn.ijiami.framework.common.enums.HiddenEnum;
import cn.ijiami.message.enums.MessageClassifyEnum;
import cn.ijiami.message.enums.MessageNotificationEnum;
import cn.ijiami.message.service.api.IMessageService;
import cn.ijiami.message.vo.MessageSendVO;

/**
 * 推送砸壳进度服务
 *
 * <AUTHOR>
 * @date 2020-06-30 18:51
 */
@Service
public class ShellSendMessageImpl extends BaseDetectionMongoDBDAOImpl<TaskDetailVO> implements IShellSendMessage {

    private static final Logger logger = LoggerFactory.getLogger(ShellSendMessageImpl.class);

    @Autowired
    private IMessageService       messageService;
    @Resource
    private TIpaShellRecordMapper tIpaShellRecordMapper;
    @Resource
    private TTaskMapper           taskMapper;

    @Value("${ijiami.ios.remote.tool.status}")
    private Boolean iosRemoteToolStatus;

    @Autowired
    private IHitShellService iHitShellService;

    @Override
    public void sendShellMessage(Long taskId, ShellStatusEnum status) {
        sendShellMessage(taskId, status.getValue());
    }

    @Override
    public void sendShellMessage(Long taskId, Integer status) {
        sendShellMessage(taskId, status, null);
    }

    @Override
    public void sendShellMessage(Long taskId, int status, String desc) {
        TIpaShellRecord record = tIpaShellRecordMapper.selectOneByTaskId(taskId);
        // 该记录被人工终止为失败，不推送任何消息
        if (record.getStatus() == ShellStatusEnum.STOP.getValue()) {
            return;
        }
        // 查询任务
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        IpaShellVO ipaShellVO = iHitShellService.getShellInfoByTaskId(task.getTaskId(),task.getCreateUserId(),true);
        // 成功状态得时候推送正确得地址
        if (status == ShellStatusEnum.SUCCESS.getValue()) {
            ipaShellVO.setDesc(desc);
            ipaShellVO.setTextDescription("砸壳已完成");
            ipaShellVO.setStyle(null);
        }
        ipaShellVO.setStatus(status);

        // 发送脱壳进度
        MessageSendVO messageSendVO = new MessageSendVO();
        messageSendVO.setClassifyEnum(MessageClassifyEnum.BROADCAST);
        messageSendVO.setNotificationType(MessageNotificationEnum.INFO);
        messageSendVO.setHiddenEnum(HiddenEnum.HIDDEN);
        messageSendVO.setMessageTypeCode("ipa-shell");
        messageSendVO.setMessageTypeName("ipa脱壳");
        messageSendVO.setMessageTitle("脱壳进度-" + status);
        messageSendVO.setMessageContent(JSON.toJSONString(ipaShellVO));
        messageSendVO.setTopicName("/ipa-shell-service");
        messageSendVO.setSaveData(true);
        messageService.sendMessage(messageSendVO);
        logger.info("ipa-shell-service推送：{}", ipaShellVO);
    }

    @Override
    public void updateMongoTask(String documentId,DynamicAutoStatusEnum status,String desc) {
        Update update = new Update();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("_id", documentId);
        update.set("dynamic_detection_status", status.getValue());
        if(StringUtils.isBlank(desc)) {
            desc = status.getName();
        }
        update.set("dynamic_detection_description", desc);
        update(paramMap, update);
    }

    /**
     * 根据砸壳状态更新检测任务状态
     *
     * @param taskId
     * @param status
     */
    public void updateDynamicStatusByShellStatus(Long taskId, int status, String desc) {
        // 更新任务状态
        TTask task = taskMapper.selectByPrimaryKey(taskId);
        if (Objects.isNull(task)) {
            return;
        }
        // 如果任务已经中断直接return
        if (task.getDynamicStatus().getValue() == DynamicAutoStatusEnum.DETECTION_AUTO_FAILED.getValue()) {
            return;
        }
        TTask updateTask = new TTask();
        updateTask.setTaskId(taskId);
        if (status == ShellStatusEnum.RUN.getValue()) {
            // 更新砸壳状态，为砸壳中
            updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_HIT);
            logger.info("hitId-{}-DETECTION_AUTO_HIT sendShellMessage", task.getTaskId());
        }
        if (status == ShellStatusEnum.SUCCESS.getValue()) {
            // 更新任务状态,为下载IPA中
            // 如果是云手机，那么状态要流转到待检测中，等任务
            updateTask.setDynamicStarttime(new Date());
            if (iosRemoteToolStatus) {
                updateTask.setDynamicAutoWaiting();
            } else {
                updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA);
            }
            logger.info("hitId-{}-DETECTION_AUTO_DOWNLOAD_IPA sendShellMessage", task.getTaskId());
        }
        if (status == ShellStatusEnum.FAIL.getValue()) {
            // 更新任务状态,动态检测失败
            updateTask.setDynamicAutoFailure();
            logger.info("hitId-{}-DETECTION_AUTO_FAILED sendShellMessage", task.getTaskId());
            updateTask.setDescription(StringUtils.isNotBlank(desc) ? desc : "砸壳失败");
        }
        taskMapper.updateByPrimaryKeySelective(updateTask);
        // 更新mongo记录
        updateMongoTask(task.getApkDetectionDetailId(),updateTask.getDynamicStatus(),updateTask.getDescription());
    }
}
