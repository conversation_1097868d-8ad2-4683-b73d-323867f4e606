package cn.ijiami.detection.service.impl;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.hutool.core.io.FileUtil;
import cn.ijiami.base.common.user.IUser;
import cn.ijiami.detection.config.IjiamiCommonProperties;
import cn.ijiami.detection.entity.TAITrainImage;
import cn.ijiami.detection.enums.AITrainImageCategoryEnum;
import cn.ijiami.detection.enums.AITrainImageStatusEnum;
import cn.ijiami.detection.enums.StorageFileType;
import cn.ijiami.detection.fastdfs.SingleFastDfsFileService;
import cn.ijiami.detection.mapper.TAITrainImageMapper;
import cn.ijiami.detection.query.AITrainImagePageQuery;
import cn.ijiami.detection.query.AITrainImageVo;
import cn.ijiami.detection.service.api.AITrainService;
import cn.ijiami.detection.service.api.ISendMessageService;
import cn.ijiami.detection.service.api.IStorageLogService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.FileVOUtils;
import cn.ijiami.detection.validator.ImageUploadValidator;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiFileNotFoundException;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.UuidUtil;
import cn.ijiami.manager.user.entity.User;
import cn.ijiami.manager.user.service.api.IUserService;
import cn.ijiami.message.enums.MessageNotificationEnum;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AiTrainServiceImpl.java
 * @Description ai训练相关
 * @createTime 2024年03月06日 17:40:00
 */
@Slf4j
@Service
public class AITrainServiceImpl implements AITrainService {

    @Autowired
    private TAITrainImageMapper taiTrainImageMapper;

    @Autowired
    private SingleFastDfsFileService singleFastDfsFileService;

    @Autowired
    private IjiamiCommonProperties commonProperties;

    @Autowired
    private IStorageLogService iStorageLogService;

    @Autowired
    private ISendMessageService sendMessageServiceImpl;

    @Autowired
    private IUserService userService;

    @Transactional
    @Override
    public String uploadImage(IUser currentUser, MultipartFile data) throws IOException, IjiamiApplicationException {
        log.info("上传文件 file={}", data.getName());
        String filePath = commonProperties.getFilePath() + File.separator + "default" + File.separator + data.getOriginalFilename();
        File temp = new File(filePath);
        try {
            data.transferTo(temp);
            if (!ImageUploadValidator.validateImage(temp, 640, 640)) {
                throw new IjiamiApplicationException("图片格式不正确或尺寸不为640x640");
            }
            FileVO uploadDfsFile = FileVOUtils.convertFileVOByFile(temp);
            FileVO fileVO = singleFastDfsFileService.instance().storeFile(uploadDfsFile);
            User user = new User();
            user.setUserName(currentUser.getUsername());
            user.setUserId(currentUser.getUserId());
            String imageUrl = commonProperties.getProperty("detection.result.url.prefix") + fileVO.getFileUrl();
            iStorageLogService.saveStorageLogByFastDfsPath(imageUrl, imageUrl, StorageFileType.IMAGE, user);
            return imageUrl;
        } finally {
            temp.delete();
        }
    }

    @Transactional
    @Override
    public void saveOrUpdate(IUser currentUser, Long id, Integer category, String imageUrl, Long taskId) throws IOException, IjiamiApplicationException {
        TAITrainImage image;
        if (Objects.nonNull(id) && id > 0) {
            image = taiTrainImageMapper.selectByPrimaryKey(id);
            if (image.getStatus() == AITrainImageStatusEnum.DELETE) {
                throw new IjiamiFileNotFoundException("已删除的数据不可修改");
            }
            if (image.getStatus() == AITrainImageStatusEnum.EXPORT) {
                throw new IjiamiFileNotFoundException("已导出的数据不可修改");
            }
            image.setStatus(AITrainImageStatusEnum.WAITING_FOR_TRAINING);
            image.setCategory(category);
            if (!StringUtils.equalsIgnoreCase(image.getImagePath(), imageUrl)) {
                iStorageLogService.deleteFile(image.getImagePath());
                image.setImagePath(imageUrl);
                iStorageLogService.markFileAsUsedByAssociationKey(imageUrl, currentUser.getUserId());
            }
            taiTrainImageMapper.updateByPrimaryKeySelective(image);
        } else {
            image = new TAITrainImage();
            image.setCreateTime(new Date());
            image.setUpdateTime(new Date());
            image.setUpdateUserId(currentUser !=null ? currentUser.getUserId() : 1);
            image.setCreateUserId(currentUser !=null ? currentUser.getUserId() : 1);
            image.setStatus(AITrainImageStatusEnum.WAITING_FOR_TRAINING);
            image.setCategory(category);
            image.setImagePath(imageUrl);
            image.setTaskId(Integer.valueOf(taskId.toString()));
            taiTrainImageMapper.insert(image);
            iStorageLogService.markFileAsUsedByAssociationKey(imageUrl, (currentUser !=null ? currentUser.getUserId() : 1));
        }
    }

    @Override
    public PageInfo<AITrainImageVo> findImageByPage(AITrainImagePageQuery query) {
        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        List<AITrainImageVo> imageList = taiTrainImageMapper.findImage(
                Arrays.asList(AITrainImageStatusEnum.WAITING_FOR_TRAINING, AITrainImageStatusEnum.EXPORT));
        if(imageList != null && imageList.size()>0) {
        	imageList.forEach(image->{
        		if(StringUtils.isNotBlank(image.getImageUrl()) && image.getImageUrl().startsWith("group")) {
        			image.setImageUrl(commonProperties.getProperty("detection.result.url.prefix")+image.getImageUrl());
        		}
        	});
        }
        return new PageInfo<>(imageList);
    }

    @Override
    public void deleteImage(IUser currentUser, boolean isAdmin, Long imageId) throws IjiamiFileNotFoundException {
        TAITrainImage image = taiTrainImageMapper.selectByPrimaryKey(imageId);
        if (Objects.isNull(image)) {
            throw new IjiamiFileNotFoundException("文件已删除或不存在");
        }
        if (image.getStatus() == AITrainImageStatusEnum.EXPORT) {
            throw new IjiamiFileNotFoundException("已导出的数据不可删除");
        }
        if (!isAdmin && !Objects.equals(image.getCreateUserId(), currentUser.getUserId())) {
            throw new IjiamiRuntimeException("不是你创建的文件，无权限删除文件");
        }
        log.info("删除文件 file={}", image.getImagePath());
        taiTrainImageMapper.deleteByPrimaryKey(imageId);
        iStorageLogService.deleteFile(image.getImagePath());
    }

    @Override
    public Integer countNewImage() {
        Example example = new Example(TAITrainImage.class);
        example.createCriteria().andEqualTo("status", AITrainImageStatusEnum.WAITING_FOR_TRAINING);
        return taiTrainImageMapper.selectCountByExample(example);
    }

    @Transactional
    @Override
    public File exportImage(IUser user) {
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        File dir = new File(dynamicPath, UuidUtil.uuid());
        try {
            List<AITrainImageVo> imageList = taiTrainImageMapper.findImage(
                    Collections.singletonList(AITrainImageStatusEnum.WAITING_FOR_TRAINING));
            if (imageList.isEmpty()) {
                throw new IjiamiRuntimeException("没有需要导出的文件");
            }
            Map<AITrainImageCategoryEnum, List<AITrainImageVo>> imageGroups = imageList.stream().collect(Collectors.groupingBy(AITrainImageVo::getCategory));
            for (Map.Entry<AITrainImageCategoryEnum, List<AITrainImageVo>> entry:imageGroups.entrySet()) {
                AITrainImageCategoryEnum category = entry.getKey();
                List<AITrainImageVo> categoryImages = entry.getValue();
                File categoryDir = new File(dir, category.itemName());
                log.info("创建文件夹 {}", dir.mkdirs());
                for (AITrainImageVo image: categoryImages) {
                    if (StringUtils.isBlank(image.getImageUrl())) {
                        continue;
                    }
                    File file = new File(categoryDir, UuidUtil.uuid() + "." + CommonUtil.getFileExtName(image.getImageUrl()));
                    try {
                        FileUtils.copyURLToFile(new URL(image.getImageUrl()), file);
                        TAITrainImage updateImageStatus = new TAITrainImage();
                        updateImageStatus.setId(image.getId());
                        updateImageStatus.setStatus(AITrainImageStatusEnum.EXPORT);
                        taiTrainImageMapper.updateByPrimaryKeySelective(updateImageStatus);
                    } catch (IOException e) {
                        file.delete();
                        log.error("导出文件失败", e);
                    }
                }
            }
            if (FileUtil.loopFiles(dir).isEmpty()) {
                throw new IjiamiRuntimeException("导出失败");
            }
            String zipName = UuidUtil.uuid() + ".zip";
            String compress = CommonUtil.compress(dir.getAbsolutePath(), dir.getParent() + File.separator + zipName);
            return new File(compress);
        } catch (IjiamiRuntimeException e) {
            sendBuildErrorNotice(user, e.getMessage());
            throw e;
        } finally {
            FileUtils.deleteQuietly(dir);
        }
    }

    private void sendBuildErrorNotice(IUser user, String message) {
        User sendUser = userService.findUserById(user.getUserId());
        sendMessageServiceImpl.sendNoticeToBrowserTab(MessageNotificationEnum.ERROR,
                "report_build_failed", message, sendUser, StringUtils.EMPTY);
    }
}
