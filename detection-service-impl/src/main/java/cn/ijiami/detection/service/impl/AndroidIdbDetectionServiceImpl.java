package cn.ijiami.detection.service.impl;

import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_ANDROID_DEVICE_TYPE;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_ANDROID_STF_TOKEN;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_JAVA_STACK;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_JNI_STACK;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_LOG_DETAILS_DATA;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_MSG_TYPE;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_EXECUTOR;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_EXECUTOR_TYPE;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_LOG;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_LOG_ACTION_ID;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_LOG_BEHAVIOR_STAGE;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_LOG_ID;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_LOG_PERSONAL;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_OR_APPLET_PACKAGE_NAME;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DATA_ANDROID_TASK_PROGRESS;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_DEVICE_SERIAL;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_LAW_TYPE;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_PROTOCOL;
import static cn.ijiami.detection.constant.IdbMsgFieldName.CMD_TYPE;
import static cn.ijiami.detection.constant.IdbMsgFieldName.DYNAMIC_TYPE;
import static cn.ijiami.detection.constant.IdbMsgFieldName.NOTIFICATION_ID;
import static cn.ijiami.detection.constant.IdbMsgFieldName.TASK_ID;
import static cn.ijiami.detection.constant.PinfoConstant.DEFAULT_PROGRESS;

import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import cn.ijiami.detection.VO.AndroidSensorLog;
import cn.ijiami.detection.VO.RealTimeNetLog;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.expert.BehaviorPushInfo;
import cn.ijiami.detection.VO.expert.BehaviorWebInfo;
import cn.ijiami.detection.VO.expert.NetWorkPushInfo;
import cn.ijiami.detection.analyzer.BehaviorInfoAction;
import cn.ijiami.detection.analyzer.helper.BehaviorActionConvertHelper;
import cn.ijiami.detection.analyzer.helper.SpecialActionTypeConvertHelper;
import cn.ijiami.detection.bean.DynamicTaskContext;
import cn.ijiami.detection.config.IjiamiCommonProperties;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.dao.TaskDAO;
import cn.ijiami.detection.entity.TActionNougat;
import cn.ijiami.detection.entity.TPrivacyActionNougat;
import cn.ijiami.detection.entity.TPrivacyOutsideAddress;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.enums.AndroidDynamicDetectionCmdEnum;
import cn.ijiami.detection.enums.AndroidDynamicLogTypeEnum;
import cn.ijiami.detection.enums.AndroidManualTypeEnum;
import cn.ijiami.detection.enums.BehaviorStageEnum;
import cn.ijiami.detection.enums.BroadcastMessageTypeEnum;
import cn.ijiami.detection.enums.DetectionDynamicType;
import cn.ijiami.detection.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.enums.DynamicDeviceTypeEnum;
import cn.ijiami.detection.enums.DynamicLawStatusEnum;
import cn.ijiami.detection.enums.ExecutorTypeEnum;
import cn.ijiami.detection.enums.IdbMsgProtocolEnum;
import cn.ijiami.detection.enums.PrivacyStatusEnum;
import cn.ijiami.detection.enums.ReviewStatusEnum;
import cn.ijiami.detection.enums.TaskDetectionTypeEnum;
import cn.ijiami.detection.helper.AndroidActionLogConvertHelper;
import cn.ijiami.detection.helper.CustomDetectHelper;
import cn.ijiami.detection.helper.NetLogHelper;
import cn.ijiami.detection.service.DetectionDataService;
import cn.ijiami.detection.service.api.AndroidIdbDetectionService;
import cn.ijiami.detection.service.api.CacheService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.framework.common.exception.IjiamiCommandException;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AndroidIdbDetectionServiceImpl.java
 * @Description android idb检测服务
 * @createTime 2023年08月09日 16:25:00
 */
@Slf4j
@Service
public class AndroidIdbDetectionServiceImpl extends BaseIdbDetectionServiceImpl<TaskDetailVO> implements AndroidIdbDetectionService {

    @Autowired
    private TaskDAO taskDAO;

    @Autowired
    private IjiamiCommonProperties commonProperties;

    @Autowired
    private BehaviorActionConvertHelper actionConvertHelper;

    @Autowired
    private BehaviorInfoAction behaviorInfoAction;

    @Autowired
    private DetectionDataService detectionDataService;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private AndroidActionLogConvertHelper actionLogConvertHelper;

    @Value("${ijiami.remote.tool.status}")
    private boolean isRemote;

    @Override
    public void updateAndroidDynamicFromStomp(JSONObject message, String messageStr) throws Exception {
        try {
            JSONObject cmdData = message.getJSONObject(CMD_DATA);
            int type = cmdData.getInt(CMD_DATA_ANDROID_MSG_TYPE);
            int cmdType = message.getInt(CMD_TYPE);
            // 处理前端发送给idb的消息
            if (message.optString(CMD_PROTOCOL).equals(IdbMsgProtocolEnum.SEND.getName())) {
                handleClientSendMessageToIdb(message);
                return;
            }
            if (cmdType == AndroidDynamicDetectionCmdEnum.RUNNING.value) {
                if (type == AndroidDynamicLogTypeEnum.BEHAVIOR.value) {
                    handleAndroidBehaviorLog(message, cmdData);
                } else if (type == AndroidDynamicLogTypeEnum.NET.value) {
                    handleAndroidNetLog(message, cmdData);
                } else if (type == AndroidDynamicLogTypeEnum.SHAKE.value) {
                    handleAndroidSensorLog(message, cmdData);
                } else {
                    handleAndroidCommand(message, cmdData);
                }
            } else {
                handleAndroidCommand(message, cmdData);
            }
        } catch (Exception e) {
            log.error("updateAndroidDynamic msg:{} error", messageStr, e);
        }
    }

    public void handleAndroidBehaviorLog(JSONObject message, JSONObject cmdData) {
        Long taskId = cmdData.optLong(TASK_ID);
        Long interdictedTime = cacheService.getLong(PinfoConstant.INTERDICTED_ACTION + taskId);
        if (Objects.nonNull(interdictedTime)) {
            log.info("日志拦截");
            return;
        }
        if (isReviewTask(taskId)) {
            // 复核任务不发送日志
            log.info("复核任务不发送实时日志消息");
            return;
        }
        try {
            int dynamicType = message.getInt(DYNAMIC_TYPE);
            if (dynamicType == DetectionDynamicType.ANDROID_DEEP.getValue()) {
                JSONObject msgJson = cmdData.getJSONObject(CMD_DATA_ANDROID_OR_APPLET_LOG);
                if (!msgJson.containsKey(CMD_DATA_ANDROID_OR_APPLET_LOG_ACTION_ID)) {
                    log.info("TaskId:{} 没有type_id", taskId);
                    return;
                }
                DynamicTaskContext taskContext = getTaskContext(taskId);
                if (taskContext == null) {
                    log.info("TaskId:{} 任务数据为空", taskId);
                    return;
                }
                //20220809过滤传感器误报数据
                String stackInfo = msgJson.optString(CMD_DATA_ANDROID_JAVA_STACK, StringUtils.EMPTY);
                //过滤爱加密的包名
                stackInfo = cn.ijiami.detection.analyzer.parser.BehaviorInfoParser.filterIJMStackInfo(stackInfo);
                if(StringUtils.isNoneBlank(stackInfo)) {
                	msgJson.put(CMD_DATA_ANDROID_JAVA_STACK, stackInfo);
                }
                Long actionId = actionConvertHelper.convertActionId(
                        msgJson.getLong(CMD_DATA_ANDROID_OR_APPLET_LOG_ACTION_ID),
                        stackInfo,
                        msgJson.optString(CMD_DATA_ANDROID_LOG_DETAILS_DATA),
                        taskContext.getPackageName());
                Optional<TActionNougat> actionNougat = Optional.ofNullable(taskContext.getActionNougatMap().get(actionId));
                if (!actionNougat.isPresent()) {
                    log.info("TaskId:{} 行为id:{} 不存在", taskId, actionId);
                    return;
                }
                
                //过滤创建文件非个人信息数据
                if(SpecialActionTypeConvertHelper.FILTER_WRITE_CREATE_FILES_ACTIONIDS.contains(actionId)){
            		return;
            	}
                
                //特定行为的监控
                String isPrivacyAction = commonProperties.getProperty("monitor.privacy.action.enabled");
                String privacyActionIds = commonProperties.getProperty("monitor.privacy.actionIds");
                if(StringUtils.isNotBlank(isPrivacyAction) && isPrivacyAction.equals("true")) {
                    String[] parts = privacyActionIds.split(",");
                    List<Long> actionList = Arrays.stream(parts).map(Long::valueOf).collect(Collectors.toList());
                    if(actionList != null && actionList.size()>0) {
                    	if(!actionList.contains(actionId)){
                    		return;
                    	}
                    }
                }
                
                //过滤浏览器产生的行为
                String filter = commonProperties.getProperty("filter.action.stackInfo");
                if (StringUtils.isBlank(filter)) {
                    filter = SpecialActionTypeConvertHelper.FILTERPACKAGE;
                }
                
                String platformmark = commonProperties.getProperty("ijiami.platformmark");
            	if(StringUtils.isNotBlank(platformmark) && platformmark.equals("demo1")) {
            		filter = SpecialActionTypeConvertHelper.FILTERPACKAGE_DEMO1;
            	}

            	//判断是否存在指定的监控函数
            	if(!BehaviorInfoAction.appointActionGroupRegex(taskContext.getActionFilterGroupRegexList(), actionId, stackInfo, null)){
            		String sandboxPackageFilter = commonProperties.getProperty("sandbox.filter.packages");
                    if (StringUtils.isBlank(sandboxPackageFilter)) {
                        sandboxPackageFilter = SpecialActionTypeConvertHelper.WRITE_EXTERNAL_SYSTEM_APP_ACTIONIDS_REGEX;
                    }

                    if (StringUtils.isNoneBlank(stackInfo) && SpecialActionTypeConvertHelper.isMatcherContent(stackInfo, filter)) {
                        return;
                    }
                    String detailsData = msgJson.optString(CMD_DATA_ANDROID_LOG_DETAILS_DATA, StringUtils.EMPTY);
                    if (!BehaviorInfoAction.effectAction(
                            actionId,
                            stackInfo,
                            detailsData,
                            taskContext.getAppName(),
                            taskContext.getPackageName(), sandboxPackageFilter)) {
                        return;
                    }
                    if (BehaviorInfoAction.filterActionGroupRegex(taskContext.getActionFilterGroupRegexList(), actionId, stackInfo, detailsData)) {
                        return;
                    }

                    //过滤小米行为
                	if(SpecialActionTypeConvertHelper.ACTIONID_XIAOMI.contains(actionId)){
                		return;
                	}

                    //v2.6.2_R1 20220510增加行为类型转换bing
                    if (StringUtils.isNoneBlank(detailsData) && SpecialActionTypeConvertHelper.ACTIONIDS.contains(actionId)) {
                        String detail = SpecialActionTypeConvertHelper.actionTypeConvert(detailsData);
                        msgJson.put(CMD_DATA_ANDROID_LOG_DETAILS_DATA, StringUtils.isBlank(detail) ? detailsData : detail);
                    }

                    //过滤小米行为
                	if(SpecialActionTypeConvertHelper.ACTIONID_XIAOMI.contains(actionId)){
                		return;
                	}

                    //过滤动态广播行为
                	if(SpecialActionTypeConvertHelper.ACTIONID_32002.contains(actionId) && !SpecialActionTypeConvertHelper.isMatcherContent(detailsData, SpecialActionTypeConvertHelper.DYNAMICREGISTRATIONBROADCAST)){
                		return;
                	}

                    //20220907过滤获取运行中的进程代码中过滤掉这个行为 (去除getRunningAppProcesses监控)
                    if (SpecialActionTypeConvertHelper.ACTIONID_22002.contains(actionId)
                            && StringUtils.containsAny(stackInfo, SpecialActionTypeConvertHelper.FILTERACTIONSTACKINFO1, SpecialActionTypeConvertHelper.FILTERACTIONSTACKINFO2)) {
                        return;
                    }

                    if (SpecialActionTypeConvertHelper.WRITE_EXTERNAL_STORAGE_ACTIONIDS.contains(actionId) && StringUtils.isNoneBlank(detailsData) && SpecialActionTypeConvertHelper.writeExternalStorageRegex(detailsData)) {
                        return;
                    }

                    //过滤runtime的getprop产生的获取设备序列号信息
                    if (SpecialActionTypeConvertHelper.ACTIONID_31001.contains(actionId) && SpecialActionTypeConvertHelper.isMatcherContent(detailsData, SpecialActionTypeConvertHelper.FILTER_31001_DETAIL)) {
                        return;
                    }

                    //过滤28006 详情为gms包名
                    if (SpecialActionTypeConvertHelper.ACTIONID_28006.contains(actionId) && (SpecialActionTypeConvertHelper.isMatcherContent(stackInfo, SpecialActionTypeConvertHelper.FILTERPACKAGE) || SpecialActionTypeConvertHelper.isMatcherContent(detailsData, SpecialActionTypeConvertHelper.FILTERPACKAGE))) {
                        return;
                    }

                    //过滤动态广播行为
                	if(SpecialActionTypeConvertHelper.ACTIONID_32002.contains(actionId) && !SpecialActionTypeConvertHelper.isMatcherContent(detailsData, SpecialActionTypeConvertHelper.DYNAMICREGISTRATIONBROADCAST)){
                		return;
                	}

                	if(SpecialActionTypeConvertHelper.ACTIONID_10004.contains(actionId)  && !SpecialActionTypeConvertHelper.isMatcherContent(detailsData, SpecialActionTypeConvertHelper.ICCID_PHONE_NUMBER_PATTERN_10004)) {
                		return;
                	}

                	if(SpecialActionTypeConvertHelper.ACTIONID_28005.contains(actionId) && SpecialActionTypeConvertHelper.isMatcherContent(stackInfo, SpecialActionTypeConvertHelper.FILTERACTIONSTACKINFO_28005)) {
                		return;
                	}

                	//过滤多媒体发生变化
                	if(SpecialActionTypeConvertHelper.ACTIONID_14017_14018.contains(actionId) && (!SpecialActionTypeConvertHelper.isMatcherContent(detailsData, SpecialActionTypeConvertHelper.FILTER_DETAIL_14017_14018_REGEX))) {
                		return;
                	}


                	//过滤获取IP误报
                	if(SpecialActionTypeConvertHelper.ACTIONID_IP.contains(actionId) &&
                			(SpecialActionTypeConvertHelper.isMatcherContent(stackInfo, SpecialActionTypeConvertHelper.FILTER_DETAIL_IP_REGEX))) {
                		return;
                	}

                    //设定开关-打开后行为只有库里面规则比配才能入库-2023-03-08
                    String taierFilterActionOpen = commonProperties.getProperty("taier.filter.action.open");
                    //泰尔实验室行为数据
                    if (StringUtils.isNotBlank(taierFilterActionOpen) && "true".equals(taierFilterActionOpen) &&
                            !behaviorInfoAction.filterActionRegex(actionId, stackInfo, detailsData)) {
                        return;
                    }
            	}
                // 如果没有行为阶段，默认用前台阶段
                if (!msgJson.has(CMD_DATA_ANDROID_OR_APPLET_LOG_BEHAVIOR_STAGE)) {
                    msgJson.put(CMD_DATA_ANDROID_OR_APPLET_LOG_BEHAVIOR_STAGE, BehaviorStageEnum.BEHAVIOR_FRONT.getValue());
                }
                // 行为id
                msgJson.put(CMD_DATA_ANDROID_OR_APPLET_LOG_ACTION_ID, actionId);
                sendBehaviorLogMessage(taskId, message, msgJson, taskContext, actionNougat.get());
            } else {
                sendTaskDynamicLogMessage(message, taskId);
            }
        } catch (Exception e) {
            log.error(String.format("TaskId:%d 行为日志处理失败 {}", taskId), e);
        }
    }

    /**
     * 发送行为日志给前端
     * @param taskId
     * @param message
     * @param msgJson
     * @param taskContext
     * @param actionNougat
     */
    private void sendBehaviorLogMessage(Long taskId, JSONObject message, JSONObject msgJson, DynamicTaskContext taskContext, TActionNougat actionNougat) {
        // 是否涉及个人隐私
        PrivacyStatusEnum privacyStatus = getPrivacyStatus(Optional.ofNullable(actionNougat));
        TPrivacyActionNougat nougat = actionLogConvertHelper.buildPrivacyActionNougat(taskId, msgJson, taskContext, privacyStatus);
        detectionDataService.insertDynamicAction(nougat, taskContext);
        if (taskContext.getDetectionType() != TaskDetectionTypeEnum.DEPTH_EXP && taskContext.getDetectionType() != TaskDetectionTypeEnum.DEPTH_SPECIAL) {
            // 非个人信息相关的不发给前端
            if (actionNougat != null && actionNougat.getPersonal() == PrivacyStatusEnum.NO.getValue()) {
                return;
            }
            // 是否涉及个人隐私
            msgJson.put(CMD_DATA_ANDROID_OR_APPLET_LOG_PERSONAL, CommonUtil.beanToJson(privacyStatus));
            // 生成id
            msgJson.put(CMD_DATA_ANDROID_OR_APPLET_LOG_ID, String.valueOf(nougat.getId()));
            msgJson.put(CMD_DATA_ANDROID_OR_APPLET_EXECUTOR_TYPE, nougat.getExecutorType());
            msgJson.put(CMD_DATA_ANDROID_OR_APPLET_EXECUTOR, nougat.getExecutor());
            msgJson.put(CMD_DATA_ANDROID_OR_APPLET_PACKAGE_NAME, nougat.getPackageName());
            // 删除掉堆栈再发给前端
            msgJson.remove(CMD_DATA_ANDROID_LOG_DETAILS_DATA);
            msgJson.remove(CMD_DATA_ANDROID_JAVA_STACK);
            msgJson.remove(CMD_DATA_ANDROID_JNI_STACK);
            JSONObject cmdData = message.getJSONObject(CMD_DATA);
            cmdData.put(CMD_DATA_ANDROID_OR_APPLET_LOG, msgJson);
            iSendMessageService.sendTaskDynamicLogMessage(message, taskContext);
        } else {
            // 发送专家版行为
            BehaviorWebInfo behaviorInfo = getBehaviorWebInfo(nougat, actionNougat);
            BehaviorPushInfo behaviorPushInfo = new BehaviorPushInfo();
            behaviorPushInfo.setBehaviorInfo(behaviorInfo);
            iSendMessageService.sendTaskDynamicLogMessage(JSONObject.fromObject(CommonUtil.beanToJson(behaviorPushInfo)), taskContext);
        }
    }

    /**
     * 发送网络行为
     *
     * @param taskId
     * @param msgJson
     */
    private void sendNetworkLogMessage(Long taskId, JSONObject msgJson) {
        DynamicTaskContext taskContext = getTaskContext(taskId);
        if (Objects.isNull(taskContext)) {
            log.info("TaskId:{} 不在动态检测中", taskId);
            return;
        }
        TPrivacyOutsideAddress outsideAddress = actionLogConvertHelper.buildPrivacyOutsideAddress(msgJson, getTaskContext(taskId));
        detectionDataService.insertAndroidNetAction(outsideAddress, taskContext);
        if (TaskDetectionTypeEnum.isExpert(taskContext.getDetectionType())) {
            // 专家版的网络行为
            NetWorkPushInfo netWorkPushInfo = NetLogHelper.buildExpertNetPushInfo(outsideAddress);
            iSendMessageService.sendTaskNetLogMessage(JSONObject.fromObject(CommonUtil.beanToJson(netWorkPushInfo)), taskContext);
        } else {
            RealTimeNetLog netLog = NetLogHelper.buildNetLog(outsideAddress);
            iSendMessageService.sendTaskNetLogMessage(JSONObject.fromObject(netLog), taskContext);
        }

    }

    @NotNull
    private static BehaviorWebInfo getBehaviorWebInfo(TPrivacyActionNougat privacyActionNougat, TActionNougat actionNougat) {
        BehaviorWebInfo behaviorInfo = new BehaviorWebInfo();
        behaviorInfo.setId(privacyActionNougat.getId());
        behaviorInfo.setActionTime(privacyActionNougat.getActionTime());
        behaviorInfo.setActionName(actionNougat.getActionName());
        behaviorInfo.setActionId(actionNougat.getActionId());
        behaviorInfo.setIsPersonal(actionNougat.getPersonal() == PrivacyStatusEnum.YES.getValue());
        //这里做数据处理给前端展示
        behaviorInfo.setBehaviorStage(privacyActionNougat.getBehaviorStage());
        if (Objects.equals(ExecutorTypeEnum.SDK.getValue(), privacyActionNougat.getExecutorType())) {
            behaviorInfo.setExecutorShow(privacyActionNougat.getExecutor()+"sdk");
        } else {
            behaviorInfo.setExecutorShow("app");
        }
        return behaviorInfo;
    }

    private void handleClientSendMessageToIdb(JSONObject message) {
        JSONObject cmdData = message.getJSONObject(CMD_DATA);
        int cmdType = message.getInt(CMD_TYPE);
        if (cmdType == AndroidDynamicDetectionCmdEnum.STOP.value) {
            Long taskId = cmdData.optLong(TASK_ID);
            DynamicTaskContext taskContext = getTaskContext(taskId);
            if (taskContext != null) {
                // 如果超时后任务状态还没变，说明前端发送消息给idb中断任务失败，后台进行补偿
                startIdbMessageTimeoutTask(taskId, taskContext.getTaskProcessId(), message, task -> {
                    int dynamicType = message.getInt(DYNAMIC_TYPE);
                    handleAndroidDynamicStop(task, AndroidManualTypeEnum.USER_INTERRUPTED.value, dynamicType, "idb中断超时，服务器进行中断");
                });
            }
        }
    }

    public void handleAndroidNetLog(JSONObject message, JSONObject cmdData) {
        Long taskId = cmdData.optLong(TASK_ID);
        Long interdictedTime = cacheService.getLong(PinfoConstant.INTERDICTED_ACTION + taskId);
        if (Objects.nonNull(interdictedTime)) {
            log.info("日志拦截");
            return;
        }
        if (isReviewTask(taskId)) {
            // 复核任务不发送日志
            log.info("复核任务不发送实时日志消息");
            return;
        }
        DynamicTaskContext taskContext = getTaskContext(taskId);
        if (taskContext == null) {
            log.info("TaskId:{} 任务数据为空", taskId);
            return;
        }
        try {
            int dynamicType = message.getInt(DYNAMIC_TYPE);
            if (dynamicType == DetectionDynamicType.ANDROID_DEEP.getValue()) {
                JSONObject msgJson = cmdData.getJSONObject(CMD_DATA_ANDROID_OR_APPLET_LOG);
                TPrivacyOutsideAddress outsideAddress = actionLogConvertHelper.buildPrivacyOutsideAddress(msgJson, getTaskContext(taskId));
                detectionDataService.insertAndroidNetAction(outsideAddress, getTaskContext(taskId));
                if (TaskDetectionTypeEnum.isExpert(taskContext.getDetectionType())) {
                    // 专家版的网络行为
                    NetWorkPushInfo netWorkPushInfo = NetLogHelper.buildExpertNetPushInfo(outsideAddress);
                    iSendMessageService.sendTaskNetLogMessage(JSONObject.fromObject(CommonUtil.beanToJson(netWorkPushInfo)), taskContext);
                } else {
                    RealTimeNetLog netLog = NetLogHelper.buildNetLog(outsideAddress);
                    // 删除掉堆栈再发给前端
                    netLog.setRequestData("");
                    netLog.setResponseData("");
                    sendTaskNetLogMessage(JSONObject.fromObject(netLog), taskId);
                }
            }
        } catch (Exception e) {
            log.error(String.format("TaskId:%d 日志处理失败 {}", taskId), e);
        }
    }

    private AndroidSensorLog buildAndroidSensorLog(JSONObject msgJson) {
        AndroidSensorLog sensorLog = CommonUtil.jsonToBean(msgJson.toString(), AndroidSensorLog.class);
        if (Objects.nonNull(sensorLog)) {
            sensorLog.setActionTime(System.currentTimeMillis());
        }
        return sensorLog;
    }

    public void handleAndroidCommand(JSONObject message, JSONObject cmdData) {
        String taskId = cmdData.optString(TASK_ID);
        try {
            int dynamicType = message.getInt(DYNAMIC_TYPE);
            int cmdType = message.getInt(CMD_TYPE);
            int type = cmdData.getInt(CMD_DATA_ANDROID_MSG_TYPE);
            String cmdDataMsg = cmdData.getString(CMD_DATA_ANDROID_OR_APPLET_LOG);
            TTask tTask = taskMapper.selectByPrimaryKey(taskId);
            if (tTask == null) {
                throw new IjiamiCommandException("检测任务不存在,更新操作失败！");
            }
            boolean isCloudPhoneTask = dynamicType == DetectionDynamicType.ANDROID_FAST.getValue()
                    && Objects.nonNull(tTask.getDynamicDeviceType())
                    && tTask.getDynamicDeviceType() == DynamicDeviceTypeEnum.DYNAMIC_DEVICE_CLOUD;
            // 当任务是云手机快速检测任务，且是远程任务，且不是本地优先用户，且未进行复核检测时，不进行处理
            if (isCloudPhoneTask
                    && isRemote
                    && !CustomDetectHelper.getInstance().isPriorityLocal(tTask.getCreateUserId())
                    && tTask.getReviewStatus() == ReviewStatusEnum.DETECTION_REVIEW_NONE) {
                log.info("TaskId:{} 云手机的快速检测消息不进行处理", taskId);
                return;
            }
            int lawType = message.optInt(CMD_LAW_TYPE, -1);
            if (lawType > 0) {
                TaskDetailVO taskDetailVO = findById(tTask.getApkDetectionDetailId(), "taskDetailVO");
                if (taskDetailVO == null) {
                    throw new IjiamiCommandException("检测详情不存在,更新操作失败！");
                }
                if (taskDetailVO.getLawTypeCode() == null || !taskDetailVO.getLawTypeCode().contains(String.valueOf(lawType))) {
                    Map<String, Object> paramMap = new HashMap<>();
                    Update update = new Update();
                    paramMap.put("_id", tTask.getApkDetectionDetailId());
                    update.set("lawTypeCode", lawType + ",");
                    update(paramMap, update);
                    getTaskContext(tTask.getTaskId()).setLawType(lawType);
                }
            }
            // 快速检测失败 直接返回
            if (dynamicType == DetectionDynamicType.ANDROID_FAST.getValue()
                    && tTask.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_FAILED) {
                log.info("TaskId:{} 快速检测失败", taskId);
                return;
            }
            // 正常进度
            if (cmdType == AndroidDynamicDetectionCmdEnum.RUNNING.value) {
                handleAndroidDynamicRunning(tTask, type, message, cmdData);
            } else if (cmdType == AndroidDynamicDetectionCmdEnum.STOP.value) {
                // 异常进度
                handleAndroidDynamicStop(tTask, type, dynamicType, cmdDataMsg);
            } else if (cmdType == AndroidDynamicDetectionCmdEnum.SCREENSHOT.value) {
                // 截图数据
                saveScreenshotImageData(tTask.getTaskId(), cmdData);
            } else if (cmdType == AndroidDynamicDetectionCmdEnum.AI_DETECT_LOGIN.value) {
                // 通知前端弹窗给用户进行登录
                sendTaskStatusMessage(BroadcastMessageTypeEnum.DETECTION_AI_DETECT_NOT_LOGGED_IN,
                        BroadcastMessageTypeEnum.DETECTION_AI_DETECT_NOT_LOGGED_IN.getName(), tTask);
            }
        } catch (Exception e) {
            log.error(String.format("TaskId:%s 消息处理失败 {}", taskId), e);
        }
    }

    public void handleAndroidSensorLog(JSONObject message, JSONObject cmdData) {
        Long taskId = cmdData.optLong(TASK_ID);
        Long interdictedTime = cacheService.getLong(PinfoConstant.INTERDICTED_ACTION + taskId);
        if (Objects.nonNull(interdictedTime)) {
            log.info("日志拦截");
            return;
        }
        if (isReviewTask(taskId)) {
            // 复核任务不发送日志
            log.info("复核任务不发送实时日志消息");
            return;
        }
        DynamicTaskContext taskContext = getTaskContext(taskId);
        if (taskContext == null) {
            log.info("TaskId:{} 任务数据为空", taskId);
            return;
        }
        try {
            int dynamicType = message.getInt(DYNAMIC_TYPE);
            if (dynamicType == DetectionDynamicType.ANDROID_DEEP.getValue()) {
                JSONObject msgJson = cmdData.getJSONObject(CMD_DATA_ANDROID_OR_APPLET_LOG);
                if (msgJson.has("robot") || msgJson.has("phoneSensor")) {
                    AndroidSensorLog sensorLog = buildAndroidSensorLog(msgJson);
                    cacheService.cacheSensorAction(taskId, sensorLog);
                    sendTaskSensorLogMessage(msgJson, taskId);
                }
            }
        } catch (Exception e) {
            log.error(String.format("TaskId:%s 日志处理失败 {}", taskId), e);
        }
    }

    private void handleAndroidDynamicRunning(TTask tTask, int type, JSONObject message, JSONObject cmdData) {
        if (type == AndroidManualTypeEnum.CLEAR_LOG.value) {
            log.info("TaskId:{} idb 清除日志完成", tTask.getTaskId());
            cacheService.delete(PinfoConstant.INTERDICTED_ACTION + tTask.getTaskId());
            return;
        }
        int dynamicType = message.getInt(DYNAMIC_TYPE);
        saveNotification(message.optString(NOTIFICATION_ID, StringUtils.EMPTY), tTask);
        if (dynamicType == DetectionDynamicType.ANDROID_FAST.getValue()) {
            handleAndroidFastRunning(tTask, message, cmdData);
        } else if (dynamicType == DetectionDynamicType.ANDROID_DEEP.getValue()) {
            handleAndroidDeepRunning(tTask, message, cmdData);
        } else if (dynamicType == DetectionDynamicType.ANDROID_LAW.getValue()) {
            if (tTask.isReviewIn()) {
                handleAndroidReviewRunning(tTask, message, cmdData);
            } else {
                handleAndroidLawRunning(tTask, message, cmdData);
            }
        }
    }

    /**
     * 更新消息传过来的设备信息
     *
     * @param updateTask
     * @param message
     */
    private void updateDeviceInfo(TTask updateTask, TTask task, JSONObject message) {
        String deviceSerial = message.getString(CMD_DEVICE_SERIAL);
        String stfToken = message.getString(CMD_ANDROID_STF_TOKEN);
        DynamicDeviceTypeEnum deviceTypeEnum = DynamicDeviceTypeEnum.getItem(message.optInt(CMD_ANDROID_DEVICE_TYPE));
        // 有设备id 云手机
        if (StringUtils.isNotBlank(deviceSerial)) {
            updateTask.setDeviceSerial(deviceSerial);
        }
        if (StringUtils.isNotBlank(stfToken)) {
            updateTask.setStfToken(stfToken);
        }
        if (Objects.nonNull(deviceTypeEnum)) {
            updateTask.setDynamicDeviceType(deviceTypeEnum);
        }
        
        //保存使用设备记录
	    log.info("操作设备保存记录saveOperateLog={}, task.getDeviceSerial={}",deviceSerial, task.getDeviceSerial());
	    if(StringUtils.isBlank(task.getDeviceSerial()) && StringUtils.isNotBlank(deviceSerial)){
	    	log.info("1操作设备保存记录saveOperateLog={}",deviceSerial );
	    	taskDAO.saveOperateLog(deviceSerial, task);
	    }
    }

    /**
     * 快速检测进度消息处理
     *
     * @param tTask
     * @param message
     * @param cmdData
     */
    private void handleAndroidFastRunning(TTask tTask, JSONObject message, JSONObject cmdData) {
        // 如果动态检测已经完成，直接返回
        if (tTask.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) {
            log.info("TaskId:{} 动态检测已经完成", tTask.getTaskId());
            return;
        }
        String cmdDataMsg = cmdData.getString(CMD_DATA_ANDROID_OR_APPLET_LOG);

        Map<String, Object> paramMap = new HashMap<>();
        Update update = new Update();
        paramMap.put("_id", tTask.getApkDetectionDetailId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(tTask.getTaskId());
        updateTask.setDescription(cmdDataMsg);
        updateDeviceInfo(updateTask, tTask, message);
        if (Objects.isNull(tTask.getDynamicStarttime()) || tTask.getDynamicStatus() != DynamicAutoStatusEnum.DETECTION_AUTO_IN) {
            updateTask.setDynamicStarttime(new Date());
            updateTask.setDynamicAutoIn();
        }
        update.set("dynamic_detection_description", cmdDataMsg);
        update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_IN.getValue());
        int progress = DEFAULT_PROGRESS;
        if (cmdData.containsKey(CMD_DATA_ANDROID_TASK_PROGRESS)) {
            progress = Math.min(cmdData.getInt(CMD_DATA_ANDROID_TASK_PROGRESS), DEFAULT_PROGRESS);
            update.set("dynamicProgress", progress);
        }
        taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
        sendDynamicTaskProgressMessage(progress, tTask);
    }

    private void handleAndroidDeepRunning(TTask tTask, JSONObject message, JSONObject cmdData) {
        // 如果动态检测已经完成，直接返回
        if (tTask.getDynamicStatus() == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) {
            log.info("TaskId:{} 动态检测已经完成", tTask.getTaskId());
            return;
        }
        int type = cmdData.getInt(CMD_DATA_ANDROID_MSG_TYPE);
        String cmdDataMsg = cmdData.getString(CMD_DATA_ANDROID_OR_APPLET_LOG);

        Map<String, Object> paramMap = new HashMap<>();
        Update update = new Update();
        paramMap.put("_id", tTask.getApkDetectionDetailId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(tTask.getTaskId());
        if (Objects.isNull(tTask.getDynamicStarttime())) {
            updateTask.setDynamicStarttime(new Date());
        }
        updateDeviceInfo(updateTask, tTask, message);
        // 下载apk
        if (type == AndroidManualTypeEnum.DOWNLOAD_APP.value) {
            // 深度/快速检测
            updateTask.setDescription("App下载中");
            updateTask.setDynamicStatus(DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA);
            update.set("dynamic_detection_description", "App下载中");
            update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA.getValue());

            update(paramMap, update);
            taskMapper.updateByPrimaryKeySelective(updateTask);
            sendDynamicTaskProgressMessage(0, tTask);
        } else if (type == AndroidManualTypeEnum.SCREENSHOT_DATA.value) {
            log.info("截图数据");
        } else if (type == AndroidManualTypeEnum.APP_UNINSTALL_COMPLETE.value) {
            removeTaskContext(tTask);
        }
        // 其他正常情况
        else {
            updateTask.setDescription(cmdDataMsg);
            updateTask.setDynamicAutoIn();
            update.set("dynamic_detection_description", cmdDataMsg);
            update.set("dynamic_detection_status", DynamicAutoStatusEnum.DETECTION_AUTO_IN.getValue());
            taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
            sendDynamicTaskProgressMessage(DEFAULT_PROGRESS, tTask);
        }
    }

    // 法规检测
    private void handleAndroidLawRunning(TTask tTask, JSONObject message, JSONObject cmdData) {
        int type = cmdData.getInt(CMD_DATA_ANDROID_MSG_TYPE);
        String cmdDataMsg = cmdData.getString(CMD_DATA_ANDROID_OR_APPLET_LOG);

        Map<String, Object> paramMap = new HashMap<>();
        Update update = new Update();
        paramMap.put("_id", tTask.getApkDetectionDetailId());
        TTask updateTask = new TTask();
        updateTask.setTaskId(tTask.getTaskId());
        // 法规检测
        if (Objects.isNull(tTask.getLawStarttime())) {
            updateTask.setLawStarttime(new Date());
        }
        updateDeviceInfo(updateTask, tTask, message);
        // 下载apk
        if (type == AndroidManualTypeEnum.DOWNLOAD_APP.value) {
            // 深度/快速检测
            String desc = "App下载中";
            updateTask.setDescription(desc);
            updateTask.setDynamicLawStatus(DynamicLawStatusEnum.DETECTION_LAW_DOWNLOAD_IPA);
            update.set("dynamic_law_detection_status", DynamicLawStatusEnum.DETECTION_LAW_DOWNLOAD_IPA.getValue());
            update.set("describe", desc);
            update(paramMap, update);
            taskMapper.updateByPrimaryKeySelective(updateTask);
            sendDynamicTaskProgressMessage(0, tTask);
            removeTaskContext(tTask);
        } else if (type == AndroidManualTypeEnum.SCREENSHOT_DATA.value) {
            log.info("截图数据");
        } else if (type == AndroidManualTypeEnum.APP_UNINSTALL_COMPLETE.value) {
            removeTaskContext(tTask);
        }
        // 其他正常情况
        else {
            updateTask.setDescription(cmdDataMsg);
            updateTask.setDynamicLawIn();
            update.set("dynamic_law_detection_status", DynamicLawStatusEnum.DETECTION_LAW_IN.getValue());
            update.set("describe", cmdDataMsg);
            taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
            sendDynamicTaskProgressMessage(DEFAULT_PROGRESS, tTask);
        }
    }

    // 复核检测
    private void handleAndroidReviewRunning(TTask tTask, JSONObject message, JSONObject cmdData) {
        // 如果动态检测已经完成，直接返回
        if (tTask.getReviewStatus() == ReviewStatusEnum.DETECTION_REVIEW_SUCCEED) {
            log.info("TaskId:{} 复核检测已经完成", tTask.getTaskId());
            return;
        }
        int type = cmdData.getInt(CMD_DATA_ANDROID_MSG_TYPE);
        Map<String, Object> paramMap = new HashMap<>();
        Update update = new Update();
        paramMap.put("_id", tTask.getApkDetectionDetailId());
        TTask updateTask = new TTask();
        if (Objects.isNull(tTask.getReviewStarttime())) {
            updateTask.setReviewStarttime(new Date());
        }
        updateTask.setTaskId(tTask.getTaskId());
        // 复核检测
        updateDeviceInfo(updateTask, tTask, message);
        // 下载apk
        if (type == AndroidManualTypeEnum.DOWNLOAD_APP.value) {
            updateTask.setReviewStatus(ReviewStatusEnum.DETECTION_REVIEW_DOWNLOAD_APP);
            update(paramMap, update);
            taskMapper.updateByPrimaryKeySelective(updateTask);
            sendDynamicTaskProgressMessage(0, tTask);
            removeTaskContext(tTask);
        } else if (type == AndroidManualTypeEnum.SCREENSHOT_DATA.value) {
            log.info("截图数据");
        } else if (type == AndroidManualTypeEnum.APP_UNINSTALL_COMPLETE.value) {
            removeTaskContext(tTask);
        }
        // 其他正常情况
        else {
            updateTask.setReviewStatus(ReviewStatusEnum.DETECTION_REVIEW_IN);
            taskDAO.updateTaskStatus(tTask, updateTask, paramMap, update);
        }
        setReviewTask(tTask.getTaskId());
    }

    private void handleAndroidDynamicStop(TTask tTask, int type, int dynamicType, String cmdDataMsg) {
        // 避免截图失败导致任务中断
        if (StringUtils.equals(cmdDataMsg, "截图失败")) {
            return;
        }
        // 深度/快速检测
        if (dynamicType == DetectionDynamicType.ANDROID_DEEP.getValue()
                || dynamicType == DetectionDynamicType.ANDROID_FAST.getValue()) {
            taskDAO.updateDynamicFailure(tTask, type == AndroidManualTypeEnum.USER_INTERRUPTED.value ? "手动中断" : cmdDataMsg);
        }
        // 法规检测
        else if (dynamicType == DetectionDynamicType.ANDROID_LAW.getValue()) {
            // 复核检测失败
            if (tTask.isReviewIn()) {
                taskDAO.updateReviewFailure(tTask, type == AndroidManualTypeEnum.USER_INTERRUPTED.value ? "手动中断" : cmdDataMsg);
            } else {
                taskDAO.updateLawFailure(tTask, type == AndroidManualTypeEnum.USER_INTERRUPTED.value ? "手动中断" : cmdDataMsg);
            }
        }
        sendTaskStatusBroadcast(BroadcastMessageTypeEnum.DETECTION_ERROR, cmdDataMsg, tTask);
    }

}
