package cn.ijiami.detection.service.impl;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import cn.ijiami.detection.enums.AiUsageLimitTypeEnum;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.VO.DeleteDetectionConfig;
import cn.ijiami.detection.VO.DetectableTerminalVO;
import cn.ijiami.detection.VO.DetectionConfigItem;
import cn.ijiami.detection.VO.DetectionConfigVO;
import cn.ijiami.detection.VO.PageDetectionConfigVO;
import cn.ijiami.detection.VO.RoleUserResultVO;
import cn.ijiami.detection.VO.TDetectionConfigVO;
import cn.ijiami.detection.VO.TaskConditionCheckResult;
import cn.ijiami.detection.constant.PinfoConstant;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TDetectableTerminal;
import cn.ijiami.detection.entity.TDetectionConfig;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.entity.TUserTaskConsumption;
import cn.ijiami.detection.enums.DetectionConfigCategoryEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.job.XxlDetectWebServer;
import cn.ijiami.detection.mapper.RoleWithUserMapper;
import cn.ijiami.detection.mapper.TAssetsMapper;
import cn.ijiami.detection.mapper.TDetectableTerminalMapper;
import cn.ijiami.detection.mapper.TDetectionConfigMapper;
import cn.ijiami.detection.mapper.TTaskMapper;
import cn.ijiami.detection.mapper.TUserTaskConsumptionMapper;
import cn.ijiami.detection.query.DetectionPageQuery;
import cn.ijiami.detection.query.RoleUserQuery;
import cn.ijiami.detection.service.api.CacheService;
import cn.ijiami.detection.service.api.DetectionConfigService;
import cn.ijiami.framework.common.enums.StatusEnum;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.manager.role.entity.Role;
import cn.ijiami.manager.role.mapper.RoleMapper;
import cn.ijiami.manager.user.entity.User;
import cn.ijiami.manager.user.mapper.UserMapper;
import cn.ijiami.manager.user.mapper.UserRoleMapper;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName DetectionConfigServiceImpl.java
 * @Description 检测配置服务
 * @createTime 2023年06月21日 15:30:00
 */
@Service
public class DetectionConfigServiceImpl implements DetectionConfigService {

    @Autowired
    private TDetectionConfigMapper detectionConfigMapper;

    @Autowired
    private TAssetsMapper assetsMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private CacheService cacheService;

    @Autowired
    private TDetectableTerminalMapper detectableTerminalMapper;

    @Autowired
    private TTaskMapper taskMapper;

    @Autowired
    private RoleWithUserMapper roleWithUserMapper;

    @Autowired
    private TUserTaskConsumptionMapper userTaskConsumptionMapper;
    
    @Autowired
    private XxlDetectWebServer xxlDetectWebServer;

    @Transactional
    @Override
    public void saveOrUpdate(Long userId, DetectionConfigVO config) {
        if (Objects.isNull(config.getId()) && Objects.isNull(config.getCategory())) {
            throw new IllegalArgumentException("缺少数据");
        }
        TDetectionConfig query = new TDetectionConfig();
        if (config.getCategory() == DetectionConfigCategoryEnum.USER.getValue()) {
            if (!userMapper.existsWithPrimaryKey(config.getId())) {
                throw new IllegalArgumentException("用户不存在");
            }
            query.setUserId(config.getId());
        } else {
            if (!roleMapper.existsWithPrimaryKey(config.getId())) {
                throw new IllegalArgumentException("角色不存在");
            }
            query.setRoleId(config.getId());
        }
        TDetectionConfig configuration = detectionConfigMapper.selectOne(query);
        if (configuration == null) {
            configuration = new TDetectionConfig();
            if (config.getCategory() == DetectionConfigCategoryEnum.USER.getValue()) {
                configuration.setUserId(config.getId());
            } else {
                configuration.setRoleId(config.getId());
            }
            configuration.setCreateTime(new Date());
            setConfig(configuration, config);
            detectionConfigMapper.save(configuration);
            saveTerminalList(configuration.getId(), config.getDetectableTerminals());
        } else {
            setConfig(configuration, config);
            updateTerminalList(configuration.getId(), config.getDetectableTerminals());
            detectionConfigMapper.updateByPrimaryKey(configuration);
        }

        //刷新数据
        if(StringUtils.isNotBlank(config.getDynamicJobIds()) || StringUtils.isNotBlank(config.getStaticJobIds())) {
        	xxlDetectWebServer.refreshDetectionConfig();
        }
    }

    @Transactional
    @Override
    public void delete(Long userId, DeleteDetectionConfig config) {
        TDetectionConfig query = new TDetectionConfig();
        if (config.getCategory() == DetectionConfigCategoryEnum.USER.getValue()) {
            query.setUserId(config.getId());
        } else {
            query.setRoleId(config.getId());
        }
        List<TDetectionConfig> configList = detectionConfigMapper.select(query);
        for (TDetectionConfig deleteConfig : configList) {
            detectionConfigMapper.deleteByPrimaryKey(deleteConfig.getId());
            Example example = new Example(TDetectableTerminal.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("configId", deleteConfig.getId());
            detectableTerminalMapper.deleteByExample(example);
        }
    }

    @Override
    public TaskConditionCheckResult preTaskConditionCheck(Long userId, List<Long> assetsIds) {
        List<String> packageList = new ArrayList<>();
        TerminalTypeEnum terminalTypeEnum = null;
        for (Long assetsId : assetsIds) {
            TAssets assets = assetsMapper.selectByPrimaryKey(assetsId);
            if (Objects.isNull(assets)) {
                throw new IjiamiRuntimeException("资产id错误");
            }
            if (assets.getTerminalType() == TerminalTypeEnum.WECHAT_APPLET) {
                packageList.add(assets.getAppId());
            } else if (assets.getTerminalType() == TerminalTypeEnum.ALIPAY_APPLET) {
                packageList.add(assets.getName());
            } else {
                packageList.add(assets.getPakage());
            }
            terminalTypeEnum = assets.getTerminalType();
        }
        if (terminalTypeEnum == null) {
            throw new IjiamiRuntimeException("资产id错误");
        }
        List<Role> roleList = userRoleMapper.selectByUserId(userId);
        TerminalTypeEnum finalTerminalTypeEnum = terminalTypeEnum;
        // 先检查角色的限制
        List<TaskConditionCheckResult> roleCheckList = roleList.stream().filter(role -> role.getStatus() == StatusEnum.ACTIVATION)
                .map(role -> judgmentRoleLimits(userId, role.getRoleId(), finalTerminalTypeEnum, packageList))
                .collect(Collectors.toList());
        if (roleCheckList.isEmpty()) {
            return buildNoneResult();
        }
        if (roleCheckList.stream().noneMatch(TaskConditionCheckResult::isAllowed)) {
            return roleCheckList.get(0);
        }
        // 检查用户的限制
        return judgmentUserLimits(userId, finalTerminalTypeEnum, packageList);
    }

    @Override
    public TaskConditionCheckResult userTaskConditionCheck(Long userId, TerminalTypeEnum terminalTypeEnum) {
        if (terminalTypeEnum == null) {
            throw new IjiamiRuntimeException("平台类型错误");
        }
        List<Role> roleList = userRoleMapper.selectByUserId(userId);
        // 先检查角色的限制
        List<TaskConditionCheckResult> roleCheckList = roleList.stream().filter(role -> role.getStatus() == StatusEnum.ACTIVATION)
                .map(role -> judgmentRoleLimits(userId, role.getRoleId(), terminalTypeEnum, Collections.emptyList()))
                .collect(Collectors.toList());
        if (roleCheckList.isEmpty()) {
            return buildNoneResult();
        }
        if (roleCheckList.stream().noneMatch(TaskConditionCheckResult::isAllowed)) {
            return roleCheckList.get(0);
        }
        // 检查用户的限制
        return judgmentUserLimits(userId, terminalTypeEnum, Collections.emptyList());
    }

    private TaskConditionCheckResult judgmentRoleLimits(Long userId, Long roleId, TerminalTypeEnum terminalTypeEnum, List<String> packageList) {
        TDetectionConfig configuration = findRoleConfig(roleId);
        if (Objects.isNull(configuration)) {
            return buildNoneResult();
        }
        TDetectableTerminal terminalQuery = new TDetectableTerminal();
        terminalQuery.setConfigId(configuration.getId());
        terminalQuery.setTerminalType(terminalTypeEnum.getValue());
        TDetectableTerminal terminal = detectableTerminalMapper.selectOne(terminalQuery);
        return judgmentLimits(terminal, userId, packageList);
    }

    private TaskConditionCheckResult judgmentUserLimits(Long userId, TerminalTypeEnum terminalTypeEnum, List<String> packageList) {
        TDetectionConfig configuration = findUserConfig(userId);
        if (Objects.isNull(configuration)) {
            return buildNoneResult();
        }
        User user = userMapper.selectByPrimaryKey(userId);
        if (Objects.isNull(user) || isUserLocked(user) || isAccountExpired(user)) {
            return buildAccountExpiredResult();
        }
        TDetectableTerminal terminalQuery = new TDetectableTerminal();
        terminalQuery.setConfigId(configuration.getId());
        terminalQuery.setTerminalType(terminalTypeEnum.getValue());
        TDetectableTerminal terminal = detectableTerminalMapper.selectOne(terminalQuery);
        return judgmentLimits(terminal, userId, packageList);
    }

    private boolean isUserLocked(User user) {
        Date lockedTime = user.getLockedTime();
        if (lockedTime != null) {
            Date date = new Date();
            long diffs = (date.getTime() - lockedTime.getTime()) / (1000 * 60);// 这样得到的差值是分钟级别
            return diffs < 10;
        }
        return false;
    }

    private boolean isAccountExpired(User user) {
        boolean isEffect = false;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        int now = Integer.parseInt(sdf.format(new Date()));
        int start = 0;
        int end = ********;
        if (user.getValidStartDate() != null) {
            start = Integer.parseInt(sdf.format(user.getValidStartDate()));
        }
        if (user.getValidEndDate() != null) {
            end = Integer.parseInt(sdf.format(user.getValidEndDate()));
        }
        if (now >= start && now <= end) {
            isEffect = true;
        }
        return !isEffect;
    }

    private TaskConditionCheckResult buildNoneResult() {
        TaskConditionCheckResult result = new TaskConditionCheckResult();
        result.setAllowed(false);
        result.setTipsText("无法进行该类型检测，未采购或已过期，请联系商务经理咨询。");
        return result;
    }

    private TaskConditionCheckResult buildAccountExpiredResult() {
        TaskConditionCheckResult result = new TaskConditionCheckResult();
        result.setAllowed(false);
        result.setTipsText("您的账号已到期，请联系商务经理或售后等工作人员处理。");
        return result;
    }

    private TaskConditionCheckResult buildExpiredResult() {
        TaskConditionCheckResult result = new TaskConditionCheckResult();
        result.setAllowed(false);
        result.setTipsText("您的检测次数剩余0次，或已到期，请联系商务经理或售后等工作人员处理。");
        return result;
    }

    private TaskConditionCheckResult buildAllowResult() {
        TaskConditionCheckResult result = new TaskConditionCheckResult();
        result.setAllowed(true);
        result.setTipsText("");
        return result;
    }

    private TaskConditionCheckResult judgmentLimits(TDetectableTerminal terminal, Long userId, List<String> packageList) {
        if (Objects.isNull(terminal) || !terminal.getEnabled()) {
            return buildNoneResult();
        }
        int usedCount = taskCountByUser(userId, terminal);
        int detectionLimit = Objects.isNull(terminal.getDetectionLimit()) ? Integer.MAX_VALUE : terminal.getDetectionLimit();
        int residuals = Math.max(detectionLimit - usedCount, 0);
        if (residuals == 0
                || isTerminalTypeExpired(terminal)) {
            return buildExpiredResult();
        }
        TaskConditionCheckResult result = new TaskConditionCheckResult();
        // 如果没有传应用包名，默认非批量检测，消耗一个检测次数
        int consumeQuota = packageList.isEmpty() ? 1 : packageList.size();
        // 检查是否有足够的检测次数
        if (consumeQuota > residuals) {
            result.setAllowed(false);
            result.setTipsText(String.format("您的检测次数剩余%d次，已不足%d次，不足以提交批量测试，烦请分次提交或批量提交可用次数内的检测，请联系商务经理或售后等工作人员处理。",
                    residuals, packageList.size()));
        } else if (packageList.stream().anyMatch(packageName -> !validPackageName(terminal, packageName))) {
            result.setAllowed(false);
            result.setTipsText("您提交的应用包名或APPID错误，请重新上传资产，如需新增检测范围请联系商务经理或售后等工作人员处理。");
        } else if (isTips(PinfoConstant.CACHE_DETECTION_50_PERCENT_TIPS, userId) && detectionLimit > 0 && residuals / (float) detectionLimit < 0.5) {
            cacheService.set(PinfoConstant.CACHE_DETECTION_50_PERCENT_TIPS + userId, StringUtils.EMPTY, todaySecondsRemaining(), TimeUnit.SECONDS);
            result.setAllowed(true);
            result.setTipsText(String.format("剩余检测任务次数/检测任务次数：%d/%d", residuals, terminal.getDetectionLimit()));
        } else if (isTips(PinfoConstant.CACHE_DETECTION_10_PERCENT_TIPS, userId) && detectionLimit > 0 && residuals / (float) detectionLimit < 0.1) {
            cacheService.set(PinfoConstant.CACHE_DETECTION_10_PERCENT_TIPS + userId, StringUtils.EMPTY, todaySecondsRemaining(), TimeUnit.SECONDS);
            result.setAllowed(true);
            result.setTipsText(String.format("剩余检测任务次数/检测任务次数：%d/%d", residuals, detectionLimit));
        } else if (isTips(PinfoConstant.CACHE_DETECTION_100_NUM_TIPS, userId) && residuals < 100) {
            cacheService.set(PinfoConstant.CACHE_DETECTION_100_NUM_TIPS + userId, StringUtils.EMPTY, todaySecondsRemaining(), TimeUnit.SECONDS);
            result.setAllowed(true);
            result.setTipsText(String.format("您的检测次数剩余%d次，已不足%d次，请联系商务经理或售后等工作人员处理。", residuals, 100));
        } else if (isTips(PinfoConstant.CACHE_DETECTION_10_NUM_TIPS, userId) && residuals < 10) {
            cacheService.set(PinfoConstant.CACHE_DETECTION_10_NUM_TIPS + userId, StringUtils.EMPTY, todaySecondsRemaining(), TimeUnit.SECONDS);
            result.setAllowed(true);
            result.setTipsText(String.format("您的检测次数剩余%d次，已不足%d次，请联系商务经理或售后等工作人员处理。", residuals, 10));
        } else {
            result.setAllowed(true);
            result.setTipsText("");
        }
        return result;
    }

    private boolean isTerminalTypeExpired(TDetectableTerminal terminal) {
        return (terminal.getStartDate() != null && terminal.getStartDate().getTime() > System.currentTimeMillis())
                || (terminal.getEndDate() != null && terminal.getEndDate().getTime() < System.currentTimeMillis());
    }

    private boolean isTips(String keyPrefix, Long userId) {
        return Objects.isNull(cacheService.get(keyPrefix + userId));
    }

    private boolean validPackageName(TDetectableTerminal configTerminal, String packageName) {
        return StringUtils.isBlank(configTerminal.getAppPackageLimit())
                || StringUtils.contains(configTerminal.getAppPackageLimit(), packageName);
    }

    private static Long todaySecondsRemaining() {
        // 获取当前日期和时间
        LocalDateTime now = LocalDateTime.now();
        // 获取当天的最后一秒时间
        LocalDateTime endOfDay = now.with(LocalTime.MAX);
        // 计算当前时间到当天最后一秒的时间差
        return now.until(endOfDay, ChronoUnit.SECONDS);
    }

    private void setConfig(TDetectionConfig configuration, DetectionConfigVO config) {
        configuration.setAndroidDevicesLimit(config.getAndroidDevicesLimit());
        configuration.setIosDevicesLimit(config.getIosDevicesLimit());
        configuration.setDescription(config.getDescription());
        configuration.setUpdateTime(new Date());
        configuration.setStaticJobIds(config.getStaticJobIds());
        configuration.setDynamicJobIds(config.getDynamicJobIds());
        configuration.setAndroidDeviceIps(config.getAndroidDeviceIps());
        configuration.setHarmonyDevicesLimit(config.getHarmonyDevicesLimit());
        configuration.setAiUsageLimit(config.getAiUsageLimit());
        configuration.setAiUsageLimitType(AiUsageLimitTypeEnum.getItem(config.getAiUsageLimitType()));
    }

    private void saveTerminalList(Long configId, List<DetectableTerminalVO> terminalVOList) {
        terminalVOList.forEach(terminalVO -> {
            TDetectableTerminal insert = buildTerminal(configId, terminalVO);
            detectableTerminalMapper.insert(insert);
        });
    }

    private void updateTerminalList(Long configId, List<DetectableTerminalVO> terminalVOList) {
        TDetectableTerminal query = new TDetectableTerminal();
        query.setConfigId(configId);
        List<TDetectableTerminal> terminalList = detectableTerminalMapper.select(query);
        List<TDetectableTerminal> deleteList = terminalList.stream()
                .filter(terminal -> !inTerminalVOList(terminalVOList, terminal)).collect(Collectors.toList());
        deleteList.forEach(delete -> {
            detectableTerminalMapper.delete(delete);
        });
        List<TDetectableTerminal> insertList = terminalVOList.stream()
                .filter(terminalVO -> !inTerminalDbList(terminalList, terminalVO))
                .map(terminalVO -> buildTerminal(configId, terminalVO))
                .collect(Collectors.toList());
        insertList.forEach(insert -> {
            detectableTerminalMapper.insert(insert);
        });
        List<TDetectableTerminal> updateList = terminalVOList.stream()
                .map(terminalVO -> updaterTerminal(terminalList, terminalVO))
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
        updateList.forEach(update -> {
            detectableTerminalMapper.updateByPrimaryKey(update);
        });
    }

    private boolean inTerminalVOList(List<DetectableTerminalVO> terminalVOList, TDetectableTerminal terminal) {
        return terminalVOList.stream().anyMatch(terminalVO -> terminalVO.getTerminalType().equals(terminal.getTerminalType()));
    }

    private boolean inTerminalDbList(List<TDetectableTerminal> terminalList, DetectableTerminalVO terminalVO) {
        return terminalList.stream().anyMatch(terminal -> terminal.getTerminalType().equals(terminalVO.getTerminalType()));
    }

    private Optional<TDetectableTerminal> updaterTerminal(List<TDetectableTerminal> terminalList, DetectableTerminalVO terminalVO) {
        Optional<TDetectableTerminal> terminalOptional = terminalList.stream()
                .filter(terminal -> terminal.getTerminalType().equals(terminalVO.getTerminalType())).findFirst();
        if (terminalOptional.isPresent()) {
            setTerminal(terminalOptional.get(), terminalVO);
            return terminalOptional;
        } else {
            return Optional.empty();
        }
    }

    private TDetectableTerminal buildTerminal(Long configId, DetectableTerminalVO terminalVO) {
        TDetectableTerminal insert = new TDetectableTerminal();
        insert.setConfigId(configId);
        setTerminal(insert, terminalVO);
        return insert;
    }

    private void setTerminal(TDetectableTerminal terminal, DetectableTerminalVO terminalVO) {
        terminal.setTerminalType(terminalVO.getTerminalType());
        if (Objects.nonNull(terminalVO.getStartDate())
                && Objects.nonNull(terminalVO.getEndDate())
                && terminalVO.getStartDate().getTime() >= terminalVO.getEndDate().getTime()) {
            throw new IllegalArgumentException("开始时间不能大于结束时间");
        }
        if (StringUtils.isNotBlank(terminalVO.getAppPackageLimit())
                && !Pattern.compile("^[a-zA-Z.,\\-0-9]+$").matcher(terminalVO.getAppPackageLimit()).matches()) {
            throw new IllegalArgumentException("包名填写错误");
        }
        terminal.setDetectionLimit(terminalVO.getDetectionLimit());
        terminal.setStartDate(terminalVO.getStartDate());
        terminal.setEndDate(terminalVO.getEndDate());
        terminal.setAppPackageLimit(terminalVO.getAppPackageLimit());
        if (Objects.nonNull(terminalVO.getEnabled())) {
            terminal.setEnabled(terminalVO.getEnabled());
        }
    }

    @Override
    public PageInfo<PageDetectionConfigVO> findListByPage(DetectionPageQuery query) {
        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        PageInfo<DetectionConfigItem> pageInfo = new PageInfo<>(detectionConfigMapper.findList(query.getName(), query.getSortType(), query.getSortOrder()));
        PageInfo<PageDetectionConfigVO> configVOPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, configVOPageInfo);
        List<Long> userIds = pageInfo.getList().stream().map(DetectionConfigItem::getUserId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<Long, List<Role>> roleMap = userIds.isEmpty() ? Collections.emptyMap() : findUserRoles(userIds);
        List<TDetectionConfig> roleConfigList;
        if (!roleMap.isEmpty()) {
            Example example = new Example(TDetectionConfig.class);
            Example.Criteria criteria = example.createCriteria();
            Collection<Long> roleList = roleMap.values().stream().flatMap(Collection::stream).map(Role::getRoleId).collect(Collectors.toSet());
            criteria.andIn("roleId", roleList);
            roleConfigList = detectionConfigMapper.selectByExample(example);
        } else {
            roleConfigList = Collections.emptyList();
        }
        configVOPageInfo.setList(pageInfo.getList().stream().map(item -> buildVO(item, roleMap, roleConfigList)).collect(Collectors.toList()));
        return configVOPageInfo;
    }

    @Override
    public List<TDetectionConfig> findUserRoleConfig(Long userId) {
        return userRoleMapper.selectByUserId(userId)
                .stream()
                .map(role -> findRoleConfig(role.getRoleId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Override
    public TDetectionConfig findRoleConfig(Long roleId) {
        TDetectionConfig query = new TDetectionConfig();
        query.setRoleId(roleId);
        return detectionConfigMapper.selectOne(query);
    }

    @Override
    public TDetectionConfig findUserConfig(Long userId) {
        TDetectionConfig query = new TDetectionConfig();
        query.setUserId(userId);
        return detectionConfigMapper.selectOne(query);
    }

    @Override
    public List<RoleUserResultVO> findRoleUser(RoleUserQuery query) {
        return roleWithUserMapper.findByName(query.getName());
    }

    private Map<Long, List<Role>> findUserRoles(List<Long> userIds) {
        Map<Long, List<Role>> map = new HashMap<>();
        for (Long userId : userIds) {
            map.put(userId, userRoleMapper.selectByUserId(userId));
        }
        return map;
    }

    private PageDetectionConfigVO buildVO(DetectionConfigItem item, Map<Long, List<Role>> roleMap, Collection<TDetectionConfig> roleConfigList) {
        PageDetectionConfigVO configVO = new PageDetectionConfigVO();
        BeanUtils.copyProperties(item, configVO);
        TDetectableTerminal terminalQuery = new TDetectableTerminal();
        terminalQuery.setConfigId(item.getId());
        List<DetectableTerminalVO> detectableTerminals = detectableTerminalMapper.select(terminalQuery)
                .stream().map(terminal -> buildTerminalVO(item, terminal)).collect(Collectors.toList());
        configVO.setDetectableTerminals(detectableTerminals);
        if (Objects.nonNull(item.getRoleId())) {
            configVO.setId(item.getRoleId());
            configVO.setRoleName(item.getRoleName());
            configVO.setCategory(DetectionConfigCategoryEnum.ROLE.getValue());
        } else {
            configVO.setId(item.getUserId());
            configVO.setUserName(item.getUserName());
            configVO.setCategory(DetectionConfigCategoryEnum.USER.getValue());
            // 用户的需要去找到他的角色过期时间
            List<Role> roles = roleMap.get(item.getUserId());
            if (CollectionUtils.isNotEmpty(roles)) {
                configVO.setRoleName(roles.stream().map(Role::getRoleName).collect(Collectors.joining(",")));
            }
        }
        return configVO;
    }

    private DetectableTerminalVO buildTerminalVO(DetectionConfigItem configItem, TDetectableTerminal terminal) {

        DetectableTerminalVO terminalVO = new DetectableTerminalVO();
        // 查询用户在这个时间内用了多少量
        if (Objects.nonNull(terminal.getDetectionLimit())) {
            if (Objects.nonNull(configItem.getUserId())) {
                int usedCount = taskCountByUser(configItem.getUserId(), terminal);
                terminalVO.setResidualDetectionLimit(Math.max(0, terminal.getDetectionLimit() - usedCount));
            } else {
                terminalVO.setResidualDetectionLimit(terminal.getDetectionLimit());
            }
        }
        terminalVO.setDetectionLimit(terminal.getDetectionLimit());
        terminalVO.setAppPackageLimit(terminal.getAppPackageLimit());
        terminalVO.setTerminalType(terminal.getTerminalType());
        terminalVO.setStartDate(terminal.getStartDate());
        terminalVO.setEndDate(terminal.getEndDate());
        terminalVO.setEnabled(terminal.getEnabled());
        return terminalVO;
    }

    private int taskCountByUser(Long userId, TDetectableTerminal terminal) {
        Example example = new Example(TUserTaskConsumption.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("userId", userId);
        criteria.andEqualTo("terminalType", terminal.getTerminalType());
        return userTaskConsumptionMapper.selectCountByExample(example);
    }

    @Override
    public void addUserTaskConsumption(TTask task) {
        TUserTaskConsumption consumption = new TUserTaskConsumption();
        consumption.setTaskId(task.getTaskId());
        consumption.setUserId(task.getCreateUserId());
        consumption.setAssetsId(task.getAssetsId());
        consumption.setTerminalType(task.getTerminalType());
        consumption.setDetectionType(task.getDetectionType());
        consumption.setDynamicStatus(task.getDynamicStatus());
        consumption.setDynamicLawStatus(task.getDynamicLawStatus());
        consumption.setTaskTatus(task.getTaskTatus());
        consumption.setCreateTime(new Date());
        consumption.setUpdateTime(new Date());
        userTaskConsumptionMapper.insert(consumption);
    }

    @Override
    public void updateUserTaskConsumptionStatus(TTask task) {
        // 暂时不需要更新
    }
    
    /**
     * 初始化配置文件
     * @return
     */
    @Override
    public Map<Long, TDetectionConfigVO> initDetectionConfigData(){
    	List<TDetectionConfigVO> configList = detectionConfigMapper.findJobIdConfig();
    	Map<Long, TDetectionConfigVO> detectionConfigMap = configList.stream()
        .collect(Collectors.toMap(
                TDetectionConfigVO::getUserId,
                config -> config,
                (existing, replacement) -> existing
        ));
    	return detectionConfigMap;
    }

}