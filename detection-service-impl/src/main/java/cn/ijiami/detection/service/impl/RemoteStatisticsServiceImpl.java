package cn.ijiami.detection.service.impl;

import cn.ijiami.base.common.context.UserContextHolder;
import cn.ijiami.base.common.data.DataAccessInfo;
import cn.ijiami.base.common.data.IDateRuleManager;
import cn.ijiami.base.common.user.IRole;
import cn.ijiami.base.common.user.IUser;
import cn.ijiami.base.common.user.RoleVO;
import cn.ijiami.detection.VO.statistics.AssetsDetection;
import cn.ijiami.detection.VO.statistics.AssetsInfo;
import cn.ijiami.detection.VO.statistics.AssetsStatistics;
import cn.ijiami.detection.VO.statistics.AssetsStatisticsDetail;
import cn.ijiami.detection.VO.statistics.AssetsTask;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesAssetsInfo;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesDetail;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesLawItem;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesReportAssetsInfo;
import cn.ijiami.detection.VO.statistics.DetectFalsePositivesStatistics;
import cn.ijiami.detection.VO.statistics.DetectionStatistics;
import cn.ijiami.detection.VO.statistics.DetectionStatisticsDetail;
import cn.ijiami.detection.VO.statistics.DetectionStatisticsLawItem;
import cn.ijiami.detection.VO.statistics.HomePageDetectionStatistics;
import cn.ijiami.detection.VO.statistics.SdkStatisticsDetail;
import cn.ijiami.detection.android.client.api.AndroidStatisticsServiceApi;
import cn.ijiami.detection.android.client.api.IosStatisticsServiceApi;
import cn.ijiami.detection.enums.PrivacyLawId;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.CustomDepartmentMapper;
import cn.ijiami.detection.query.AssetsDetailsQuery;
import cn.ijiami.detection.query.AssetsInfoQuery;
import cn.ijiami.detection.query.AssetsStatisticsQuery;
import cn.ijiami.detection.query.DetectionDetailsQuery;
import cn.ijiami.detection.query.DetectionLawItemQuery;
import cn.ijiami.detection.query.DetectionStatisticsQuery;
import cn.ijiami.detection.query.HomePageDetectionStatisticsQuery;
import cn.ijiami.detection.query.SdkDetailsQuery;
import cn.ijiami.detection.server.client.base.dto.AssetsDetectionDTO;
import cn.ijiami.detection.server.client.base.dto.AssetsInfoDTO;
import cn.ijiami.detection.server.client.base.dto.AssetsStatisticsDTO;
import cn.ijiami.detection.server.client.base.dto.AssetsStatisticsDetailDTO;
import cn.ijiami.detection.server.client.base.dto.statistics.AssetsTaskDTO;
import cn.ijiami.detection.server.client.base.dto.statistics.DetectFalsePositivesAssetsInfoDTO;
import cn.ijiami.detection.server.client.base.dto.statistics.DetectFalsePositivesDetailDTO;
import cn.ijiami.detection.server.client.base.dto.statistics.DetectFalsePositivesLawItemDTO;
import cn.ijiami.detection.server.client.base.dto.statistics.DetectFalsePositivesReportAssetsInfoDTO;
import cn.ijiami.detection.server.client.base.dto.statistics.DetectFalsePositivesStatisticsDTO;
import cn.ijiami.detection.server.client.base.dto.statistics.DetectionStatisticsDTO;
import cn.ijiami.detection.server.client.base.dto.statistics.DetectionStatisticsDetailDTO;
import cn.ijiami.detection.server.client.base.dto.statistics.DetectionStatisticsLawItemDTO;
import cn.ijiami.detection.server.client.base.dto.statistics.HomePageDetectionStatisticsDTO;
import cn.ijiami.detection.server.client.base.dto.statistics.SdkStatisticsDetailDTO;
import cn.ijiami.detection.server.client.base.param.AssetsDetailsParam;
import cn.ijiami.detection.server.client.base.param.AssetsInfoParam;
import cn.ijiami.detection.server.client.base.param.AssetsStatisticsParam;
import cn.ijiami.detection.server.client.base.param.DetectionStatisticsParam;
import cn.ijiami.detection.server.client.base.param.DetectionDetailsParam;
import cn.ijiami.detection.server.client.base.param.DetectionLawItemParam;
import cn.ijiami.detection.server.client.base.param.HomePageDetectionStatisticsParam;
import cn.ijiami.detection.server.client.base.param.SdkDetailsParam;
import cn.ijiami.detection.service.api.StatisticsService;
import cn.ijiami.framework.common.enums.DataRuleTypeEnum;
import cn.ijiami.manager.role.entity.Role;
import cn.ijiami.manager.user.entity.User;
import cn.ijiami.manager.user.entity.UserInfo;
import cn.ijiami.manager.user.mapper.UserInfoMapper;
import cn.ijiami.manager.user.mapper.UserMapper;
import cn.ijiami.manager.user.query.UserQuery;
import cn.ijiami.manager.user.service.api.IUserRoleService;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName RemoteStatisticsServiceImpl.java
 * @Description 远程任务服务实现类，通过Feign调用远程Android和iOS检测服务实现检测统计功能
 * @createTime 2025年06月16日 18:30:00
 */
@Slf4j
@Service("statisticsService")
public class RemoteStatisticsServiceImpl implements StatisticsService {

    @Autowired
    private AndroidStatisticsServiceApi androidStatisticsServiceApi;

    @Autowired
    private IosStatisticsServiceApi iosStatisticsServiceApi;

    @Autowired
    private IDateRuleManager dateRuleManager;

    @Autowired
    private IUserRoleService userRoleService;

    @Autowired
    private UserInfoMapper userInfoMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private CustomDepartmentMapper departmentMapper;

    private Long searchUser(Long currentUserId, String searchUserName) {
        if (StringUtils.isNotBlank(searchUserName)) {
            UserQuery query = new UserQuery();
            query.setRealName(searchUserName);
            User searchUser = userMapper.selectUser(query);
            if (Objects.isNull(searchUser)) {
                return null;
            }
            if (!checkDataAccess(searchUser.getUserId())) {
                return null;
            }
            return searchUser.getUserId();
        } else {
            return currentUserId;
        }
    }

    private DataAccessInfo buildDataAccessInfo(String moduleCode, Long userId) {
        DataAccessInfo accessInfo = new DataAccessInfo();
        accessInfo.setModuleCode(moduleCode);
        List<Role> roles = userRoleService.findRoleListByUserId(userId);
        if(!CollectionUtils.isEmpty(roles)){
            // 角色集合
            List<IRole> roleVOs = new ArrayList<>();
            RoleVO roleVO = null;
            for (Role role : roles) {
                roleVO = new RoleVO();
                // 拷贝属性
                BeanUtils.copyProperties(role, roleVO);
                roleVOs.add(roleVO);
            }
            accessInfo.setRoles(roleVOs);
        }
        return accessInfo;
    }

    private boolean checkDataAccess(Long targetUserId) {
        IUser currentUser = UserContextHolder.getCurrentUser();
        // 检查是否符合数据范围
        DataAccessInfo dataAccessInfo = buildDataAccessInfo("statistics", currentUser.getUserId());
        DataRuleTypeEnum dataRuleTypeEnum = dateRuleManager.getRuleType(dataAccessInfo);
        if (dataRuleTypeEnum == DataRuleTypeEnum.SELF) {
            return false;
        } else if (dataRuleTypeEnum == DataRuleTypeEnum.DEPARTMENT) {
            List<UserInfo> userInfoList = userInfoMapper.findUserInfoByUserId(targetUserId);
            if (userInfoList.isEmpty()) {
                return false;
            }
            // 是否同部门
            return userInfoList.stream().anyMatch(userInfo -> userInfo.getDepartmentId().equals(currentUser.getDepartmentId()));
        } else if (dataRuleTypeEnum == DataRuleTypeEnum.DEPARTMENT_AND_SUBORDINATE_DEPARTMENTS) {
            List<UserInfo> userInfoList = userInfoMapper.findUserInfoByUserId(targetUserId);
            if (userInfoList.isEmpty()) {
                return false;
            }
            return userInfoList.stream().anyMatch(userInfo -> isSubDepartments(userInfo.getDepartmentId(), targetUserId));
        } else return dataRuleTypeEnum == DataRuleTypeEnum.ALL;
    }

    private Boolean isSubDepartments(Long departmentId, Long targetUserId) {
        return Objects.nonNull(departmentMapper.isSubDepartmentUserId(departmentId, targetUserId));
    }

    private AssetsStatistics emptyAssetsStatistics(AssetsStatisticsQuery query) {
        AssetsStatistics statistics = new AssetsStatistics();
        statistics.setIncrementThisWeek(0);
        statistics.setIncrementThisMonth(0);
        statistics.setIncrementThisYear(0);
        statistics.setTerminalType(query.getTerminalType());
        // 资产总数
        statistics.setTotalCount(0);
        return statistics;
    }

    @Override
    public AssetsStatistics assetsStatistics(Long userId, AssetsStatisticsQuery query) {
        AssetsStatisticsParam param = new AssetsStatisticsParam();
        Long searchUserId = searchUser(userId, query.getUserName());
        if (Objects.isNull(searchUserId)) {
            return emptyAssetsStatistics(query);
        }
        param.setUserId(searchUserId);
        BeanUtils.copyProperties(query, param);
        
        try {
            if (query.getTerminalType() == TerminalTypeEnum.ANDROID.getValue()) {
                return convert(androidStatisticsServiceApi.assetsStatistics(param));
            } else if (query.getTerminalType() == TerminalTypeEnum.IOS.getValue()) {
                return convert(iosStatisticsServiceApi.assetsStatistics(param));
            }
        } catch (Exception e) {
            log.error("调用远程资产统计服务失败, userId: {}, terminalType: {}", userId, query.getTerminalType(), e);
        }
        return null;
    }

    @Override
    public List<AssetsDetection> detectionTopList(Long userId, AssetsStatisticsQuery query) {
        Long searchUserId = searchUser(userId, query.getUserName());
        if (Objects.isNull(searchUserId)) {
            return Collections.emptyList();
        }
        AssetsStatisticsParam param = new AssetsStatisticsParam();
        BeanUtils.copyProperties(query, param);
        param.setUserId(searchUserId);
        try {
            if (query.getTerminalType() == TerminalTypeEnum.ANDROID.getValue()) {
                return convertAssetsDetectionDtoList(androidStatisticsServiceApi.detectionTopList(param));
            } else if (query.getTerminalType() == TerminalTypeEnum.IOS.getValue()) {
                return convertAssetsDetectionDtoList(iosStatisticsServiceApi.detectionTopList(param));
            }
        } catch (Exception e) {
            log.error("调用远程检测排行榜服务失败, userId: {}, terminalType: {}", userId, query.getTerminalType(), e);
        }
        return Collections.emptyList();
    }

    @Override
    public PageInfo<AssetsStatisticsDetail> assetsDetailsByPage(Long userId, AssetsDetailsQuery query) {
        try {
            Long searchUserId = searchUser(userId, query.getUserName());
            AssetsDetailsParam param = new AssetsDetailsParam();
            BeanUtils.copyProperties(query, param);
            param.setUserId(searchUserId);
            if (query.getTerminalType() == TerminalTypeEnum.ANDROID.getValue()) {
                return convertAssetsDetailsByPage(androidStatisticsServiceApi.assetsDetailsByPage(param));
            } else if (query.getTerminalType() == TerminalTypeEnum.IOS.getValue()) {
                return convertAssetsDetailsByPage(iosStatisticsServiceApi.assetsDetailsByPage(param));
            }
            return PageInfo.emptyPageInfo();
        } catch (Exception e) {
            log.error("调用远程资产详情分页服务失败, userId: {}, terminalType: {}", userId, query.getTerminalType(), e);
        }
        return PageInfo.emptyPageInfo();
    }

    /**
     * 转换List<AssetsDetectionDTO>到List<AssetsDetection>
     */
    private static PageInfo<AssetsStatisticsDetail> convertAssetsDetailsByPage(PageInfo<AssetsStatisticsDetailDTO> pageInfo) {
       PageInfo<AssetsStatisticsDetail> pageInfo2 = new PageInfo<>();
       BeanUtils.copyProperties(pageInfo, pageInfo2);
       pageInfo2.setList(pageInfo.getList().stream().map(item -> {
           AssetsStatisticsDetail detail = new AssetsStatisticsDetail();
           BeanUtils.copyProperties(item, detail);
           detail.setVersionList(convertVersionList(item.getVersionList()));
           return detail;
       }).collect(Collectors.toList()));
       return pageInfo2;
    }

    private static List<AssetsStatisticsDetail> convertVersionList(List<AssetsStatisticsDetailDTO> versionList) {
        if (versionList == null) {
            return null;
        }
        return versionList.stream().map(item -> {
            AssetsStatisticsDetail detail = new AssetsStatisticsDetail();
            BeanUtils.copyProperties(item, detail);
            return detail;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AssetsStatisticsDetail> assetsDetailsAll(Long userId, AssetsStatisticsQuery query) {
        Long searchUserId = searchUser(userId, query.getUserName());
        AssetsStatisticsParam param = new AssetsStatisticsParam();
        BeanUtils.copyProperties(query, param);
        param.setUserId(searchUserId);
        try {
            if (query.getTerminalType() == TerminalTypeEnum.ANDROID.getValue()) {
                return convertAssetsStatisticsDetailDtoList(androidStatisticsServiceApi.assetsDetailsAll(param));
            } else if (query.getTerminalType() == TerminalTypeEnum.IOS.getValue()) {
                return convertAssetsStatisticsDetailDtoList(iosStatisticsServiceApi.assetsDetailsAll(param));
            }
        } catch (Exception e) {
            log.error("调用远程所有资产详情服务失败, userId: {}, terminalType: {}", userId, query.getTerminalType(), e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AssetsTask> assetsTaskAll(Long userId, AssetsStatisticsQuery query) {
        AssetsStatisticsParam param = new AssetsStatisticsParam();
        param.setUserId(userId);
        BeanUtils.copyProperties(query, param);

        try {
            if (query.getTerminalType() == TerminalTypeEnum.ANDROID.getValue()) {
                return convertAssetsTaskDtoList(androidStatisticsServiceApi.assetsTaskAll(param));
            } else if (query.getTerminalType() == TerminalTypeEnum.IOS.getValue()) {
                return convertAssetsTaskDtoList(iosStatisticsServiceApi.assetsTaskAll(param));
            }
        } catch (Exception e) {
            log.error("调用远程资产任务列表服务失败, userId: {}, terminalType: {}", userId, query.getTerminalType(), e);
        }
        return Collections.emptyList();
    }

    @Override
    public DetectionStatistics detectionStatistics(Long userId, DetectionStatisticsQuery query) {
        DetectionStatisticsParam param = new DetectionStatisticsParam();
        User user = userMapper.selectByPrimaryKey(userId);
        param.setUserName(user.getUserName());
        BeanUtils.copyProperties(query, param);

        try {
            if (query.getTerminalType() == TerminalTypeEnum.ANDROID.getValue()) {
                return convertDetectionStatisticsDto(androidStatisticsServiceApi.detectionStatistics(param));
            } else if (query.getTerminalType() == TerminalTypeEnum.IOS.getValue()) {
                return convertDetectionStatisticsDto(iosStatisticsServiceApi.detectionStatistics(param));
            }
        } catch (Exception e) {
            log.error("调用远程检测统计服务失败, userId: {}, terminalType: {}", userId, query.getTerminalType(), e);
        }
        return null;
    }

    private static TerminalTypeEnum getTerminalTypeByLawId(Integer lawId) {
        if (lawId == null) {
            return null;
        }
        if (PrivacyLawId.allAndroidLaws().contains(PrivacyLawId.getItem(lawId))) {
            return TerminalTypeEnum.ANDROID;
        } else if (PrivacyLawId.allIOSLaws().contains(PrivacyLawId.getItem(lawId))) {
            return TerminalTypeEnum.IOS;
        } else if (PrivacyLawId.allWechatAppletLaws().contains(PrivacyLawId.getItem(lawId))) {
            return TerminalTypeEnum.WECHAT_APPLET;
        } else if (PrivacyLawId.allAlipayAppletLaws().contains(PrivacyLawId.getItem(lawId))) {
            return TerminalTypeEnum.ALIPAY_APPLET;
        } else if (PrivacyLawId.allHarmonyLaws().contains(PrivacyLawId.getItem(lawId))) {
            return TerminalTypeEnum.HARMONY;
        }
        return null;
    }

    @Override
    public PageInfo<DetectionStatisticsDetail> detectionDetailsByPage(Long userId, DetectionDetailsQuery query) {
        TerminalTypeEnum terminalTypeEnum = getTerminalTypeByLawId(query.getLawId());
        if (terminalTypeEnum == null) {
            return PageInfo.emptyPageInfo();
        }

        DetectionDetailsParam param = new DetectionDetailsParam();
        param.setUserId(userId);
        BeanUtils.copyProperties(query, param);

        try {
            if (terminalTypeEnum == TerminalTypeEnum.ANDROID) {
                return convertDetectionStatisticsDetailPageInfo(androidStatisticsServiceApi.detectionDetailsByPage(param));
            } else if (terminalTypeEnum == TerminalTypeEnum.IOS) {
                return convertDetectionStatisticsDetailPageInfo(iosStatisticsServiceApi.detectionDetailsByPage(param));
            }
        } catch (Exception e) {
            log.error("调用远程检测详情分页服务失败, userId: {}, terminalType: {}", userId, terminalTypeEnum, e);
        }
        return PageInfo.emptyPageInfo();
    }

    @Override
    public DetectionStatisticsLawItem detectionLawItem(Long userId, DetectionLawItemQuery query, Integer pageSize) {
        TerminalTypeEnum terminalTypeEnum = TerminalTypeEnum.getItem(query.getTerminalType());
        if (terminalTypeEnum == null) {
            return null;
        }

        DetectionLawItemParam param = new DetectionLawItemParam();
        User user = userMapper.selectByPrimaryKey(userId);
        BeanUtils.copyProperties(query, param);
        param.setUserName(user.getUserName());
        try {
            if (terminalTypeEnum == TerminalTypeEnum.ANDROID) {
                return convertDetectionStatisticsLawItemDto(androidStatisticsServiceApi.detectionLawItem(param, pageSize));
            } else if (terminalTypeEnum == TerminalTypeEnum.IOS) {
                return convertDetectionStatisticsLawItemDto(iosStatisticsServiceApi.detectionLawItem(param, pageSize));
            }
        } catch (Exception e) {
            log.error("调用远程检测法规条目服务失败, userId: {}, terminalType: {}", userId, query.getTerminalType(), e);
        }
        return null;
    }

    @Override
    public PageInfo<AssetsInfo> detectionLawItemAssetsByPage(Long userId, AssetsInfoQuery query) {
        AssetsInfoParam param = new AssetsInfoParam();
        param.setUserName(query.getUserName());
        BeanUtils.copyProperties(query, param);

        try {
            // 根据法规条目ID判断终端类型
            TerminalTypeEnum terminalType = getTerminalTypeByLawChildItemId(query.getLawChildItemId());
            if (terminalType == TerminalTypeEnum.ANDROID) {
                return convertAssetsInfoPageInfo(androidStatisticsServiceApi.detectionLawItemAssetsByPage(param));
            } else if (terminalType == TerminalTypeEnum.IOS) {
                return convertAssetsInfoPageInfo(iosStatisticsServiceApi.detectionLawItemAssetsByPage(param));
            }
        } catch (Exception e) {
            log.error("调用远程检测法规条目资产分页服务失败, userId: {}, lawChildItemId: {}", userId, query.getLawChildItemId(), e);
        }
        return PageInfo.emptyPageInfo();
    }

    @Override
    public PageInfo<SdkStatisticsDetail> sdkDetailsByPage(Long userId, SdkDetailsQuery query) {
        TerminalTypeEnum terminalType = TerminalTypeEnum.getItem(query.getTerminalType());
        if (terminalType == null) {
            return PageInfo.emptyPageInfo();
        }

        SdkDetailsParam param = new SdkDetailsParam();
        param.setUserName(query.getUserName());
        BeanUtils.copyProperties(query, param);

        try {
            if (terminalType == TerminalTypeEnum.ANDROID) {
                return convertSdkStatisticsDetailPageInfo(androidStatisticsServiceApi.sdkDetailsByPage(param));
            } else if (terminalType == TerminalTypeEnum.IOS) {
                return convertSdkStatisticsDetailPageInfo(iosStatisticsServiceApi.sdkDetailsByPage(param));
            }
        } catch (Exception e) {
            log.error("调用远程SDK详情分页服务失败, userId: {}, terminalType: {}", userId, query.getTerminalType(), e);
        }
        return PageInfo.emptyPageInfo();
    }

    @Override
    public DetectFalsePositivesStatistics detectFalsePositivesStatistics(Long userId, DetectionStatisticsQuery query) {
        TerminalTypeEnum terminalType = TerminalTypeEnum.getItem(query.getTerminalType());
        if (terminalType == null) {
            return null;
        }

        DetectionStatisticsParam param = new DetectionStatisticsParam();
        param.setUserName(query.getUserName());
        BeanUtils.copyProperties(query, param);

        try {
            if (terminalType == TerminalTypeEnum.ANDROID) {
                return convertDetectFalsePositivesStatisticsDto(androidStatisticsServiceApi.detectFalsePositivesStatistics(param));
            } else if (terminalType == TerminalTypeEnum.IOS) {
                return convertDetectFalsePositivesStatisticsDto(iosStatisticsServiceApi.detectFalsePositivesStatistics(param));
            }
        } catch (Exception e) {
            log.error("调用远程误报统计服务失败, userId: {}, terminalType: {}", userId, query.getTerminalType(), e);
        }
        return null;
    }

    @Override
    public PageInfo<DetectFalsePositivesDetail> detectFalsePositivesByPage(Long userId, DetectionDetailsQuery query) {
        TerminalTypeEnum terminalTypeEnum = getTerminalTypeByLawId(query.getLawId());
        if (terminalTypeEnum == null) {
            return PageInfo.emptyPageInfo();
        }

        DetectionDetailsParam param = new DetectionDetailsParam();
        param.setUserId(userId);
        BeanUtils.copyProperties(query, param);

        try {
            if (terminalTypeEnum == TerminalTypeEnum.ANDROID) {
                return convertDetectFalsePositivesDetailPageInfo(androidStatisticsServiceApi.detectFalsePositivesByPage(param));
            } else if (terminalTypeEnum == TerminalTypeEnum.IOS) {
                return convertDetectFalsePositivesDetailPageInfo(iosStatisticsServiceApi.detectFalsePositivesByPage(param));
            }
        } catch (Exception e) {
            log.error("调用远程误报分页服务失败, userId: {}, terminalType: {}", userId, terminalTypeEnum, e);
        }
        return PageInfo.emptyPageInfo();
    }

    @Override
    public DetectFalsePositivesLawItem detectFalsePositivesLawItem(Long userId, DetectionLawItemQuery query, Integer pageSize) {
        TerminalTypeEnum terminalTypeEnum = TerminalTypeEnum.getItem(query.getTerminalType());
        if (terminalTypeEnum == null) {
            return null;
        }

        DetectionLawItemParam param = new DetectionLawItemParam();
        BeanUtils.copyProperties(query, param);
        param.setUserId(userId);
        try {
            if (terminalTypeEnum == TerminalTypeEnum.ANDROID) {
                return convertDetectFalsePositivesLawItemDto(androidStatisticsServiceApi.detectFalsePositivesLawItem(param, pageSize));
            } else if (terminalTypeEnum == TerminalTypeEnum.IOS) {
                return convertDetectFalsePositivesLawItemDto(iosStatisticsServiceApi.detectFalsePositivesLawItem(param, pageSize));
            }
        } catch (Exception e) {
            log.error("调用远程误报法规条目服务失败, userId: {}, terminalType: {}", userId, query.getTerminalType(), e);
        }
        return null;
    }

    @Override
    public PageInfo<DetectFalsePositivesAssetsInfo> detectFalsePositivesLawItemAssetsByPage(Long userId, AssetsInfoQuery query) {
        AssetsInfoParam param = new AssetsInfoParam();
        param.setUserId(userId);
        BeanUtils.copyProperties(query, param);

        try {
            // 根据法规条目ID判断终端类型
            TerminalTypeEnum terminalType = getTerminalTypeByLawChildItemId(query.getLawChildItemId());
            if (terminalType == TerminalTypeEnum.ANDROID) {
                return convertDetectFalsePositivesAssetsInfoPageInfo(androidStatisticsServiceApi.detectFalsePositivesLawItemAssetsByPage(param));
            } else if (terminalType == TerminalTypeEnum.IOS) {
                return convertDetectFalsePositivesAssetsInfoPageInfo(iosStatisticsServiceApi.detectFalsePositivesLawItemAssetsByPage(param));
            }
        } catch (Exception e) {
            log.error("调用远程误报法规条目资产分页服务失败, userId: {}, lawChildItemId: {}", userId, query.getLawChildItemId(), e);
        }
        return PageInfo.emptyPageInfo();
    }

    @Override
    public List<DetectFalsePositivesReportAssetsInfo> findDetectFalsePositivesReport(Long userId, DetectionStatisticsQuery query) {
        TerminalTypeEnum terminalType = TerminalTypeEnum.getItem(query.getTerminalType());
        if (terminalType == null) {
            return Collections.emptyList();
        }

        DetectionStatisticsParam param = new DetectionStatisticsParam();
        param.setUserId(userId);
        BeanUtils.copyProperties(query, param);

        try {
            if (terminalType == TerminalTypeEnum.ANDROID) {
                return convertDetectFalsePositivesReportAssetsInfoList(androidStatisticsServiceApi.findDetectFalsePositivesReport(param));
            } else if (terminalType == TerminalTypeEnum.IOS) {
                return convertDetectFalsePositivesReportAssetsInfoList(iosStatisticsServiceApi.findDetectFalsePositivesReport(param));
            }
        } catch (Exception e) {
            log.error("调用远程误报报告服务失败, userId: {}, terminalType: {}", userId, query.getTerminalType(), e);
        }
        return Collections.emptyList();
    }

    @Override
    public HomePageDetectionStatistics homePageDetectionStatistics(Long userId, HomePageDetectionStatisticsQuery query) {
        TerminalTypeEnum terminalType = TerminalTypeEnum.getItem(query.getTerminalType());
        if (terminalType == null) {
            return null;
        }

        HomePageDetectionStatisticsParam param = new HomePageDetectionStatisticsParam();
        param.setUserId(userId);
        BeanUtils.copyProperties(query, param);

        try {
            if (terminalType == TerminalTypeEnum.ANDROID) {
                return convertHomePageDetectionStatisticsDto(androidStatisticsServiceApi.homePageDetectionStatistics(param));
            } else if (terminalType == TerminalTypeEnum.IOS) {
                return convertHomePageDetectionStatisticsDto(iosStatisticsServiceApi.homePageDetectionStatistics(param));
            }
        } catch (Exception e) {
            log.error("调用远程首页检测统计服务失败, userId: {}, terminalType: {}", userId, query.getTerminalType(), e);
        }
        return null;
    }

    @Override
    public void deleteByTaskId(Long taskId) {
        try {
            // 同时调用Android和iOS服务删除任务相关统计数据
            androidStatisticsServiceApi.deleteByTaskId(taskId);
            iosStatisticsServiceApi.deleteByTaskId(taskId);
        } catch (Exception e) {
            log.error("调用远程删除任务统计数据服务失败, taskId: {}", taskId, e);
        }
    }

    @Override
    public void deleteByAssetsId(Long assetsId) {
        try {
            // 同时调用Android和iOS服务删除资产相关统计数据
            androidStatisticsServiceApi.deleteByAssetsId(assetsId);
            iosStatisticsServiceApi.deleteByAssetsId(assetsId);
        } catch (Exception e) {
            log.error("调用远程删除资产统计数据服务失败, assetsId: {}", assetsId, e);
        }
    }

    /**
     * 转换AssetsStatisticsDTO到AssetsStatistics
     */
    private static AssetsStatistics convert(AssetsStatisticsDTO assetsStatisticsDTO) {
        if (assetsStatisticsDTO == null) {
            return null;
        }
        AssetsStatistics assetsStatistics = new AssetsStatistics();
        BeanUtils.copyProperties(assetsStatisticsDTO, assetsStatistics);
        return assetsStatistics;
    }

    /**
     * 转换List<AssetsDetectionDTO>到List<AssetsDetection>
     */
    private static List<AssetsDetection> convertAssetsDetectionDtoList(List<AssetsDetectionDTO> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) {
            return Collections.emptyList();
        }

        return dtoList.stream()
                .map(RemoteStatisticsServiceImpl::convertAssetsDetectionDto)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个AssetsDetectionDTO到AssetsDetection
     */
    private static AssetsDetection convertAssetsDetectionDto(AssetsDetectionDTO dto) {
        if (dto == null) {
            return null;
        }

        AssetsDetection assetsDetection = new AssetsDetection();
        BeanUtils.copyProperties(dto, assetsDetection);
        return assetsDetection;
    }

    /**
     * 转换List<AssetsStatisticsDetailDTO>到List<AssetsStatisticsDetail>
     */
    private static List<AssetsStatisticsDetail> convertAssetsStatisticsDetailDtoList(List<AssetsStatisticsDetailDTO> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) {
            return Collections.emptyList();
        }

        return dtoList.stream()
                .map(RemoteStatisticsServiceImpl::convertAssetsStatisticsDetailDto)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个AssetsStatisticsDetailDTO到AssetsStatisticsDetail
     */
    private static AssetsStatisticsDetail convertAssetsStatisticsDetailDto(AssetsStatisticsDetailDTO dto) {
        if (dto == null) {
            return null;
        }

        AssetsStatisticsDetail detail = new AssetsStatisticsDetail();
        BeanUtils.copyProperties(dto, detail);
        return detail;
    }

    /**
     * 转换List<AssetsTaskDTO>到List<AssetsTask>
     */
    private static List<AssetsTask> convertAssetsTaskDtoList(List<AssetsTaskDTO> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) {
            return Collections.emptyList();
        }

        return dtoList.stream()
                .map(RemoteStatisticsServiceImpl::convertAssetsTaskDto)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个AssetsTaskDTO到AssetsTask
     */
    private static AssetsTask convertAssetsTaskDto(AssetsTaskDTO dto) {
        if (dto == null) {
            return null;
        }

        AssetsTask task = new AssetsTask();
        BeanUtils.copyProperties(dto, task);
        return task;
    }

    /**
     * 转换DetectionStatisticsDTO到DetectionStatistics
     */
    private static DetectionStatistics convertDetectionStatisticsDto(DetectionStatisticsDTO dto) {
        if (dto == null) {
            return null;
        }

        DetectionStatistics statistics = new DetectionStatistics();
        BeanUtils.copyProperties(dto, statistics);
        return statistics;
    }

    /**
     * 转换PageInfo<DetectionStatisticsDetailDTO>到PageInfo<DetectionStatisticsDetail>
     */
    private static PageInfo<DetectionStatisticsDetail> convertDetectionStatisticsDetailPageInfo(PageInfo<DetectionStatisticsDetailDTO> pageInfo) {
        if (pageInfo == null) {
            return PageInfo.emptyPageInfo();
        }

        PageInfo<DetectionStatisticsDetail> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);
        result.setList(pageInfo.getList().stream().map(item -> {
            DetectionStatisticsDetail detail = new DetectionStatisticsDetail();
            BeanUtils.copyProperties(item, detail);
            return detail;
        }).collect(Collectors.toList()));
        return result;
    }

    /**
     * 转换DetectionStatisticsLawItemDTO到DetectionStatisticsLawItem
     */
    private static DetectionStatisticsLawItem convertDetectionStatisticsLawItemDto(DetectionStatisticsLawItemDTO dto) {
        if (dto == null) {
            return null;
        }

        DetectionStatisticsLawItem lawItem = new DetectionStatisticsLawItem();
        BeanUtils.copyProperties(dto, lawItem);
        return lawItem;
    }

    /**
     * 转换PageInfo<AssetsInfoDTO>到PageInfo<AssetsInfo>
     */
    private static PageInfo<AssetsInfo> convertAssetsInfoPageInfo(PageInfo<AssetsInfoDTO> pageInfo) {
        if (pageInfo == null) {
            return PageInfo.emptyPageInfo();
        }

        PageInfo<AssetsInfo> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);
        result.setList(pageInfo.getList().stream().map(item -> {
            AssetsInfo info = new AssetsInfo();
            BeanUtils.copyProperties(item, info);
            return info;
        }).collect(Collectors.toList()));
        return result;
    }

    /**
     * 根据法规子条目ID判断终端类型（这是一个简化的实现，实际可能需要查询数据库）
     */
    private static TerminalTypeEnum getTerminalTypeByLawChildItemId(Long lawChildItemId) {
        // 这里应该根据实际的业务逻辑来判断，可能需要查询数据库
        // 暂时返回Android作为默认值，实际实现时需要根据具体的法规条目ID来判断
        return TerminalTypeEnum.ANDROID;
    }

    /**
     * 转换PageInfo<SdkStatisticsDetailDTO>到PageInfo<SdkStatisticsDetail>
     */
    private static PageInfo<SdkStatisticsDetail> convertSdkStatisticsDetailPageInfo(PageInfo<SdkStatisticsDetailDTO> pageInfo) {
        if (pageInfo == null) {
            return PageInfo.emptyPageInfo();
        }

        PageInfo<SdkStatisticsDetail> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);
        result.setList(pageInfo.getList().stream().map(item -> {
            SdkStatisticsDetail detail = new SdkStatisticsDetail();
            BeanUtils.copyProperties(item, detail);
            return detail;
        }).collect(Collectors.toList()));
        return result;
    }

    /**
     * 转换DetectFalsePositivesStatisticsDTO到DetectFalsePositivesStatistics
     */
    private static DetectFalsePositivesStatistics convertDetectFalsePositivesStatisticsDto(DetectFalsePositivesStatisticsDTO dto) {
        if (dto == null) {
            return null;
        }

        DetectFalsePositivesStatistics statistics = new DetectFalsePositivesStatistics();
        BeanUtils.copyProperties(dto, statistics);
        return statistics;
    }

    /**
     * 转换PageInfo<DetectFalsePositivesDetailDTO>到PageInfo<DetectFalsePositivesDetail>
     */
    private static PageInfo<DetectFalsePositivesDetail> convertDetectFalsePositivesDetailPageInfo(PageInfo<DetectFalsePositivesDetailDTO> pageInfo) {
        if (pageInfo == null) {
            return PageInfo.emptyPageInfo();
        }

        PageInfo<DetectFalsePositivesDetail> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);
        result.setList(pageInfo.getList().stream().map(item -> {
            DetectFalsePositivesDetail detail = new DetectFalsePositivesDetail();
            BeanUtils.copyProperties(item, detail);
            return detail;
        }).collect(Collectors.toList()));
        return result;
    }

    /**
     * 转换DetectFalsePositivesLawItemDTO到DetectFalsePositivesLawItem
     */
    private static DetectFalsePositivesLawItem convertDetectFalsePositivesLawItemDto(DetectFalsePositivesLawItemDTO dto) {
        if (dto == null) {
            return null;
        }

        DetectFalsePositivesLawItem lawItem = new DetectFalsePositivesLawItem();
        BeanUtils.copyProperties(dto, lawItem);
        return lawItem;
    }

    /**
     * 转换PageInfo<DetectFalsePositivesAssetsInfoDTO>到PageInfo<DetectFalsePositivesAssetsInfo>
     */
    private static PageInfo<DetectFalsePositivesAssetsInfo> convertDetectFalsePositivesAssetsInfoPageInfo(PageInfo<DetectFalsePositivesAssetsInfoDTO> pageInfo) {
        if (pageInfo == null) {
            return PageInfo.emptyPageInfo();
        }

        PageInfo<DetectFalsePositivesAssetsInfo> result = new PageInfo<>();
        BeanUtils.copyProperties(pageInfo, result);
        result.setList(pageInfo.getList().stream().map(item -> {
            DetectFalsePositivesAssetsInfo info = new DetectFalsePositivesAssetsInfo();
            BeanUtils.copyProperties(item, info);
            return info;
        }).collect(Collectors.toList()));
        return result;
    }

    /**
     * 转换List<DetectFalsePositivesReportAssetsInfoDTO>到List<DetectFalsePositivesReportAssetsInfo>
     */
    private static List<DetectFalsePositivesReportAssetsInfo> convertDetectFalsePositivesReportAssetsInfoList(List<DetectFalsePositivesReportAssetsInfoDTO> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) {
            return Collections.emptyList();
        }

        return dtoList.stream()
                .map(RemoteStatisticsServiceImpl::convertDetectFalsePositivesReportAssetsInfoDto)
                .collect(Collectors.toList());
    }

    /**
     * 转换单个DetectFalsePositivesReportAssetsInfoDTO到DetectFalsePositivesReportAssetsInfo
     */
    private static DetectFalsePositivesReportAssetsInfo convertDetectFalsePositivesReportAssetsInfoDto(DetectFalsePositivesReportAssetsInfoDTO dto) {
        if (dto == null) {
            return null;
        }

        DetectFalsePositivesReportAssetsInfo info = new DetectFalsePositivesReportAssetsInfo();
        BeanUtils.copyProperties(dto, info);
        return info;
    }

    /**
     * 转换HomePageDetectionStatisticsDTO到HomePageDetectionStatistics
     */
    private static HomePageDetectionStatistics convertHomePageDetectionStatisticsDto(HomePageDetectionStatisticsDTO dto) {
        if (dto == null) {
            return null;
        }

        HomePageDetectionStatistics statistics = new HomePageDetectionStatistics();
        BeanUtils.copyProperties(dto, statistics);
        return statistics;
    }

}
