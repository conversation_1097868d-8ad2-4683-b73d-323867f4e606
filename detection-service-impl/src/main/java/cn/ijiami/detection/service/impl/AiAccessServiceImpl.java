package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.VO.ai.AIAvailableCountVO;
import cn.ijiami.detection.entity.TAIConsumption;
import cn.ijiami.detection.entity.TAiApiCalls;
import cn.ijiami.detection.entity.TAiAuthCodes;
import cn.ijiami.detection.entity.TAiClients;
import cn.ijiami.detection.entity.TAiUsageCounter;
import cn.ijiami.detection.entity.TDetectionConfig;
import cn.ijiami.detection.enums.AiClientTypeEnum;
import cn.ijiami.detection.enums.AiUsageLimitTypeEnum;
import cn.ijiami.detection.enums.BooleanEnum;
import cn.ijiami.detection.mapper.TAIConsumptionMapper;
import cn.ijiami.detection.mapper.TAiApiCallsMapper;
import cn.ijiami.detection.mapper.TAiAuthCodesMapper;
import cn.ijiami.detection.mapper.TAiClientsMapper;
import cn.ijiami.detection.mapper.TAiQuotaUpdatesMapper;
import cn.ijiami.detection.mapper.TAiUsageCounterMapper;
import cn.ijiami.detection.mapper.TDetectionConfigMapper;
import cn.ijiami.detection.service.api.AIAssistantService;
import cn.ijiami.detection.service.api.AiAccessService;
import cn.ijiami.detection.service.api.DistributedLockService;
import cn.ijiami.detection.utils.TimestampUtils;
import cn.ijiami.framework.common.enums.HttpStatusEnum;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.framework.common.response.BaseResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import static cn.ijiami.detection.constant.DistributedLockConstant.KEY_AI_USAGE_COUNTER;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AiAccessServiceImpl.java
 * @Description ai api key校验
 * @createTime 2025年03月25日 11:52:00
 */
@Slf4j
@Service
public class AiAccessServiceImpl implements AiAccessService {

    @Autowired
    private TAiClientsMapper aiClientsMapper;

    @Autowired
    private TAiAuthCodesMapper aiAuthCodesMapper;

    @Autowired
    private TAiApiCallsMapper aiApiCallsMapper;

    @Autowired
    private TAiQuotaUpdatesMapper aiQuotaUpdatesMapper;

    @Autowired
    private TAIConsumptionMapper aiConsumptionMapper;

    @Autowired
    private TDetectionConfigMapper detectionConfigMapper;

    @Autowired
    private AIAssistantService aiAssistantService;

    @Autowired
    private TAiUsageCounterMapper aiUsageCounterMapper;

    @Autowired
    @Qualifier("redisDistributedLock")
    private DistributedLockService distributedLockService;

    /**
     * 消费配额，用于非云平台的ai调用
     * @param authorization
     * @param endpoint
     * @return
     */
    @Transactional
    @Override
    public BaseResponse<Long> consumeApiQuota(String authorization, String endpoint) {
        String apiKey = extractApiKeyFromAuthHeader(authorization);
        try {
            // 加锁，避免并发问题导致修改配额出错
            if (distributedLockService.tryLock(apiKey, TimeUnit.SECONDS.toMillis(5))) {
                TAiAuthCodes aiAuthCodes = aiAuthCodesMapper.findByAuthCodeId(apiKey);
                if (Objects.isNull(aiAuthCodes)) {
                    log.info("apiKey={} 找不到", apiKey);
                    return buildQuotaDeductionFailureResponse(apiKey, endpoint, "apiKey 不存在");
                }
                if (aiAuthCodes.getIsDelete() == BooleanEnum.TRUE.value) {
                    log.info("apiKey={} 已经删除", apiKey);
                    return buildQuotaDeductionFailureResponse(apiKey, endpoint, "apiKey 不存在");
                }
                if (aiAuthCodes.getStartDate().getTime() > System.currentTimeMillis()) {
                    log.info("apiKey={} startTime={} 未生效", apiKey, aiAuthCodes.getStartDate());
                    return buildQuotaDeductionFailureResponse(apiKey, endpoint, "apiKey 未生效");
                }
                if (aiAuthCodes.getEndDate().getTime() < System.currentTimeMillis()) {
                    log.info("apiKey={} endTime={} 已失效", apiKey, aiAuthCodes.getEndDate());
                    return buildQuotaDeductionFailureResponse(apiKey, endpoint, "apiKey 已失效");
                }
                TAiClients clients = aiClientsMapper.findByClientId(aiAuthCodes.getClientId());
                if (Objects.isNull(clients) || clients.getStatus() == AiClientTypeEnum.DISABLE.itemValue()) {
                    log.info("apiKey={} 未启用", apiKey);
                    return buildQuotaDeductionFailureResponse(apiKey, endpoint, "apiKey 未启用");
                }
                if (aiAuthCodes.getRemainingQuota() <= 0) {
                    log.info("apiKey={} 额度不足", apiKey);
                    return buildQuotaDeductionFailureResponse(apiKey, endpoint, "apiKey 额度不足");
                }
                aiAuthCodesMapper.updateRemainingQuota(apiKey, aiAuthCodes.getRemainingQuota() - 1);
                return buildQuotaDeductionSuccessResponse(apiKey, endpoint);
            }
            return buildQuotaDeductionFailureResponse(apiKey, endpoint, "服务错误");
        } finally {
            distributedLockService.unlock(apiKey);
        }
    }

    private BaseResponse<Long> buildQuotaDeductionFailureResponse(String apiKey, String endpoint, String failureMessage) {
        BaseResponse<Long> response = new BaseResponse<>();
        response.setStatus(HttpStatusEnum.FAIL.getValue());
        response.setMessage(failureMessage);
        TAiApiCalls aiApiCalls = new TAiApiCalls();
        aiApiCalls.setApiEndpoint(endpoint);
        aiApiCalls.setAuthCodeId(apiKey);
        aiApiCalls.setErrorMessage(failureMessage);
        aiApiCalls.setIpAddress("");
        aiApiCalls.setSuccess(BooleanEnum.FALSE.value);
        aiApiCalls.setCallTime(new Date());
        aiApiCallsMapper.insert(aiApiCalls);
        return response;
    }

    private BaseResponse<Long> buildQuotaDeductionSuccessResponse(String apiKey, String endpoint) {
        TAiApiCalls aiApiCalls = new TAiApiCalls();
        aiApiCalls.setApiEndpoint(endpoint);
        aiApiCalls.setAuthCodeId(apiKey);
        aiApiCalls.setErrorMessage("");
        aiApiCalls.setIpAddress("");
        aiApiCalls.setSuccess(BooleanEnum.TRUE.value);
        aiApiCalls.setCallTime(new Date());
        aiApiCallsMapper.insert(aiApiCalls);
        BaseResponse<Long> response = new BaseResponse<>();
        response.setStatus(HttpStatusEnum.SUCCESS.getValue());
        response.setData(aiApiCalls.getId());
        return response;
    }

    private String extractApiKeyFromAuthHeader(String apiKey) {
        return StringUtils.remove(apiKey, "Bearer ");
    }

    /**
     * 回滚配额，用于非云平台的ai调用
     * @param authorization
     * @param callId
     * @return
     */
    @Override
    public boolean refundApiQuota(String authorization, Long callId) {
        String apiKey = extractApiKeyFromAuthHeader(authorization);
        try {
            // 加锁，避免并发问题导致修改配额出错
            if (distributedLockService.tryLock(apiKey, TimeUnit.SECONDS.toMillis(5))) {
                TAiApiCalls aiApiCalls = aiApiCallsMapper.selectByPrimaryKey(callId);
                if (Objects.isNull(aiApiCalls)) {
                    log.info("callId={} 找不到", callId);
                    return false;
                }
                if (aiApiCalls.getSuccess() == BooleanEnum.FALSE.value) {
                    log.info("callId={} 状态错误", callId);
                    return false;
                }
                TAiAuthCodes aiAuthCodes = aiAuthCodesMapper.findByAuthCodeId(apiKey);
                if (Objects.isNull(aiAuthCodes)) {
                    log.info("apiKey={} 找不到", apiKey);
                    return false;
                }
                if (aiAuthCodes.getIsDelete() == BooleanEnum.TRUE.value) {
                    log.info("apiKey={} 已经删除", apiKey);
                    return false;
                }
                aiAuthCodesMapper.updateRemainingQuota(apiKey, aiAuthCodes.getRemainingQuota() + 1);
                TAiApiCalls updateCalls = new TAiApiCalls();
                updateCalls.setId(callId);
                updateCalls.setSuccess(BooleanEnum.FALSE.value);
                updateCalls.setErrorMessage("回滚");
                aiApiCallsMapper.updateByPrimaryKeySelective(updateCalls);
                return true;
            }
            return false;
        } finally {
            distributedLockService.unlock(apiKey);
        }
    }

    /**
     * 查询配额
     * @param authorization
     * @return
     */
    @Override
    public BaseResponse<AIAvailableCountVO> queryApiQuota(String authorization) {
        String apiKey = extractApiKeyFromAuthHeader(authorization);
        TAiAuthCodes aiAuthCodes = aiAuthCodesMapper.findByAuthCodeId(apiKey);
        if (Objects.isNull(aiAuthCodes)) {
            log.info("apiKey={} 找不到", apiKey);
            return BaseResponse.FAIL("apiKey 不存在");
        }
        if (aiAuthCodes.getIsDelete() == BooleanEnum.TRUE.value) {
            log.info("apiKey={} 已经删除", apiKey);
            return BaseResponse.FAIL("apiKey 不存在");
        }
        if (aiAuthCodes.getStartDate().getTime() > System.currentTimeMillis()) {
            log.info("apiKey={} startTime={} 未生效", apiKey, aiAuthCodes.getStartDate());
            return BaseResponse.FAIL("apiKey 未生效");
        }
        if (aiAuthCodes.getEndDate().getTime() < System.currentTimeMillis()) {
            log.info("apiKey={} endTime={} 已失效", apiKey, aiAuthCodes.getEndDate());
            return BaseResponse.FAIL("apiKey 已失效");
        }
        TAiClients clients = aiClientsMapper.findByClientId(aiAuthCodes.getClientId());
        if (Objects.isNull(clients) || clients.getStatus() == AiClientTypeEnum.DISABLE.itemValue()) {
            log.info("apiKey={} 未启用", apiKey);
            return BaseResponse.FAIL("apiKey 未启用");
        }
        AIAvailableCountVO aiAvailableCountVO = new AIAvailableCountVO();
        aiAvailableCountVO.setAvailable(aiAuthCodes.getRemainingQuota());
        aiAvailableCountVO.setLimit(aiAuthCodes.getTotalQuota());
        aiAvailableCountVO.setLimitType(AiUsageLimitTypeEnum.TOTAL.getValue());
        return BaseResponse.SUCCESS(aiAvailableCountVO);
    }


    /**
     * 检查用户用量限制
     * @param userId
     * @return
     */
    @Override
    public boolean checkUserUsageLimit(Long userId) {
        TDetectionConfig config = getDetectionConfig(userId);
        if (Objects.isNull(config.getAiUsageLimit())) {
            // 不做限制
            return true;
        }
        if (config.getAiUsageLimitType() == AiUsageLimitTypeEnum.MONTHLY) {
            long count = aiAssistantService.countConsumptionByUser(userId, new Date(TimestampUtils.getStartOfMonthTimestamp()));
            return count < config.getAiUsageLimit();
        } else if (config.getAiUsageLimitType() == AiUsageLimitTypeEnum.YEARLY) {
            long count = aiAssistantService.countConsumptionByUser(userId, new Date(TimestampUtils.getStartOfYearTimestamp()));
            return count < config.getAiUsageLimit();
        } else {
            long count = getTotalCount(userId);
            return count < config.getAiUsageLimit();
        }
    }

    private long getTotalCount(Long userId) {
        Example example = new Example(TAiUsageCounter.class);
        example.createCriteria().andEqualTo("userId", userId);
        TAiUsageCounter counter = aiUsageCounterMapper.selectOneByExample(example);
        return counter != null ? counter.getTotalCount() : 0;
    }

    /**
     * 记录用户使用情况
     * @param userId
     * @param path
     * @return
     */
    @Override
    @Transactional
    public Long trackUserUsage(Long userId, String path) {
        TDetectionConfig config = getDetectionConfig(userId);
        if (config.getAiUsageLimit() != null) {
            // 增加计数（包含自动重置和限额检查）
            incrementCount(userId);
        }

        // 记录使用详情
        TAIConsumption consumption = new TAIConsumption();
        consumption.setUserId(userId);
        consumption.setPath(path);
        consumption.setIsDelete(BooleanEnum.FALSE.value);
        consumption.setCreateTime(new Date());
        consumption.setUpdateTime(new Date());
        aiConsumptionMapper.insert(consumption);

        return consumption.getId();
    }

    private void incrementCount(Long userId) {
        String lockKey = KEY_AI_USAGE_COUNTER + userId;
        try {
            if (distributedLockService.tryLock(lockKey, TimeUnit.SECONDS.toMillis(5))) {
                Example example = new Example(TAiUsageCounter.class);
                example.createCriteria().andEqualTo("userId", userId);
                TAiUsageCounter counter = aiUsageCounterMapper.selectOneByExample(example);
                Date now = new Date();
                // 初始化计数器（如果不存在）
                if (counter == null) {
                    counter = new TAiUsageCounter();
                    counter.setUserId(userId);
                    counter.setTotalCount(1);
                    counter.setUpdateTime(now);
                    aiUsageCounterMapper.insert(counter);
                    return;
                }
                // 增加计数
                TAiUsageCounter increase = new TAiUsageCounter();
                increase.setId(counter.getId());
                increase.setTotalCount(counter.getTotalCount() + 1);
                increase.setUpdateTime(now);
                aiUsageCounterMapper.updateByPrimaryKeySelective(counter);
            } else {
                throw new IjiamiRuntimeException("获取用量计数器锁超时");
            }
        } finally {
            distributedLockService.unlock(lockKey);
        }
    }

    @Override
    public void deleteUserUsage(Long recordId) {
        // 先查询记录获取用户ID
        TAIConsumption consumption = aiConsumptionMapper.selectByPrimaryKey(recordId);
        if (consumption == null || consumption.getIsDelete() == BooleanEnum.TRUE.value) {
            return;
        }

        // 标记删除
        consumption.setIsDelete(BooleanEnum.TRUE.value);
        aiConsumptionMapper.updateByPrimaryKeySelective(consumption);

        // 减少计数器
        TDetectionConfig config = getDetectionConfig(consumption.getUserId());
        if (config != null && config.getAiUsageLimit() != null) {
            decrementCount(consumption.getUserId());
        }
    }

    private void decrementCount(Long userId) {
        String lockKey = KEY_AI_USAGE_COUNTER + userId;
        try {
            if (distributedLockService.tryLock(lockKey, TimeUnit.SECONDS.toMillis(5))) {
                Example example = new Example(TAiUsageCounter.class);
                example.createCriteria().andEqualTo("userId", userId);
                TAiUsageCounter counter = aiUsageCounterMapper.selectOneByExample(example);
                if (counter == null) {
                    return; // 计数器不存在，直接返回
                }
                TAiUsageCounter decrease = new TAiUsageCounter();
                decrease.setId(counter.getId());
                decrease.setTotalCount(counter.getTotalCount() - 1);
                aiUsageCounterMapper.updateByPrimaryKeySelective(decrease);
            }
        } finally {
            distributedLockService.unlock(lockKey);
        }
    }

    private TDetectionConfig getDetectionConfig(Long userId) {
        Example example = new Example(TDetectionConfig.class);
        example.createCriteria().andEqualTo("userId", userId);
        return detectionConfigMapper.selectOneByExample(example);
    }
}
