package cn.ijiami.detection.service.impl;

import cn.ijiami.detection.mapper.ISysLogMapper;
import cn.ijiami.detection.query.SysLogQuery;
import cn.ijiami.detection.service.api.ISysLogService;
import cn.ijiami.manager.syslog.entity.TsysOperateLog;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 个人信息检测审计日志实现类
 */
@Service
@Slf4j
public class SysLogServiceImpl implements ISysLogService{

    @Autowired
    private ISysLogMapper sysLogMapper;

    @Override
    public PageInfo<TsysOperateLog> findSysLogByQuery(SysLogQuery query){
        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        List<TsysOperateLog> logList = sysLogMapper.selectSysLogByQuery(query);
        return new PageInfo<>(logList);
    }
}
