package cn.ijiami.detection.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.ijiami.detection.DTO.compliance.ComplianceSensitiveGroupDto;
import cn.ijiami.detection.DTO.compliance.ComplianceSensitiveWordDto;
import cn.ijiami.detection.VO.compliance.ComplianceBaseVo;
import cn.ijiami.detection.entity.TSensitiveType;
import cn.ijiami.detection.mapper.TPrivacySensitiveWordMapper;
import cn.ijiami.detection.mapper.TPrivacySharedPrefsMapper;
import cn.ijiami.detection.mapper.TSensitiveTypeMapper;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.entity.TSensitiveWord;
import cn.ijiami.detection.mapper.TSensitiveWordMapper;
import cn.ijiami.detection.service.api.ISensitiveWordService;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import tk.mybatis.mapper.entity.Example;

@Service
@Transactional
@CacheConfig(cacheNames = {"privacy-detection:sensitiveWord"})
public class SensitiveWordServiceImpl implements ISensitiveWordService {

    private final TSensitiveWordMapper sensitiveWordMapper;
    private final TPrivacySensitiveWordMapper privacySensitiveWordMapper;
    private final TPrivacySharedPrefsMapper privacySharedPrefsMapper;
    private final TSensitiveTypeMapper sensitiveTypeMapper;

    public SensitiveWordServiceImpl(TSensitiveWordMapper sensitiveWordMapper,TPrivacySensitiveWordMapper privacySensitiveWordMapper,TPrivacySharedPrefsMapper privacySharedPrefsMapper,
                                    TSensitiveTypeMapper sensitiveTypeMapper) {
        this.sensitiveWordMapper = sensitiveWordMapper;
        this.privacySensitiveWordMapper = privacySensitiveWordMapper;
        this.privacySharedPrefsMapper = privacySharedPrefsMapper;
        this.sensitiveTypeMapper = sensitiveTypeMapper;
    }

    @Override
    @CacheEvict(value = "privacy-detection:sensitiveWord", allEntries = true)
    public void addSensitiveWord(TSensitiveWord sensitiveWord) throws IjiamiApplicationException {
        TSensitiveWord sensitiveWordTwo = new TSensitiveWord();
        sensitiveWordTwo.setName(sensitiveWord.getName());
        sensitiveWordTwo.setTerminalType(sensitiveWord.getTerminalType());
        List<TSensitiveWord> oldSensitiveWordList = sensitiveWordMapper.findSensitiveWord(sensitiveWordTwo)
                .stream()
                .filter(word -> !word.getId().equals(sensitiveWord.getId()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(oldSensitiveWordList)) {
            throw new IjiamiApplicationException("已存在敏感词汇：" + sensitiveWord.getName());
        }
        if (sensitiveWord.getId() == null) {
            sensitiveWordMapper.insertSelective(sensitiveWord);
        } else {
            sensitiveWordMapper.updateByPrimaryKeySelective(sensitiveWord);
        }
    }

    @Override
    public TSensitiveWord findById(Long id) {
        return sensitiveWordMapper.selectByPrimaryKey(id);
    }

    @Override
    @CacheEvict(value = "privacy-detection:sensitiveWord", allEntries = true)
    public int deleteSensitiveWordById(Long sensitiveWordId) {
        return sensitiveWordMapper.deleteByPrimaryKey(sensitiveWordId);
    }

    @Override
    public PageInfo<TSensitiveWord> findSensitiveWordByWord(TSensitiveWord sensitiveWord)
            throws IjiamiApplicationException {
        if (sensitiveWord.getTypeId() == null) {
            throw new IjiamiApplicationException("敏感词分类ID不能为空");
        }
        if (sensitiveWord.getPage() != null && sensitiveWord.getRows() != null) {
            PageHelper.startPage(sensitiveWord.getPage(), sensitiveWord.getRows());
        }
        List<TSensitiveWord> sensitiveWordList = sensitiveWordMapper.selectSensitiveWordByPage(sensitiveWord);

        return new PageInfo<>(sensitiveWordList);
    }

    /**
     * 查询个人信息使用场景的个人信息数据
     * @param complianceBaseVo
     * @return
     */
    @Override
    public List<ComplianceSensitiveGroupDto> querySensitive(ComplianceBaseVo complianceBaseVo) {
        List<ComplianceSensitiveGroupDto> complianceSensitiveGroupDtos = new ArrayList<>();
        Long taskId = complianceBaseVo.getTaskId();
        List<Long> netSensitiveIdList = privacySensitiveWordMapper.querySensitiveId(taskId);
        List<Long> storageSensitiveIdList = privacySharedPrefsMapper.querySensitiveId(taskId);


        Example example = new Example(TSensitiveType.class);
        example.createCriteria().andEqualTo("sensitiveType", 1);
        List<TSensitiveType> sensitiveTypes = sensitiveTypeMapper.selectByExample(example);

        List<TSensitiveWord> sensitiveWordList = sensitiveWordMapper.findByTerminalType(complianceBaseVo.getTerminalType().getValue());
        Map<Long, List<TSensitiveWord>> wordMap = sensitiveWordList.stream().collect(Collectors.groupingBy(TSensitiveWord::getTypeId));
        for (TSensitiveType type : sensitiveTypes) {
            ComplianceSensitiveGroupDto dto = new ComplianceSensitiveGroupDto();
            dto.setGroupName(type.getTypeName()).setGroupId(type.getId());
            dto.setComplianceSensitiveWordDtos(new ArrayList<>());
            if (wordMap.containsKey(type.getId())) {
                List<TSensitiveWord> tSensitiveWords = wordMap.get(type.getId());
                tSensitiveWords.forEach(d -> {
                    dto.getComplianceSensitiveWordDtos().add(createSensitiveWordDTO(d, netSensitiveIdList.contains(d.getId()) || storageSensitiveIdList.contains(d.getId())));
                });
            }
            complianceSensitiveGroupDtos.add(dto);
        }
      return complianceSensitiveGroupDtos;
    }

    /**
     * 创建敏感词
     *
     * @param tSensitiveWord
     * @param hasTrigger
     * @return
     */
    private ComplianceSensitiveWordDto createSensitiveWordDTO(TSensitiveWord tSensitiveWord, Boolean hasTrigger) {
        ComplianceSensitiveWordDto complianceSensitiveWordDto = new ComplianceSensitiveWordDto();
        complianceSensitiveWordDto.setWordId(tSensitiveWord.getId());
        complianceSensitiveWordDto.setWordName(tSensitiveWord.getName());
        complianceSensitiveWordDto.setHasTrigger(hasTrigger);
        return complianceSensitiveWordDto;
    }
}
