package cn.ijiami.detection.service.impl;


import java.io.IOException;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import cn.ijiami.detection.config.IjiamiCommonProperties;
import cn.ijiami.detection.service.api.IRoboflowService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.ConstantsUtils;
import cn.ijiami.detection.utils.MD5Util;
import cn.ijiami.detection.utils.RoboFllowUtils;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.file.service.api.IFileService;
import cn.ijiami.framework.file.vo.FileVO;

@Service
public class RoboflowServiceImpl implements IRoboflowService {

    private static final Logger logger= LoggerFactory.getLogger(RoboflowServiceImpl.class);
    @Autowired
	private  IFileService defaultService;
    @Autowired
    private IjiamiCommonProperties commonProperties;

    @Value("${ijiami.auth.homePath:}")
    private String homePath;

    @Value("${ijiami.authentication.appKey:}")
    private String appKey;

	@Override
	public String roboflowUploadImage(String apiKey, String datasetName, MultipartFile data) {
		if(StringUtils.isBlank(apiKey)) {
			apiKey = commonProperties.getProperty("roboflow.apikey");
		}
		if(StringUtils.isBlank(datasetName)) {
			apiKey = commonProperties.getProperty("roboflow.datasetname");
		}
		FileVO fileVO = new FileVO();
		 // 上传前数据拼装，校验
        try {
			beforUpload(fileVO, data);
			// 执行上传
	        uploade(fileVO);
			String filePath = fileVO.getFilePath();
			String result = RoboFllowUtils.uploadLocal(filePath, apiKey, datasetName);
			return result;
		} catch (IOException e) {
			e.getMessage();
		} catch (IjiamiApplicationException e) {
			e.getMessage();
		}
		return null;
	}
	
	@Override
	public String roboflowUploadImageLocal(String imagePath) {
		String apiKey = commonProperties.getProperty("roboflow.apikey");
		String datasetName = commonProperties.getProperty("roboflow.datasetname");
		String result = RoboFllowUtils.uploadLocal(imagePath, apiKey, datasetName);
		return result;
	}
	
	@Override
	public String exprotData(String workspace, String project, String version, String format, String apiKey) {
		
		if(StringUtils.isBlank(apiKey)) {
			apiKey = commonProperties.getProperty("roboflow.apikey");
		}
		return RoboFllowUtils.exportData(workspace, project, version, format, apiKey);
	}
	
	 // 执行上传
    private void uploade(FileVO fileVO) throws IjiamiApplicationException, IOException {
        fileVO = defaultService.upload(fileVO);
        if(StringUtils.isBlank(fileVO.getFilePath())){
        	fileVO.setFilePath(fileVO.getFileUrl()+fileVO.getRelativePath());
        }
    }
    
	// 上传前操作
    private void beforUpload(FileVO fileVO, MultipartFile file) throws IOException, IjiamiApplicationException {
        fileVO.setInputStream(file.getInputStream());
        fileVO.setFileSize(file.getSize());
        String fileName = file.getOriginalFilename().replaceAll(" ", "").trim();
		CommonUtil.checkAppFileExt(fileName);
        String MD5 = MD5Util.getFileMD5(file.getInputStream());
        String uuid = UUID.randomUUID().toString().replace("-", "");
        // 固定格式的文件名格式，后续根据此获取MD5
        fileName = ConstantsUtils.ASSET_SIGN + MD5 + ConstantsUtils.FILE_NAME_SEPARATOR + uuid + ConstantsUtils.FILE_NAME_SEPARATOR + fileName.substring(fileName.indexOf("."));
        fileVO.setFileName(fileName);
        Map<String, Object> params = new HashMap<>();
        DecimalFormat df = new DecimalFormat("0.00");
        String parseLong = df.format((double) file.getSize() / ConstantsUtils.BYTENUM / ConstantsUtils.BYTENUM);
        params.put("size", parseLong);
        fileVO.setParams(params);
    }

}
