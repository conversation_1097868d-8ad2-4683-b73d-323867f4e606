package cn.ijiami.detection.service.impl;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.core.type.TypeReference;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.ijiami.base.common.user.IUser;
import cn.ijiami.detection.config.IjiamiCommonProperties;
import cn.ijiami.detection.entity.TActionFilterGroup;
import cn.ijiami.detection.entity.TActionFilterGroupRegex;
import cn.ijiami.detection.entity.TActionFilterGroupUser;
import cn.ijiami.detection.enums.ActionFilterFieldEnum;
import cn.ijiami.detection.enums.ActionFilterGroupCategoryEnum;
import cn.ijiami.detection.enums.ActionFilterGroupStatusEnum;
import cn.ijiami.detection.enums.ActionFilterModeEnum;
import cn.ijiami.detection.enums.ActionFilterTypeEnum;
import cn.ijiami.detection.enums.StatusEnum;
import cn.ijiami.detection.enums.TerminalTypeEnum;
import cn.ijiami.detection.mapper.TActionFilterGroupMapper;
import cn.ijiami.detection.mapper.TActionFilterGroupRegexMapper;
import cn.ijiami.detection.mapper.TActionFilterGroupUserMapper;
import cn.ijiami.detection.mapper.UserQueryMapper;
import cn.ijiami.detection.query.ActionFilterGroupPageQuery;
import cn.ijiami.detection.query.ActionFilterGroupRegexPageQuery;
import cn.ijiami.detection.query.ActionFilterGroupRelRegexListVO;
import cn.ijiami.detection.query.ActionFilterGroupSave;
import cn.ijiami.detection.query.ActionFilterGroupVO;
import cn.ijiami.detection.query.ActionFilterVO;
import cn.ijiami.detection.service.api.ActionFilterGroupService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.framework.kit.utils.UuidUtil;
import cn.ijiami.manager.user.entity.User;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ActionFilterGroupServiceImpl.java
 * @Description 行为过滤集合
 * @createTime 2023年12月12日 11:46:00
 */
@Component
public class ActionFilterGroupServiceImpl implements ActionFilterGroupService {

    @Autowired
    private TActionFilterGroupMapper actionFilterGroupMapper;

    @Autowired
    private TActionFilterGroupUserMapper actionFilterGroupUserMapper;

    @Autowired
    private TActionFilterGroupRegexMapper actionFilterGroupRegexMapper;

    @Autowired
    private UserQueryMapper userQueryMapper;

    @Autowired
    private IjiamiCommonProperties commonProperties;

    @Override
    public PageInfo<ActionFilterGroupVO> findGroupByPage(IUser user, ActionFilterGroupPageQuery query, boolean isAdmin) {
        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        List<ActionFilterGroupVO> groupVOList;
        if (query.getCategory() == ActionFilterGroupCategoryEnum.SPECIFIC_USER.itemValue()) {
            Long userId = isAdmin ? null : user.getUserId();
            groupVOList = actionFilterGroupMapper.findSpecificUserGroupList(TerminalTypeEnum.getItem(query.getTerminalType()), userId);
        } else {
            groupVOList = actionFilterGroupMapper.findAllUserGroupList(TerminalTypeEnum.getItem(query.getTerminalType()));
        }
        return new PageInfo<>(groupVOList);
    }


    @Override
    public PageInfo<ActionFilterVO> findRegexByPage(ActionFilterGroupRegexPageQuery query) {
        if (query.getPage() != null && query.getRows() != null) {
            PageHelper.startPage(query.getPage(), query.getRows());
        }
        TActionFilterGroupRegex regexQuery = new TActionFilterGroupRegex();
        regexQuery.setGroupId(query.getGroupId());
        regexQuery.setStatus(StatusEnum.NORMAL.itemValue());
        PageInfo<TActionFilterGroupRegex> groupRegexPageInfo = new PageInfo<>(actionFilterGroupRegexMapper.select(regexQuery));
        PageInfo<ActionFilterVO> actionFilterPageInfo = new PageInfo<>();
        BeanUtils.copyProperties(groupRegexPageInfo, actionFilterPageInfo);
        actionFilterPageInfo.setList(groupRegexPageInfo.getList().stream().map(r -> {
            ActionFilterVO filterVO = new ActionFilterVO();
            filterVO.setId(r.getId());
            filterVO.setActionId(r.getActionId());
            filterVO.setRegex(r.getActionFilterRegex());
            filterVO.setActionFilterType(r.getActionType());
            return filterVO;
        }).collect(Collectors.toList()));
        return actionFilterPageInfo;
    }

    @Transactional
    @Override
    public void setMainGroup(Long groupId) {
        TActionFilterGroup groupQuery = new TActionFilterGroup();
        groupQuery.setId(groupId);
        TActionFilterGroup group = actionFilterGroupMapper.selectOne(groupQuery);
        if (Objects.isNull(group)) {
            throw new IllegalArgumentException("行为过滤集合不存在，请检查groupId");
        }
        if (group.getStatus() == ActionFilterGroupStatusEnum.DELETE) {
            throw new IllegalArgumentException("行为过滤集合已经被删除");
        }
        TActionFilterGroup mainQuery = new TActionFilterGroup();
        mainQuery.setTerminalType(group.getTerminalType());
        mainQuery.setStatus(ActionFilterGroupStatusEnum.MAIN);
        List<TActionFilterGroup> mainList = actionFilterGroupMapper.select(mainQuery);
        if (!mainList.isEmpty()) {
            mainList.forEach(main -> {
                TActionFilterGroup groupUpdate = new TActionFilterGroup();
                groupUpdate.setId(main.getId());
                groupUpdate.setStatus(ActionFilterGroupStatusEnum.NORMAL);
                groupUpdate.setUpdateTime(new Date());
                actionFilterGroupMapper.updateByPrimaryKeySelective(groupUpdate);
            });
        }
        TActionFilterGroup groupUpdate = new TActionFilterGroup();
        groupUpdate.setId(groupId);
        groupUpdate.setStatus(ActionFilterGroupStatusEnum.MAIN);
        groupUpdate.setUpdateTime(new Date());
        actionFilterGroupMapper.updateByPrimaryKeySelective(groupUpdate);
    }

    @Transactional
    @Override
    public void save(IUser user, ActionFilterGroupSave groupInfo) {
        List<Long> userIds = Arrays.stream(groupInfo.getUserNames().split(";"))
                .map(userName -> userQueryMapper.selectUsersByUserName(userName.trim(), null))
                .filter(Objects::nonNull)
                .map(User::getUserId)
                .collect(Collectors.toList());
        if (userIds.isEmpty()) {
            throw new IllegalArgumentException("指定用户状态错误");
        }
        if (Objects.nonNull(groupInfo.getGroupId())) {
            TActionFilterGroup groupQuery = new TActionFilterGroup();
            groupQuery.setId(groupInfo.getGroupId());
            TActionFilterGroup group = actionFilterGroupMapper.selectOne(groupQuery);
            if (Objects.isNull(group)) {
                throw new IllegalArgumentException("行为过滤集合不存在，请检查groupId");
            }
            if (group.getStatus() == ActionFilterGroupStatusEnum.DELETE) {
                throw new IllegalArgumentException("行为过滤集合已经被删除，无法修改");
            }
            TActionFilterGroup updateGroup = new TActionFilterGroup();
            updateGroup.setId(groupInfo.getGroupId());
            updateGroup.setName(groupInfo.getGroupName());
            updateGroup.setDescription(groupInfo.getDescription());
            updateGroup.setTerminalType(TerminalTypeEnum.getAndValid(groupInfo.getTerminalType()));
            updateGroup.setUpdateTime(new Date());
            actionFilterGroupMapper.updateByPrimaryKeySelective(updateGroup);
            // 修改关联的用户
            updateGroupUser(userIds, groupInfo);
            // 修改关联的规则
            updateGroupRegex(groupInfo);
        } else {
            TActionFilterGroup insertGroup = getActionFilterGroup(groupInfo.getGroupName(),
                    groupInfo.getDescription(),
                    TerminalTypeEnum.getAndValid(groupInfo.getTerminalType()),
                    groupInfo.getUserNames(),
                    user.getUserId());
            actionFilterGroupMapper.save(insertGroup);
            // 关联用户
            for (Long userId:userIds) {
                TActionFilterGroupUser groupUser = new TActionFilterGroupUser();
                groupUser.setGroupId(insertGroup.getId());
                groupUser.setUserId(userId);
                actionFilterGroupUserMapper.save(groupUser);
            }
            // 添加过滤规则
            for (ActionFilterVO filterVO : groupInfo.getActionFilterRegexList()) {
                TActionFilterGroupRegex regex = gettActionFilterGroupRegex(filterVO, insertGroup.getId(), ActionFilterTypeEnum.FILTER);
                actionFilterGroupRegexMapper.save(regex);
            }
            
            // 添加指定函数规则
            for (ActionFilterVO filterVO : groupInfo.getActionAppointRegexList()) {
                TActionFilterGroupRegex regex = gettActionFilterGroupRegex(filterVO, insertGroup.getId(), ActionFilterTypeEnum.APPOINT);
                actionFilterGroupRegexMapper.save(regex);
            }
        }
    }

    private boolean isUpdateGroupRegex(List<TActionFilterGroupRegex> regexList, ActionFilterVO filterVO) {
        return regexList.stream().anyMatch(r -> r.getId().equals(filterVO.getId())
                && (!r.getActionFilterRegex().equals(filterVO.getRegex()) || !r.getActionId().equals(filterVO.getActionId())));
    }

    private void updateGroupRegex(ActionFilterGroupSave groupInfo) {
        TActionFilterGroupRegex regexQuery = new TActionFilterGroupRegex();
        regexQuery.setStatus(StatusEnum.NORMAL.itemValue());
        regexQuery.setGroupId(groupInfo.getGroupId());
        List<TActionFilterGroupRegex> regexList = actionFilterGroupRegexMapper.select(regexQuery);

        List<Long> filterIds = groupInfo.getActionFilterRegexList().stream().map(ActionFilterVO::getId).collect(Collectors.toList());
        List<Long> appointRegexIds = groupInfo.getActionAppointRegexList().stream().map(ActionFilterVO::getId).collect(Collectors.toList());
        filterIds.addAll(appointRegexIds);

        // 删除
        regexList.stream()
                .filter(r -> !filterIds.contains(r.getId()))
                .forEach(r -> {
                    TActionFilterGroupRegex updateRegex = new TActionFilterGroupRegex();
                    updateRegex.setId(r.getId());
                    updateRegex.setStatus(StatusEnum.DELETE.itemValue());
                    actionFilterGroupRegexMapper.updateByPrimaryKeySelective(updateRegex);
                });
        // 新增
        groupInfo.getActionFilterRegexList().stream()
                .filter(filterVO -> regexList.stream().noneMatch(r -> r.getId().equals(filterVO.getId())))
                .forEach(filterVO -> {
                    TActionFilterGroupRegex regex = gettActionFilterGroupRegex(filterVO, groupInfo.getGroupId(), ActionFilterTypeEnum.FILTER);
                    actionFilterGroupRegexMapper.save(regex);
                });
        // 修改
        groupInfo.getActionFilterRegexList().stream()
                .filter(filterVO -> isUpdateGroupRegex(regexList, filterVO))
                .forEach(filterVO -> {
                    TActionFilterGroupRegex regex = new TActionFilterGroupRegex();
                    regex.setId(filterVO.getId());
                    regex.setActionId(filterVO.getActionId());
                    regex.setActionFilterRegex(filterVO.getRegex());
                    regex.setActionType(ActionFilterTypeEnum.FILTER);
                    regex.setUpdateTime(new Date());
                    actionFilterGroupRegexMapper.updateByPrimaryKeySelective(regex);
                });

        // 获取指定行为函数的ID
//        List<Long> appointRegexIds = groupInfo.getActionAppointRegexList().stream().map(ActionFilterVO::getId).collect(Collectors.toList());
//        // 指定函数-删除
//        regexList.stream()
//                .filter(r -> !appointRegexIds.contains(r.getId()))
//                .forEach(r -> {
//                    TActionFilterGroupRegex updateRegex = new TActionFilterGroupRegex();
//                    updateRegex.setId(r.getId());
//                    updateRegex.setStatus(StatusEnum.DELETE.itemValue());
//                    actionFilterGroupRegexMapper.updateByPrimaryKeySelective(updateRegex);
//                });
        
        // 指定函数-新增
        groupInfo.getActionAppointRegexList().stream()
                .filter(filterVO -> regexList.stream().noneMatch(r -> r.getId().equals(filterVO.getId())))
                .forEach(filterVO -> {
                    TActionFilterGroupRegex regex = gettActionFilterGroupRegex(filterVO, groupInfo.getGroupId(), ActionFilterTypeEnum.APPOINT);
                    actionFilterGroupRegexMapper.save(regex);
                });
        
        // 指定函数-修改
        groupInfo.getActionAppointRegexList().stream()
                .filter(filterVO -> isUpdateGroupRegex(regexList, filterVO))
                .forEach(filterVO -> {
                    TActionFilterGroupRegex regex = new TActionFilterGroupRegex();
                    regex.setId(filterVO.getId());
                    regex.setActionId(filterVO.getActionId());
                    regex.setActionFilterRegex(filterVO.getRegex());
                    regex.setActionType(ActionFilterTypeEnum.APPOINT);
                    regex.setUpdateTime(new Date());
                    actionFilterGroupRegexMapper.updateByPrimaryKeySelective(regex);
                });
    }

    private void updateGroupUser(List<Long> userIds, ActionFilterGroupSave groupInfo) {
        TActionFilterGroupUser userQuery = new TActionFilterGroupUser();
        userQuery.setGroupId(groupInfo.getGroupId());
        List<TActionFilterGroupUser> groupUserList = actionFilterGroupUserMapper.select(userQuery);
        groupUserList.stream()
                .filter(u -> !userIds.contains(u.getUserId()))
                .forEach(u -> {
                    actionFilterGroupUserMapper.delete(u);
                });
        userIds.stream()
                .filter(userId -> groupUserList.stream().noneMatch(u -> u.getUserId().equals(userId)))
                .forEach(userId -> {
                    TActionFilterGroupUser groupUser = new TActionFilterGroupUser();
                    groupUser.setGroupId(groupInfo.getGroupId());
                    groupUser.setUserId(userId);
                    actionFilterGroupUserMapper.save(groupUser);
                });
    }

    @NotNull
    private static TActionFilterGroupRegex gettActionFilterGroupRegex(ActionFilterVO filterVO, Long groupId, ActionFilterTypeEnum actionType) {
        TActionFilterGroupRegex regex = new TActionFilterGroupRegex();
        regex.setGroupId(groupId);
        regex.setActionId(filterVO.getActionId());
        regex.setActionFilterRegex(filterVO.getRegex());
        regex.setActionFilterMode(ActionFilterModeEnum.equals);
        regex.setActionFilterField(ActionFilterFieldEnum.stackInfo);
        regex.setStatus(StatusEnum.NORMAL.itemValue());
        regex.setActionType(actionType == null ? ActionFilterTypeEnum.FILTER : actionType);
        regex.setUpdateTime(new Date());
        regex.setCreateTime(new Date());
        return regex;
    }

    @Transactional
    @Override
    public void delete(IUser user, Long groupId, boolean isAdmin) {
        TActionFilterGroup groupQuery = new TActionFilterGroup();
        groupQuery.setId(groupId);
        TActionFilterGroup group = actionFilterGroupMapper.selectOne(groupQuery);
        if (Objects.isNull(group)) {
            throw new IllegalArgumentException("行为过滤集合不存在，请检查groupId");
        }
        if (!isAdmin && !Objects.equals(group.getCreateUserId(), user.getUserId())) {
            throw new IllegalArgumentException("不能删除其他用户的集合");
        }
        if (group.getStatus() != ActionFilterGroupStatusEnum.DELETE) {
            TActionFilterGroup groupDelete = new TActionFilterGroup();
            groupDelete.setId(groupId);
            groupDelete.setStatus(ActionFilterGroupStatusEnum.DELETE);
            groupDelete.setUpdateTime(new Date());
            actionFilterGroupMapper.updateByPrimaryKeySelective(groupDelete);
        }
    }

    @Transactional
    @Override
    public void actionFilterGroupRegexUpload(IUser user, MultipartFile file) throws Exception {
        File zipFile = null;
        String uncompress = null;
        try {
            zipFile = saveFile(file);
            String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
            uncompress = CommonUtil.uncompress(zipFile.getAbsolutePath(), dynamicPath + UuidUtil.uuid());
            Collection<File> jsonFiles = FileUtils.listFiles(new File(uncompress), new String[] {"json"}, true);
            for (File jsonFile:jsonFiles) {
                String json = FileUtils.readFileToString(jsonFile, "UTF-8");
                List<ActionFilterGroupRelRegexListVO> voList = CommonUtil.jsonToBean(json, new TypeReference<List<ActionFilterGroupRelRegexListVO>>() {});
                for (ActionFilterGroupRelRegexListVO vo:voList) {
                    TActionFilterGroup insertGroup = getActionFilterGroup(vo.getGroupName(), vo.getDescription(),
                            vo.getTerminalType(), vo.getUserNames(), user.getUserId());
                    if (vo.getIsMain()) {
                        insertGroup.setStatus(ActionFilterGroupStatusEnum.MAIN);
                    }
                    actionFilterGroupMapper.save(insertGroup);
                    if (StringUtils.isNotBlank(vo.getUserNames())) {
                        List<Long> userIds = Arrays.stream(vo.getUserNames().split(";"))
                                .map(userName -> userQueryMapper.selectUsersByUserName(userName.trim(), null))
                                .filter(Objects::nonNull)
                                .map(User::getUserId)
                                .collect(Collectors.toList());
                        // 关联用户
                        for (Long userId:userIds) {
                            TActionFilterGroupUser groupUser = new TActionFilterGroupUser();
                            groupUser.setGroupId(insertGroup.getId());
                            groupUser.setUserId(userId);
                            actionFilterGroupUserMapper.save(groupUser);
                        }
                    }
                    // 添加过滤规则
                    for (ActionFilterVO filterVO:vo.getActionFilterVOList()) {
                        TActionFilterGroupRegex regex = gettActionFilterGroupRegex(filterVO, insertGroup.getId(), ActionFilterTypeEnum.FILTER);
                        actionFilterGroupRegexMapper.save(regex);
                    }
                    
                    // 添加指定函数规则
                    for (ActionFilterVO filterVO:vo.getActionAppointVOList()) {
                        TActionFilterGroupRegex regex = gettActionFilterGroupRegex(filterVO, insertGroup.getId(), ActionFilterTypeEnum.APPOINT);
                        actionFilterGroupRegexMapper.save(regex);
                    }
                }
            }
        } finally {
            if (zipFile != null) {
                CommonUtil.deleteFile(zipFile.getAbsolutePath());
            }
            if (uncompress != null) {
                CommonUtil.deleteFile(uncompress);
            }
        }
    }

    @NotNull
    private static TActionFilterGroup getActionFilterGroup(String groupName, String desc, TerminalTypeEnum terminalType, String userNames, Long createUserId) {
        TActionFilterGroup insertGroup = new TActionFilterGroup();
        insertGroup.setName(groupName);
        insertGroup.setDescription(desc);
        insertGroup.setTerminalType(terminalType);
        insertGroup.setCategory(StringUtils.isBlank(userNames) ? ActionFilterGroupCategoryEnum.ALL_USER : ActionFilterGroupCategoryEnum.SPECIFIC_USER);
        insertGroup.setStatus(ActionFilterGroupStatusEnum.NORMAL);
        insertGroup.setUpdateTime(new Date());
        insertGroup.setCreateTime(new Date());
        insertGroup.setCreateUserId(createUserId);
        return insertGroup;
    }

    private File saveFile(MultipartFile data) throws IOException {
        String dynamicPath = commonProperties.getProperty("detection.tools.dynamic_path");
        File file = new File(dynamicPath + UuidUtil.uuid() + ".zip");
        if (data == null) {
            throw new FileNotFoundException("上传的文件不存在");
        } else {
            data.transferTo(file);
            if (!file.getParentFile().exists()) {
                boolean mkdirs = file.getParentFile().mkdirs();
                if (!mkdirs) {
                    throw new FileNotFoundException("无法创建文件夹");
                }
            }
        }
        return file;
    }

    @Override
    public List<ActionFilterGroupRelRegexListVO> export() {
        return actionFilterGroupMapper.findGroupList().stream().map(group -> {
            ActionFilterGroupRelRegexListVO vo = new ActionFilterGroupRelRegexListVO();
            BeanUtils.copyProperties(group, vo);
            TActionFilterGroupRegex regexQuery = new TActionFilterGroupRegex();
            regexQuery.setGroupId(group.getGroupId());
            regexQuery.setStatus(StatusEnum.NORMAL.itemValue());
            vo.setActionFilterVOList(actionFilterGroupRegexMapper.select(regexQuery).stream().map(r -> {
                ActionFilterVO filterVO = new ActionFilterVO();
                filterVO.setId(r.getId());
                filterVO.setActionId(r.getActionId());
                filterVO.setRegex(r.getActionFilterRegex());
                return filterVO;
            }).collect(Collectors.toList()));
            return vo;
        }).collect(Collectors.toList());
    }

}
