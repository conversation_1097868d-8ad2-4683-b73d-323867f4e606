package cn.ijiami.detection.service.impl;

import java.util.Date;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.VO.SensitiveTypeVO;
import cn.ijiami.detection.entity.TSensitiveType;
import cn.ijiami.detection.entity.TSensitiveWord;
import cn.ijiami.detection.enums.SensitiveTypeEnum;
import cn.ijiami.detection.mapper.TSensitiveTypeMapper;
import cn.ijiami.detection.mapper.TSensitiveWordMapper;
import cn.ijiami.detection.service.api.ISensitiveTypeService;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;

/**
 * 敏感词汇分类接口实现类
 *
 * <AUTHOR>
 */
@Service
@Transactional
@CacheConfig(cacheNames = {"privacy-detection:sensitiveType"})
public class SensitiveTypeServiceImpl implements ISensitiveTypeService {

    @Autowired
    private TSensitiveTypeMapper sensitiveTypeMapper;

    @Autowired
    private TSensitiveWordMapper tSensitiveWordMapper;

    @Override
    public PageInfo<TSensitiveType> findSensitiveTypeByPage(TSensitiveType sensitiveType) {
        // 此处缓存会造成新增数据统计展示不准确
        if (sensitiveType.getPage() != null && sensitiveType.getRows() != null) {
            PageHelper.startPage(sensitiveType.getPage(), sensitiveType.getRows());
        }
        List<TSensitiveType> sensitiveTypeList = sensitiveTypeMapper.selectSensitiveTypeList(sensitiveType);
        return new PageInfo<>(sensitiveTypeList);
    }

    @Override
    @CacheEvict(value = "privacy-detection:sensitiveType", allEntries = true)
    public int addSensitiveType(TSensitiveType sensitiveType) {
        Date dateTime = new Date();
        sensitiveType.setUpdateTime(dateTime);
        sensitiveType.setCreateTime(dateTime);
        sensitiveType.setSensitiveType(SensitiveTypeEnum.DIY);
        return sensitiveTypeMapper.insert(sensitiveType);
    }

    @Override
    @CacheEvict(value = "privacy-detection:sensitiveType", allEntries = true)
    public int deleteSensitiveTypeById(Long sensitiveTypeId) throws IjiamiApplicationException {
        TSensitiveWord query = new TSensitiveWord();
        query.setTypeId(sensitiveTypeId);
        if (CollectionUtils.isNotEmpty(tSensitiveWordMapper.findSensitiveWord(query))) {
            throw new IjiamiApplicationException("请先删除词汇");
        }
        return sensitiveTypeMapper.deleteByPrimaryKey(sensitiveTypeId);
    }

    @Override
    public List<TSensitiveType> findSensitiveTypeList(TSensitiveType sensitiveType) {
        if (sensitiveType == null) {
            return sensitiveTypeMapper.selectAll();
        }
        return sensitiveTypeMapper.selectSensitiveTypeList(sensitiveType);
    }

    @Override
    public List<SensitiveTypeVO> findByTaskId(Long taskId) {
        return sensitiveTypeMapper.findByTaskId(taskId);
    }

}
