package cn.ijiami.detection.bigdataLoginTools;

import org.springframework.beans.factory.annotation.Value;

import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedDeque;

/**
 * <AUTHOR>
 * @description 内存数据容器
 * @date 19.8.1
 **/
public class DataContainer {

    private static DataContainer container;

    private DataContainer() {
    }

    public static DataContainer getInstance() {
        if (container == null) {
            return new DataContainer();
        } else {
            return container;
        }
    }

    /**
     * 检测队列
     */
    public final static Queue<Object> detectionQueue = new ConcurrentLinkedDeque<>();

    /**
     * 调用Token map
     */
    public final static ConcurrentHashMap<String, String> tokenMap = new ConcurrentHashMap<>();
    

    public final static String CONTENT_TYPE_JSON = "application/json;charset=utf-8";

    @Value("${ijiami.ccrc.project.url}")
    public final static String PROJECT_URL="";
}
