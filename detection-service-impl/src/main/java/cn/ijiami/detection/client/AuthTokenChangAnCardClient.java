package cn.ijiami.detection.client;

import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.web.controller.BaseController;

/**
 * 统一认证接口客户端 - 长安汽车
 */
@Component
public class AuthTokenChangAnCardClient extends BaseController {

    private static final Logger logger= LoggerFactory.getLogger(AuthTokenChangAnCardClient.class);

    @Value("${ijiami.auth.client_id:}")
    private String clientId;
    
    @Value("${ijiami.auth.client_secret:}")
    private String clientSecret;
    
    @Value("${changan.apim.appcode.key:}")
    private String appCode;
    
    @Value("${ijiami.auth.api.token:}")
    private String apiAuthToken;

    @Value("${ijiami.auth.authorize.url:}")
    private String authorizeUrl;

//    @Value("${server.port:}")
//    private String port;
//
//    @Value("${server.contextPath:}")
//    private String path;
//
//    @Value("${ijiami.auth.host:}")
//    private String host;
//    @Autowired
//    private RestTemplate restTemplate;


    /**
     * 根据应用信息获取长安token
     * @return
     * @throws IjiamiApplicationException 
     */
    public String applicationAuthenticate() throws IjiamiApplicationException{
		String url = "%s/ichangan/rescenter/rest/resRestApi/v2/applicationAuthenticate?appId=%s&secret=%s";
		url = String.format(url, authorizeUrl, clientId, clientSecret);
		logger.info("applicationAuthenticate.url={}", url);
		Map<String, String> headParam = new HashMap<String, String>();
		headParam.put("apim-appcode-key", appCode);
		String result = cn.ijiami.detection.utils.HttpUtils.httpGetPageContent(url, headParam);
		logger.info("applicationAuthenticate.result={}", result);
    	return result;
    }
    
    /**
     * 根据loginId获取到用户信息
     * @param loginId
     * @return
     * @throws IjiamiApplicationException 
     */
    public JSONObject getUserListByLoginIds(String loginId, String identityToken) throws IjiamiApplicationException{
		String url = "%s/ichangan/rescenter/rest/resRestApi/v2/getUserListByLoginIds?identityToken=%s&userLoginIdList=%s";
		url = String.format(url, authorizeUrl, identityToken, loginId);
		logger.info("getUserListByLoginIds.url={}", url);
		Map<String, String> headParam = new HashMap<String, String>();
		headParam.put("apim-appcode-key", appCode);
		String result = cn.ijiami.detection.utils.HttpUtils.httpGetPageContent(url, headParam);
		JSONObject json = JSONObject.parseObject(result);
		logger.info("getUserListByLoginIds.result={}", result);
		return json;
    }

    
    public static void main(String[] args) {
//    	String url = "https://apitest.changan.com.cn:30598/ichangan/rescenter/rest/resRestApi/v2/getUserByToken?identityToken=67a8be25-2505-429f-9834-a5d41f76adc0";
//    	String result = null;
//		try {
//			result = cn.ijiami.detection.utils.HttpUtils.httpGetPageContent(url, null);
//		} catch (IjiamiApplicationException e) {
//			e.printStackTrace();
//		}
//    	System.out.println("result:"+result);
//    	
//    	String appId = "6cdfc489-29ec-4992-9177-16c93a72baf8";
//    	String secret = "a1fb1254-3779-4c75-8337-b92d4215794f";
//    	String url2 = "https://apitest.changan.com.cn:30598/ichangan/rescenter/rest/resRestApi/v2/applicationAuthenticate?appId=%s&secret=%s"; 
//    	String identityToken = "dd3e9ddc-3192-4005-a6d5-da5c467b6d07";
//    	try {
//    		url2 = String.format(url2, appId, secret);
//    		System.out.println(url2);
//    		Map<String, String> headParam = new HashMap<String, String>();
//    		headParam.put("apim-appcode-key", "6721d31147b2ca02188c9319");
//			String result = cn.ijiami.detection.utils.HttpUtils.httpGetPageContent(url2, headParam);
//			JSONObject json = JSONObject.parseObject(result);
//			if(json.get("result") != null && json.getString("result").equals("200")) {
//				identityToken = json.getString("data");
//			}
//			System.out.println(result);
//		} catch (IjiamiApplicationException e) {
//			e.printStackTrace();
//		}
    	
    	
    	
//    	url2 = "https://apitest.changan.com.cn:30598/ichangan/rescenter/rest/resRestApi/v2/getUserListByLoginIds?identityToken=%s&userLoginIdList=%s";
//    	try {
//    		String userLoginIdList = "APP1729654996546071";
//    		url2 = String.format(url2, identityToken, userLoginIdList);
//    		System.out.println(url2);
//    		
//    		Map<String, String> headParam = new HashMap<String, String>();
//    		headParam.put("apim-appcode-key", "6721d31147b2ca02188c9319");
//			String result = cn.ijiami.detection.utils.HttpUtils.httpGetPageContent(url2, headParam);
//			System.out.println(result);
//			JSONObject json = JSONObject.parseObject(result);
//			if(json.getJSONArray("data") != null && json.getJSONArray("data").size()>0) {
//				ChangAnUserData userGet = JSON.toJavaObject(json.getJSONArray("data").getJSONObject(0), ChangAnUserData.class);
//				System.out.println(JSONObject.toJSONString(userGet));
//			}
//		} catch (IjiamiApplicationException e) {
//			e.printStackTrace();
//		}
    	
//    	url2 = "https://apitest.changan.com.cn:30598/ichangan/rescenter/rest/resRestApi/v2/getUserByToken?identityToken=%s";
//    	try {
//    		String userLoginIdList = "";
//    		url2 = String.format(url2, identityToken, null);
//    		System.out.println(url2);
//    		
//    		Map<String, String> headParam = new HashMap<String, String>();
//    		headParam.put("apim-appcode-key", "6721d31147b2ca02188c9319");
//			String result = cn.ijiami.detection.utils.HttpUtils.httpGetPageContent(url2, headParam);
//			System.out.println(result);
//		} catch (IjiamiApplicationException e) {
//			e.getMessage();
//		}
    	
	}
}
