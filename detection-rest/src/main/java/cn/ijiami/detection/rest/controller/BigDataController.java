package cn.ijiami.detection.rest.controller;

import static cn.ijiami.detection.utils.CommonUtil.reportZipName;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.ijiami.base.common.log.OperateLog;
import cn.ijiami.base.common.user.IUser;
import cn.ijiami.detection.VO.BigDataVO;
import cn.ijiami.detection.VO.DetectResultVO;
import cn.ijiami.detection.VO.ReportResultVO;
import cn.ijiami.detection.VO.TaskDetailVO;
import cn.ijiami.detection.VO.bigdata.BigDataConnectVO;
import cn.ijiami.detection.VO.bigdata.DetectionResultVO;
import cn.ijiami.detection.VO.ccrc.CCRCDTO;
import cn.ijiami.detection.VO.ccrc.CCRCVO;
import cn.ijiami.detection.config.IjiamiCommonProperties;
import cn.ijiami.detection.controller.DetectionBaseController;
import cn.ijiami.detection.entity.TAssets;
import cn.ijiami.detection.entity.TTask;
import cn.ijiami.detection.enums.DynamicAutoStatusEnum;
import cn.ijiami.detection.enums.DynamicDeviceTypeEnum;
import cn.ijiami.detection.enums.PageOrApiOrKafkaEnum;
import cn.ijiami.detection.query.task.TaskCreateQuery;
import cn.ijiami.detection.service.api.IAssetsService;
import cn.ijiami.detection.service.api.ICommonMongodbService;
import cn.ijiami.detection.service.api.IPrivacyCheckService;
import cn.ijiami.detection.service.api.IPrivacyDetectionService;
import cn.ijiami.detection.service.api.ITaskService;
import cn.ijiami.detection.utils.CommonUtil;
import cn.ijiami.detection.utils.ConstantsUtils;
import cn.ijiami.detection.utils.HttpUtils;
import cn.ijiami.detection.utils.MD5Util;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.exception.IjiamiCommandException;
import cn.ijiami.framework.common.response.BaseResponse;
import cn.ijiami.framework.file.service.api.IFileService;
import cn.ijiami.framework.file.vo.FileVO;
import cn.ijiami.framework.kit.utils.UuidUtil;
import cn.ijiami.manager.user.entity.User;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2019/9/26 09:39
 */
@Slf4j
@Api(tags = "大数据对接专用")
@RestController
@RequestMapping("/api/bigdata")
public class BigDataController extends DetectionBaseController{

    private final IjiamiCommonProperties commonProperties;
    private final IPrivacyDetectionService privacyDetectionService;
    private final IPrivacyCheckService privacyCheckService;
    private final ITaskService taskService;
    @Autowired
    private IAssetsService assetsService;
    @Autowired
    private IFileService defaultService;
    @Autowired
	private ICommonMongodbService commonMongodbService;

    public BigDataController(IjiamiCommonProperties commonProperties,
                             IPrivacyDetectionService privacyDetectionService,
                             IPrivacyCheckService privacyCheckService, ITaskService taskService) {
        this.commonProperties = commonProperties;
        this.privacyDetectionService = privacyDetectionService;
        this.privacyCheckService = privacyCheckService;
        this.taskService = taskService;
    }

    @GetMapping("/md5/list")
    @OperateLog(
            moduleName ="大数据业务API接口",
            operateName = "用户通过大数据业务API接口获取资产MD5信息"
    )
    public BaseResponse<List<BigDataVO>> md5() {
        BaseResponse<List<BigDataVO>> baseResponse = new BaseResponse<>();
        baseResponse.setData(taskService.findMd5());
        return baseResponse;
    }

    @ApiOperation(value = "查询检测任务的法律法规类型", notes = "1:自评估指南    2:GB/T 35273   3:工信部337号文")
    @GetMapping("/countType/{md5}")
    @OperateLog(
            moduleName ="大数据业务API接口",
            operateName = "用户通过大数据业务API接口根据资产MD5信息查询检测任务的法律法规类型"
    )
    public BaseResponse<List<Integer>> countType(@PathVariable String md5) throws IjiamiApplicationException {
        BaseResponse<List<Integer>> baseResponse = new BaseResponse<>();
        TTask task = taskService.findDocumentId(md5);
        if (task == null) {
            throw new IjiamiApplicationException("无效的MD5");
        }
        baseResponse.setData(privacyCheckService.countType(task.getTaskId()));
        return baseResponse;
    }

    @ApiOperation(value = "报告下载", notes = "报告下载")
    @GetMapping(value = "/downloadReport")
    @OperateLog(
            moduleName ="大数据业务API接口",
            operateName = "大数据接口报告下载"
    )
    public void downloadReport(@ApiParam(value = "MD5") @RequestParam("md5") String md5,
                               @ApiParam(value = "法律法规类型 1.自评估指南 2.GB/T 35273") @RequestParam("type") Integer[] type,
                               @ApiParam(value = "报告文件类型 1.word 2.pdf") @RequestParam("reportType") Integer[] reportType,
                               @ApiParam(value = "模板类型 1.监管者 2.开发者") @RequestParam("reportObject") Integer[] reportObject,
                               @ApiParam(value = "终端类型1.Android 2.ios")@RequestParam("terminalTypeEnum") Integer terminalTypeEnum,
                               HttpServletRequest request, HttpServletResponse response) throws IjiamiApplicationException {

        InputStream in = null;
        OutputStream out = null;
        File file;
        ReportResultVO report = null;

        TTask task = taskService.findDocumentId(md5);
        if (task == null) {
            throw new IjiamiApplicationException("无效的MD5");
        }

        try {
            if (type.length == 1 && reportType.length == 1 && reportObject.length == 1) {
                //单个下载
                report = privacyDetectionService.downloadReport(task.getApkDetectionDetailId(), type[0], reportType[0], reportObject[0], null,terminalTypeEnum);
                file = report.report();
            } else {
                //批量下载
                String reportRootPath = commonProperties.getProperty("ijiami.report.root.path");
                File reportDir = new File(reportRootPath + "out" + File.separator + UuidUtil.uuid());

                reportDir.mkdirs();
                for (Integer t : type) {
                    for (Integer r : reportType) {
                        for (Integer o : reportObject) {
                            report = privacyDetectionService.downloadReport(task.getApkDetectionDetailId(), t, r, o, null,terminalTypeEnum);
                            FileUtils.copyFileToDirectory(report.report(), reportDir);
                        }
                    }
                }
                String zipName = reportZipName(report);
                String compress = CommonUtil.compress(reportDir.getAbsolutePath(), reportDir.getParent() + File.separator + zipName);
                file = new File(compress);
            }

            String fileName;
            //发送信息给前端
            // 返回给浏览器

            if (file.exists()) {
                // 获取文件名称
                fileName = file.getName().replace(" ", "");
                // 文件名乱码问题
                // IE的话，通过URLEncoder对filename进行UTF8编码。而其他的浏览器（firefox、chrome、safari、opera），则要通过字节转换成ISO8859-1了
                String header = request.getHeader("User-Agent").toUpperCase();
                if (header.contains("MSIE") || header.contains("TRIDENT") || header.contains("EDGE")) {
                    fileName = URLEncoder.encode(fileName, "utf-8");
                    // IE下载文件名空格变+号问题
                    fileName = fileName.replace("+", "%20");
                } else {
                    fileName = new String(fileName.getBytes(StandardCharsets.UTF_8), "ISO8859-1");
                }
                // 设置数据种类
                response.setContentType("application/octet-stream");
                response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
                response.setHeader("X-Frame-OPTIONS", "ALLOW-FROM");
                // 获取返回体输出权
                in = new FileInputStream(file);
                out = response.getOutputStream();
                // 写文件
                IOUtils.copy(in, out);
            }
        } catch (Exception e) {
            log.error("下载报表失败", e);
        } finally {
            try {
                if (in != null) {
                    in.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (Exception e) {
                log.error("关闭流失败", e);
            }
        }
    }

    @ApiOperation(value = "查询已完成的任务列表")
    @GetMapping("/tasks")
    @OperateLog(
            moduleName ="大数据业务API接口",
            operateName = "查询已完成的任务列表"
    )
    public BaseResponse<List<TTask>> findDetectedTask(@ApiParam(value = "用户id") @RequestParam(value = "userId", required = false) Long userId) {
        BaseResponse<List<TTask>> baseResponse = new BaseResponse<>();
        baseResponse.setData(taskService.findDetectedTasks(userId));
        return baseResponse;
    }

    @ApiOperation(value = "查询全量检测结果")
    @GetMapping("/result")
    @OperateLog(
            moduleName ="大数据业务API接口",
            operateName = "用户通过大数据业务API接口查询全量检测结果"
    )
    public BaseResponse<List<DetectResultVO>> findAllResult(@ApiParam(value = "mongo主键") @RequestParam(value = "documentId", required = false) String documentId,
                                                            @ApiParam(value = "用户id") @RequestParam(value = "userId", required = false) Long userId) throws IjiamiApplicationException {
        BaseResponse<List<DetectResultVO>> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacyDetectionService.getDetectResults(documentId, userId));
        return baseResponse;
    }

    @ApiOperation(value = "查询已完成的任务列表")
    @GetMapping("/listDocumentId")
    @OperateLog(
            moduleName ="大数据业务API接口",
            operateName = "用户通过大数据业务API接口查询已完成的任务列表"
    )
    public BaseResponse<List<String>> listDocumentId(@ApiParam(value = "时间戳") @RequestParam(value = "beginTime", required = false) String beginTime,
                                                       @RequestParam(value = "endTime", required = false) String endTime) {
        BaseResponse<List<String>> baseResponse = new BaseResponse<>();
        baseResponse.setData(taskService.findDetectedTasksByTime(beginTime, endTime));
        return baseResponse;
    }

    @ApiOperation(value = "获取完整的检测结果")
    @GetMapping("/getDetectedResult")
    @OperateLog(
            moduleName ="大数据业务API接口",
            operateName = "用户通过大数据业务API接口获取完整的检测结果"
    )
    public BaseResponse<BigDataConnectVO> getBigDataConnect(@ApiParam(value = "已完成全部检测的任务文档Id") @RequestParam(value = "documentId") String documentId)
            throws IjiamiApplicationException {
        BaseResponse<BigDataConnectVO> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacyDetectionService.getBigDataConnect(documentId));
        return baseResponse;
    }
    
    @ApiOperation(value = "NEW获取完整的检测结果")
    @GetMapping("/v1/getDetectionResult")
    @OperateLog(
            moduleName ="大数据业务API接口",
            operateName = "用户通过大数据业务API接口获取完整的检测结果"
    )
    public BaseResponse<BigDataConnectVO> getDetectionResult(@ApiParam(value = "已完成全部检测的任务文档Id") @RequestParam(value = "documentId") String documentId)
            throws IjiamiApplicationException {
        BaseResponse<BigDataConnectVO> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacyDetectionService.getBigDataConnect(documentId));
        return baseResponse;
    }
    
    
    /**
     * 上传文件并检测
     * @param
     * @return
     * @throws IjiamiCommandException
     */
    @ApiOperation(value = "url上传apk并执行检测")
    @PostMapping("/file/detect")
    @OperateLog(
            moduleName ="大数据业务API接口",
            operateName = "用户通过大数据业务API接口上传URL资产并执行检测"
    )
    public BaseResponse<CCRCVO> fileUploadAndDetect(@RequestParam("file") MultipartFile file, 
    		@RequestParam(value = "appId", required = false) String appId,
    		@RequestParam(value = "md5", required = false) String md5) throws IjiamiCommandException {
        BaseResponse<CCRCVO> baseResponse = new BaseResponse<>();
        try {

            IUser currentUser = getCurrentUser();
            User user = new User();
            user.setUserName(currentUser.getUsername());
            user.setUserId(currentUser.getUserId());
            
            FileVO fileVO = new FileVO();
            // 上传前数据拼装，校验
            beforUpload(fileVO, file);
            // 执行上传
            uploade(fileVO);

            TAssets assets = assetsService.analysisApk(fileVO, false, user, false);
            assets.setAppId(appId);
            // 2.6.2版本需要把api上传接口的源文件名也保存
            assets.setSourceFileName(file.getOriginalFilename().trim());
            assetsService.saveOrUpdate(assets);
            
            TTask task =  taskService.findDetectionCompleteByMd5(assets.getMd5());
            if(task!= null) {
            	log.info("应用已经检测过md5={},documentId=",assets.getMd5(),task.getApkDetectionDetailId());
            	CCRCVO ccrcvo = new CCRCVO();
                ccrcvo.setDocumentId(task.getApkDetectionDetailId());
                baseResponse.setData(ccrcvo);
                return baseResponse;
            }
            
            //添加检测任务
            TaskCreateQuery taskCreateQuery = new TaskCreateQuery();
            taskCreateQuery.setUserId(currentUser.getUserId());
            List<Long> assetsIds = new ArrayList<>();
            assetsIds.add(assets.getId());
            taskCreateQuery.setAssetsIds(assetsIds);
//            taskCreateQuery.setTemplateId(1L);
            taskCreateQuery.setDynamicDeviceTypeEnum(DynamicDeviceTypeEnum.getItem(1)); //1 云手机
            taskCreateQuery.setTerminalTypeEnum(assets.getTerminalType());
            taskCreateQuery.setDetectionType(1);
            Long taskId = taskService.startTask(taskCreateQuery);

            task = taskService.findById(taskId);

            CCRCVO ccrcvo = new CCRCVO();
            ccrcvo.setDocumentId(task.getApkDetectionDetailId());
            baseResponse.setData(ccrcvo);
        } catch (Exception e) {
            e.getMessage();
            log.error("bigData:apk下载失败");
            baseResponse.setStatus(500);
            baseResponse.setMessage("apk下载失败。");
        }
        return baseResponse;
    }
    
    
    
    /**
     * url下载并检测
     * @param ccrcdto
     * @return
     * @throws IjiamiCommandException
     */
//    @ApiOperation(value = "url上传apk并执行检测")
//    @PostMapping("/url/detect")
//    @OperateLog(
//            moduleName ="大数据业务API接口",
//            operateName = "上传URL资产并执行检测",
//            value = "用户通过大数据业务API接口上传URL资产并执行检测"
//    )
//    public BaseResponse<CCRCVO> UrlUploadAndDetect(@Validated @RequestBody CCRCDTO ccrcdto) throws IjiamiCommandException {
//        BaseResponse<CCRCVO> baseResponse = new BaseResponse<>();
//        try {
//        	log.info("请求参数={}",JSON.toJSONString(ccrcdto));
//
//            IUser currentUser = getCurrentUser();
//            User user = new User();
//            user.setUserName(currentUser.getUsername());
//            user.setUserId(currentUser.getUserId());
//            
//            TTask task =  taskService.findDetectionCompleteByMd5(ccrcdto.getMd5());
//            if(task!= null) {
//            	log.info("应用已经检测过md5={},documentId=",ccrcdto.getMd5(),task.getApkDetectionDetailId());
//            	CCRCVO ccrcvo = new CCRCVO();
//                ccrcvo.setApkId(ccrcdto.getApkId());
//                ccrcvo.setDocumentId(task.getApkDetectionDetailId());
//                baseResponse.setData(ccrcvo);
//                return baseResponse;
//            }
//            
//            TAssets assets = new TAssets();
//            assets.setUpdateUserId(currentUser.getUserId());
//            assets.setCreateUserId(currentUser.getUserId());
//            assets.setShellIpaPath(ccrcdto.getDownloadUrl());
//            assets.setCreateTime(new Date());
//            assets.setMd5(ccrcdto.getMd5());
//            assets.setName(ccrcdto.getAppName());
//            assets.setPakage(ccrcdto.getPackageName());
//            assets.setVersion(ccrcdto.getAppVersion());
//            assets.setSize(ccrcdto.getSize());
//            assets.setTerminalType(ccrcdto.getTerminalType()==null?TerminalTypeEnum.ANDROID:TerminalTypeEnum.getItem(ccrcdto.getTerminalType()));
//            assets.setAppId(ccrcdto.getAppId());
//            assets = assetsService.saveOrUpdate(assets);
//            
//            //添加检测任务
//            TaskCreateQuery taskCreateQuery = new TaskCreateQuery();
//            taskCreateQuery.setUserId(currentUser.getUserId());
//            List<Long> assetsIds = new ArrayList<>();
//            assetsIds.add(assets.getId());
//            taskCreateQuery.setAssetsIds(assetsIds);
//            taskCreateQuery.setDynamicDeviceTypeEnum(DynamicDeviceTypeEnum.getItem(1)); //1 云手机
//            taskCreateQuery.setTerminalTypeEnum(ccrcdto.getTerminalType()==null?TerminalTypeEnum.ANDROID:TerminalTypeEnum.getItem(ccrcdto.getTerminalType()));
//            taskCreateQuery.setDetectionType(1);
//            Long taskId = taskService.startTask(taskCreateQuery);
//
//            task = taskService.findById(taskId);
//
//            CCRCVO ccrcvo = new CCRCVO();
//            ccrcvo.setApkId(ccrcdto.getApkId());
//            ccrcvo.setDocumentId(task.getApkDetectionDetailId());
//            baseResponse.setData(ccrcvo);
//        } catch (Exception e) {
//            e.printStackTrace();
//            log.error("bigData:apk下载失败");
//            baseResponse.setStatus(500);
//            baseResponse.setMessage("apk下载失败。");
//        }
//        return baseResponse;
//    }
    
    
    
    @ApiOperation(value = "url上传apk并执行检测")
    @PostMapping("/url/detect")
    @OperateLog(
            moduleName ="大数据业务API接口",
            operateName = "用户通过大数据业务API接口上传URL资产并执行检测"
    )
    public BaseResponse<Object> uploadDectByUrl(@Validated @RequestBody CCRCDTO ccrcdto) throws IjiamiCommandException {
        BaseResponse<Object> baseResponse = new BaseResponse<>();
        String filePath = ccrcdto.getDownloadUrl();
        try {
        	filePath =  new String(filePath.getBytes("iso-8859-1"),"utf-8");
		} catch (Exception e1) {
			e1.getMessage();
		}
        
        log.info("URL上传apk并执行检测参数.bussinessId={},md5={},appId={},filePath={},callbackUrl={}",ccrcdto.getApkId(),ccrcdto.getMd5(),ccrcdto.getAppId(),filePath,ccrcdto.getCallbackUrl());
        
        TTask task =  taskService.findDetectionCompleteByMd5(ccrcdto.getMd5());
        if(task!= null) {
        	log.info("应用已经检测过md5={},documentId=",ccrcdto.getMd5(),task.getApkDetectionDetailId());
        	CCRCVO ccrcvo = new CCRCVO();
            ccrcvo.setApkId(ccrcdto.getApkId());
            ccrcvo.setDocumentId(task.getApkDetectionDetailId());
            baseResponse.setData(ccrcvo);
            return baseResponse;
        }
        
        String localPath = commonProperties.getFilePath() + File.separator + "default" + File.separator + UuidUtil.uuid() + ".apk";
        if(filePath.contains(".ipa")|| filePath.contains(".IPA")){
        	localPath = commonProperties.getFilePath() + File.separator + "default" + File.separator + UuidUtil.uuid() + ".ipa";
        }
        
        File file = new File(localPath);
        try {
        	if(filePath.startsWith("https")){
        		HttpUtils.download(filePath, localPath, null);
        	}
        	
        	if(!new File(localPath).exists() && filePath.contains("http")) {
        		FileUtils.copyURLToFile(new URL(filePath), file);
        	}
        	
        	if(!new File(localPath).exists()){
        		throw new IjiamiApplicationException("文件下载异常！");
        	}
        	
            IUser currentUser =getCurrentUser();
            User user = new User();
            Long logUserId = currentUser.getUserId();
            String name = currentUser.getUsername();
            user.setUserName(name);
            user.setUserId(logUserId);
            
            FileVO fileVO = new FileVO();
            // 上传前数据拼装，校验
            beforeUpload(fileVO, file);
            // 执行上传
            uploade(fileVO);
            
    		TAssets assets = null;
    		String is_upload = commonProperties.getProperty("file.is.upload.fastdfs");
    		//是否需要上传的文件服务器
    		if(StringUtils.isNoneBlank(is_upload) && is_upload.equals("false")) {
    			assets = assetsService.analysisApk(fileVO, false, user, false, filePath);
    		}else{
    			assets = assetsService.analysisApk(fileVO, false, user, false, null);
    		}
            assets.setAppId(ccrcdto.getAppId());
            assets.setCreateUserId(logUserId);
//            if(StringUtils.isNoneBlank(privacyPolicyPath)) {
//            	assets.setPrivacyPolicyPath(privacyPolicyPath);
//            }
            //解析完成后调用大数据平台接口获取资产功能类型
//            assets=assetsOfBigDataService.getAssetsBigData(assets);
            assets.setSourceFileName(getFileName(filePath,file));
            assetsService.saveOrUpdate(assets);
            
            //添加检测任务
            TaskCreateQuery taskCreateQuery = new TaskCreateQuery();
            taskCreateQuery.setUserId(logUserId);
            List<Long> assetsIds = new ArrayList<>();
            assetsIds.add(assets.getId());
            taskCreateQuery.setAssetsIds(assetsIds);
            taskCreateQuery.setDynamicDeviceTypeEnum(DynamicDeviceTypeEnum.getItem(1)); //1 云手机
            taskCreateQuery.setTerminalTypeEnum(assets.getTerminalType());
            taskCreateQuery.setDetectionType(1);
//            taskCreateQuery.setBussinessId(bussinessId);
            taskCreateQuery.setCallbackUrl(ccrcdto.getCallbackUrl());
            taskCreateQuery.setPageOrApiOrKafkaEnum(PageOrApiOrKafkaEnum.IS_API);
            taskCreateQuery.setAppId(ccrcdto.getAppId());
//            taskCreateQuery.setPriority(priority);
//            taskCreateQuery.setPrivacyPolicyPath(privacyPolicyPath);
//            taskCreateQuery.setDataSources(commonProperties.getProperty("data.sources"));
            
            
            Long taskId = taskService.startTask(taskCreateQuery);
            
            task  = taskService.findById(taskId);
            CCRCVO ccrcvo = new CCRCVO();
            ccrcvo.setApkId(ccrcdto.getApkId());
            ccrcvo.setDocumentId(task.getApkDetectionDetailId());
            baseResponse.setData(ccrcvo);
        } catch (Exception e) {
            e.getMessage();
            log.error("fileUploadAndDetect.apk下载失败");
            baseResponse.setStatus(500);
            baseResponse.setMessage("apk下载失败。");
        }
        return baseResponse;
    }
    
    // 上传前操作
    private void beforeUpload(FileVO fileVO, File file) throws IOException, IjiamiApplicationException {
        fileVO.setInputStream(new FileInputStream(file));
        fileVO.setFileSize(FileUtils.sizeOf(file));

        String fileName = file.getName().replaceAll(" ", "").trim();
        CommonUtil.checkAppFileExt(fileName);
        String MD5 = MD5Util.getFileMD5(new FileInputStream(file));
        fileName = UUID.randomUUID().toString().replace("_", "") + "_" + MD5
                + fileName.substring(fileName.indexOf("."));
        fileVO.setFileName(fileName);
        Map<String, Object> params = new HashMap<>();
        DecimalFormat df = new DecimalFormat("0.00");
        String parseLong = df.format((double) fileVO.getFileSize() / ConstantsUtils.BYTENUM / ConstantsUtils.BYTENUM);
        params.put("size", parseLong);
        fileVO.setParams(params);
    }
    
    /**
     * 比较上传的url是否包含源文件名，不包含则以下载的文件名作为源文件名
     * @param url
     * @param file
     * @return
     */
    private String getFileName(String url,File file){
        String sourceFileName="";
        if(StringUtils.isNotBlank(url) && url.toUpperCase().endsWith(".APK")){
            int star = url.lastIndexOf("/");
            int end = url.length();
            sourceFileName=url.substring(star+1,end);
        }else if(file!=null){
            sourceFileName=file.getName();
        }
        return sourceFileName;
    }
    

    /**
     * 获取检测状态
     *
     * @param bussinessId
     * @return
     */
    @GetMapping(value = "/status/{bussinessId}")
    @ApiOperation(value = "获取检测数据的检测状态")
    public BaseResponse<Object> status(@PathVariable(value = "bussinessId") String bussinessId, HttpServletRequest request, HttpServletResponse res) {
    	 BaseResponse<Object> response = new BaseResponse<>();

    	 String documentId = request.getParameter("documentId");

    	 if (bussinessId == null || documentId==null) {
            response.setStatus(500);
            response.setMessage("请求异常,任务ID不能为空");
            return response;
        }

    	TTask task = taskService.findByDocumentId(documentId);

    	Map<String, Object> map = new HashMap<>();



//    	TTask task = taskServiceImpl.findById(apiTask.getTaskId());
    	log.info("{}获取检测数据的检测状态2={}", bussinessId,(task==null?"null":"no_null"));
        if (task == null) {
            response.setStatus(500);
            response.setMessage("请求异常,任务不存在");
            return response;
        }

        map.put("staticStatus", task.getTaskTatus().getValue());
        map.put("static_status", task.getTaskTatus().getValue());

        DynamicAutoStatusEnum autoStatus = task.getDynamicStatus();
        Integer status = null;
        if (autoStatus == DynamicAutoStatusEnum.DETECTION_AUTO_WAITING) {
            status = 1;
        }

        if (autoStatus == DynamicAutoStatusEnum.DETECTION_AUTO_IN || autoStatus == DynamicAutoStatusEnum.DETECTION_AUTO_WAIT_HIT
                || autoStatus == DynamicAutoStatusEnum.DETECTION_AUTO_HIT || autoStatus == DynamicAutoStatusEnum.DETECTION_AUTO_DOWNLOAD_IPA) {
            status = 2;
        }

        if(autoStatus == DynamicAutoStatusEnum.DETECTION_AUTO_FAILED) {
        	TaskDetailVO taskDetail = commonMongodbService.findByDocumentId(task.getApkDetectionDetailId());
        	if(taskDetail != null && StringUtils.isNoneBlank(taskDetail.getDynamic_detection_description())
        			&& taskDetail.getDynamic_detection_description().contains("安装失败")){
        		status = 5;
        	}else {
        		 status = 3;
        	}
        }
        if (autoStatus == DynamicAutoStatusEnum.DETECTION_AUTO_SUCCEED) {
            status = 4;
        }

        map.put("dynamicStatus", status);
        map.put("dynamic_status", status);
        response.setData(map);
        response.setMessage("查询成功");

        return response;
    }

    @ApiOperation(value = "获取完整的检测结果")
    @GetMapping("/detail/{documentId}")
    public BaseResponse<DetectionResultVO> getDetectionDetailResult(@ApiParam(value = "已完成全部检测的任务文档Id") @PathVariable(value = "documentId") String documentId)
            throws IjiamiApplicationException {
        if (documentId == null) {
            throw new IjiamiApplicationException("检测任务不存在");
        }
        BaseResponse<DetectionResultVO> baseResponse = new BaseResponse<>();
        baseResponse.setData(privacyDetectionService.getDetectionResultV1(documentId));
        return baseResponse;
    }

    
    
    // 执行上传
    private void uploade(FileVO fileVO) throws IjiamiApplicationException, IOException {
        // 执行上传
        fileVO = defaultService.upload(fileVO);
        if(StringUtils.isBlank(fileVO.getFilePath())){
        	fileVO.setFilePath(fileVO.getFileUrl()+fileVO.getRelativePath());
        }
    }
    
    // 上传前操作
    private void beforUpload(FileVO fileVO, MultipartFile file) throws IOException, IjiamiApplicationException {
        fileVO.setInputStream(file.getInputStream());
        fileVO.setFileSize(file.getSize());
        String fileName = file.getOriginalFilename().replaceAll(" ", "").trim();
        CommonUtil.checkAppFileExt(fileName);
        String MD5 = MD5Util.getFileMD5(file.getInputStream());
        String uuid = UUID.randomUUID().toString().replace("-", "");
        // 固定格式的文件名格式，后续根据此获取MD5
        fileName = ConstantsUtils.ASSET_SIGN + MD5 + ConstantsUtils.FILE_NAME_SEPARATOR + uuid + ConstantsUtils.FILE_NAME_SEPARATOR + fileName.substring(fileName.indexOf("."));
        fileVO.setFileName(fileName);
        Map<String, Object> params = new HashMap<>();
        DecimalFormat df = new DecimalFormat("0.00");
        String parseLong = df.format((double) file.getSize() / ConstantsUtils.BYTENUM / ConstantsUtils.BYTENUM);
        params.put("size", parseLong);
        fileVO.setParams(params);
    }
}
