package cn.ijiami.detection.rest.controller;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.ijiami.detection.VO.idb.TokenResultVO;
import cn.ijiami.detection.entity.TSdkLibrary;
import cn.ijiami.detection.entity.TSdkLibraryPackage;
import cn.ijiami.detection.enums.IdbStagedDataEnum;
import cn.ijiami.detection.enums.IdbUploadDataTypeEnum;
import cn.ijiami.detection.mapper.TSdkLibraryMapper;
import cn.ijiami.detection.mapper.TSdkLibraryPackageMapper;
import cn.ijiami.detection.query.idb.TokenQuery;
import cn.ijiami.detection.service.api.ClientService;
import cn.ijiami.detection.service.api.IDBInteractService;
import cn.ijiami.detection.service.api.IHitShellService;
import cn.ijiami.detection.service.api.IPrivacyLogCrtlService;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName UploadDataController.java
 * @Description 数据上传接口
 * @createTime 2022年01月12日 15:46:00
 */
@Slf4j
@RequestMapping("/idb/interact")
@Api(value = "/idb/interact", tags = "idb交互服务")
@RestController()
public class IDBInteractController {

    @Autowired
    private ClientService clientService;

    @Autowired
    private IHitShellService iHitShellService;

    @Autowired
    private IDBInteractService idbInteractService;
    
    @Autowired
    private TSdkLibraryMapper sdkLibraryMapper;

    @Autowired
    private TSdkLibraryPackageMapper sdkLibraryPackageMapper;


    @ApiOperation("全自动检测完成上传数据包")
    @PostMapping("/upload/data/auto/{taskId}")
    public BaseResponse<?> uploadAutoData(@RequestHeader(value = "authorization", required = true) String authorization,
                                          @ApiParam(value = "任务id", required = true) @PathVariable Long taskId,
                                          @ApiParam(value = "数据类型, 默认为1, 1 检测数据压缩包 2 隐私政策文件 3 行为数据文件", required = false)
                                              @RequestParam(value = "type", required = false, defaultValue = "1") Integer type,
                                          @ApiParam(value = "数据阶段, 默认为0, 0 不分阶段的数据包 1 授权前阶段 2 授权拒绝阶段 3 授权同意阶段 4 前台阶段 5 摇一摇阶段 6 后台阶段 7 退出阶段", required = false)
                                              @RequestParam(value = "stage", required = false, defaultValue = "0") Integer stage,
                                          @ApiParam(value = "数据包", required = true) MultipartFile data) throws IOException, IjiamiApplicationException {
        if (!idbInteractService.invalidToken(authorization)) {
            throw new IjiamiApplicationException("token失效");
        }
        log.info("TaskId:{} stage:{} uploadAutoData", taskId, stage);
        clientService.analysisAuto(taskId, IdbUploadDataTypeEnum.getItem(type), IdbStagedDataEnum.getItem(stage), data);
        return new BaseResponse<>();
    }

    @ApiOperation("深度检测完成上传数据包")
    @PostMapping("/upload/data/depth/{taskId}")
    public BaseResponse<?> uploadDepthData(@RequestHeader(value = "authorization", required = true) String authorization,
                                          @ApiParam(value = "任务id", required = true) @PathVariable Long taskId,
                                          @ApiParam(value = "数据包", required = true) MultipartFile data) throws IOException, IjiamiApplicationException {
        if (!idbInteractService.invalidToken(authorization)) {
            throw new IjiamiApplicationException("token失效");
        }
        log.info("TaskId:{} uploadDepthData", taskId);
        clientService.analysisManual(taskId, data);
        return new BaseResponse<>();
    }

    @ApiOperation("砸壳完成上传数据包")
    @PostMapping("/upload/data/shell/{businessId}")
    public BaseResponse<?> uploadShellData(@RequestHeader(value = "authorization", required = true) String authorization,
                                          @ApiParam(value = "砸壳id", required = true) @PathVariable String businessId,
                                          @ApiParam(value = "数据包", required = true) MultipartFile data) throws IOException, IjiamiApplicationException {
        if (!idbInteractService.invalidToken(authorization)) {
            throw new IjiamiApplicationException("token失效");
        }
        iHitShellService.hitShellSuccess(Long.parseLong(businessId), data);
        log.info("BusinessId:{} uploadShellData", businessId);
        return new BaseResponse<>();
    }

    @ApiOperation("上传文件")
    @PostMapping("/upload/file")
    public BaseResponse<String> uploadFile(@RequestHeader(value = "authorization", required = true) String authorization,
                                           @ApiParam(value = "文件", required = true) MultipartFile file) throws IOException, IjiamiApplicationException {
        if (!idbInteractService.invalidToken(authorization)) {
            throw new IjiamiApplicationException("token失效");
        }
        BaseResponse<String> baseResponse = new BaseResponse<>();
        baseResponse.setData(idbInteractService.uploadFile(file, IDBInteractService.NEVER_EXPIRES));
        log.info("name:{} uploadImage", file.getName());
        return baseResponse;
    }

    @ApiOperation("获取token")
    @PostMapping("/oauth/token")
    public BaseResponse<TokenResultVO> token(@RequestBody @Valid TokenQuery query) throws Exception{
        BaseResponse<TokenResultVO> response = new BaseResponse<>();
        response.setData(idbInteractService.genToken(query));
        return response;
    }

    @ApiOperation("idb调用后获取需要监控的行为信息")
    @PostMapping("/findPernalActionId")
    public BaseResponse<String> findPernalActionId(@RequestHeader(value = "authorization", required = true) String authorization,
                                                   @ApiParam(value = "类型",required = true) @RequestParam(value = "terminalType") String terminalType) throws IjiamiApplicationException{
        if(!idbInteractService.invalidToken(authorization)){
            throw new IjiamiApplicationException("token失效");
        }
        BaseResponse<String> baseResponse = new BaseResponse<>();
        baseResponse.setData(idbInteractService.findActionIdOfPersinal(Integer.valueOf(terminalType)));
        return baseResponse;
    }
    
    @ApiOperation("获取SDK数据-提供给工具使用")
    @PostMapping("/getAllSDKLibrary")
    public BaseResponse<Object> getAllSDKLibrary(@RequestHeader(value = "authorization", required = true) String authorization) throws IjiamiApplicationException{
        if(!idbInteractService.invalidToken(authorization)){
            throw new IjiamiApplicationException("token失效");
        }
        BaseResponse<Object> baseResponse = new BaseResponse<>();
        Map<String,Object> map = new HashMap<>();
        List<TSdkLibrary> list = sdkLibraryMapper.selectAll();
        List<TSdkLibraryPackage> pageList = sdkLibraryPackageMapper.selectAll();
        map.put("t_sdk_library", list);
        map.put("t_sdk_library_package", pageList);
        baseResponse.setData(map);
        return baseResponse;
    }
}
