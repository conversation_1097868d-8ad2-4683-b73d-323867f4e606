package cn.ijiami.detection.rest.controller;

import cn.ijiami.base.common.log.OperateLog;
import cn.ijiami.base.common.user.IUser;
import cn.ijiami.detection.VO.detection.privacy.PrivacyPolicyTypeVO;
import cn.ijiami.detection.controller.DetectionBaseController;
import cn.ijiami.detection.entity.TPrivacyCheck;
import cn.ijiami.detection.query.DetailLawsQuery;
import cn.ijiami.detection.query.LawQuery;
import cn.ijiami.detection.service.api.ILawConfigureService;
import cn.ijiami.framework.common.exception.IjiamiRuntimeException;
import cn.ijiami.framework.common.response.BaseResponse;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
/**
 * Description:法规配置管理类
 *
 * @Author:lyl
 * @Date:2023/11/28 16:21
 */
@RestController
@RequestMapping("/api/lawConfigure")
@Api(value = "/api/lawConfigure",tags = "法规配置模块")
public class LawConfigureController extends DetectionBaseController {
    @Autowired
    private ILawConfigureService lawConfigureService;


    @ApiOperation(value = "法规配置--->查询深度检测法规")
    @PostMapping(value = "/findLaws")
    @OperateLog(
            moduleName = "法规配置模块",
            operateName = "查询法规数据"
    )
    public BaseResponse<PageInfo<PrivacyPolicyTypeVO>> findLaws(@RequestBody LawQuery lawQuery){
        if(lawQuery.getTerminalType() == null){
            throw new IjiamiRuntimeException("终端类型为空!");
        }
        BaseResponse<PageInfo<PrivacyPolicyTypeVO>> response = new BaseResponse<>();
        response.setData(lawConfigureService.findLaws(lawQuery));
        return response;
    }

    @ApiOperation(value = "法规配置--->查询当前法规具体检测项")
    @PostMapping(value = "/fidLawDetails")
    @OperateLog(
            moduleName = "法规配置模块",
            operateName = "查询具体检测项信息"
    )
    public BaseResponse<PageInfo<TPrivacyCheck>> findLawDetails(@RequestBody LawQuery lawQuery) throws Exception{
        if(lawQuery.getTerminalType() == null || lawQuery.getTerminalType().getValue() == 0){
            throw new IjiamiRuntimeException("终端类型不能为空!");
        }
        BaseResponse<PageInfo<TPrivacyCheck>> response = new BaseResponse<>();
        response.setData(lawConfigureService.findLawDetails(lawQuery.getTerminalType().getValue(),lawQuery.getLawName(),lawQuery.getPage(),lawQuery.getRows()));
        return response;
    }

    @ApiOperation(value = "法规配置--->保存法规及检测项数据")
    @PostMapping(value = "/saveLaws")
    @OperateLog(
            moduleName = "法规配置模块",
            operateName = "保存法规及检测项数据"
    )
    public BaseResponse saveLaws(@RequestBody DetailLawsQuery query){
        if(query.getTerminalType() == null){
            throw new IjiamiRuntimeException("终端类型不能为空!");
        }
        IUser currentUser = getCurrentUser();
        BaseResponse response = new BaseResponse<>();

        lawConfigureService.saveLaws(query,currentUser.getUserId());
        return response;
    }

    @ApiOperation(value = "法规配置--->修改法规状态（发布或下架）")
    @PostMapping(value = "/updateLawStatus")
    @OperateLog(
            moduleName = "法规配置模块",
            operateName = "修改法规状态（发布或下架）"
    )
    public BaseResponse updateLawStatus(@RequestBody DetailLawsQuery query) throws Exception{
        if(query.getTerminalType() == null){
            throw new IjiamiRuntimeException("终端类型不能为空!");
        }
        BaseResponse response = new BaseResponse<>();
        IUser currentUser = getCurrentUser();
        lawConfigureService.updateLawStatus(query.getId(),query.getTerminalType(),currentUser.getUserId(),query.getPushStatus());
        return response;
    }

    @ApiOperation(value = "法规配置--->删除法规操作")
    @PostMapping(value = "/deletelaws")
    @OperateLog(
            moduleName = "法规配置模块",
            operateName = "删除法规"
    )
    public BaseResponse deletelaws(@RequestBody DetailLawsQuery query){
        if(query.getTerminalType() == null){
            throw new IjiamiRuntimeException("终端类型不能为空!");
        }
        BaseResponse response = new BaseResponse();
        IUser currentUser = getCurrentUser();
        lawConfigureService.deleteLaws(query.getId(),currentUser.getUserId());
        return response;
    }

    @ApiOperation(value = "法规配置--->删除法规检测项操作")
    @PostMapping(value = "/deletelawDetails")
    @OperateLog(
            moduleName = "法规配置模块",
            operateName = "删除检测项"
    )
    public BaseResponse deletelawDetails(@RequestBody DetailLawsQuery query){
        if(query.getTerminalType() == null){
            throw new IjiamiRuntimeException("终端类型不能为空!");
        }
        BaseResponse response = new BaseResponse();
        IUser currentUser = getCurrentUser();
        lawConfigureService.deletelawDetails(query,currentUser.getUserId());
        return response;
    }

    @ApiOperation(value = "法规配置--->缓存检测项数据")
    @PostMapping("/saveCache")
    public BaseResponse saveCache(@RequestBody DetailLawsQuery query) throws Exception{
        if(query.getTerminalType() == null){
            throw new IjiamiRuntimeException("终端类型不能为空!");
        }
        BaseResponse response = new BaseResponse<>();
        IUser currentUser = getCurrentUser();
        lawConfigureService.saveCache(query,currentUser.getUserId());
        return response;
    }

    @ApiOperation(value = "法规配置--->返回接口")
    @PostMapping("/clearCache")
    public BaseResponse clearCache(@RequestBody DetailLawsQuery query) throws Exception{
        BaseResponse response = new BaseResponse();
        if(query.getTerminalType() == null){
            throw  new IjiamiRuntimeException("终端类型不能为空!");
        }
        lawConfigureService.clearCache(query);
        return response;
    }
}
