package cn.ijiami.detection.rest.controller;

import java.io.IOException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.github.pagehelper.PageInfo;

import cn.ijiami.detection.VO.UploadImageVO;
import cn.ijiami.detection.controller.DetectionBaseController;
import cn.ijiami.detection.helper.ResponseHelper;
import cn.ijiami.detection.query.AITrainImagePageQuery;
import cn.ijiami.detection.query.AITrainImageVo;
import cn.ijiami.detection.query.SaveAITrainImage;
import cn.ijiami.detection.service.api.AITrainService;
import cn.ijiami.detection.utils.HttpUtils;
import cn.ijiami.framework.common.exception.IjiamiApplicationException;
import cn.ijiami.framework.common.response.BaseResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AIModelController.java
 * @Description ai训练相关接口
 * @createTime 2024年03月06日 15:04:00
 */
@Slf4j
@RestController
@RequestMapping("/api/ai/train")
@Api(value = "/api/ai/train",tags = "ai训练相关接口")
public class AITrainController extends DetectionBaseController {

    @Autowired
    private AITrainService aiTrainService;

    @PostMapping("/upload/image")
    public BaseResponse<UploadImageVO> uploadImage(
            @ApiParam(value = "图片", required = true) @RequestParam("file") MultipartFile data) throws IOException, IjiamiApplicationException {
        log.info("uploadImage");
        UploadImageVO uploadImageVO = new UploadImageVO();
        uploadImageVO.setImageUrl(aiTrainService.uploadImage(getCurrentUser(), data));
        BaseResponse<UploadImageVO> response = new BaseResponse<>();
        response.setData(uploadImageVO);
        log.info("finish uploadImage");
        return response;
    }

    @ApiOperation(value = "保存或更新图片训练数据")
    @PostMapping(value = "/image/item/saveOrUpdate", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<String> saveOrUpdate(@RequestBody @Valid SaveAITrainImage image) throws IOException, IjiamiApplicationException {
        log.info("saveOrUpdate");
        aiTrainService.saveOrUpdate(getCurrentUser(), image.getId(), image.getCategory(), image.getImageUrl(), null);
        log.info("finish saveOrUpdate");
        return new BaseResponse<>();
    }

    @ApiOperation(value = "获取图片数据列表")
    @PostMapping(value = "/image/findByPage", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<PageInfo<AITrainImageVo>> findImageByPage(@RequestBody @Valid AITrainImagePageQuery query){
        BaseResponse<PageInfo<AITrainImageVo>> response = new BaseResponse<>();
        response.setData(aiTrainService.findImageByPage(query));
        return response;
    }

    @ApiOperation(value = "获取新增数据条数", notes = "获取新增数据条数")
    @GetMapping(value = "/image/new/count")
    public BaseResponse<Integer> countNewImage(){
        BaseResponse<Integer> response = new BaseResponse<>();
        response.setData(aiTrainService.countNewImage());
        return response;
    }

    @ApiOperation(value = "导出图片数据训练集", notes = "导出图片数据训练集")
    @GetMapping(value = "/image/export")
    public void export(HttpServletRequest request, HttpServletResponse response){
        HttpUtils.copyFile(aiTrainService.exportImage(getCurrentUser()), request,response);
    }

    @ApiOperation(value = "删除标记图片", notes = "删除sdk")
    @PostMapping(value = "/delete/image/{id}", produces = MediaType.APPLICATION_JSON_VALUE)
    public BaseResponse<String> updateAlias(@PathVariable("id") Long id) throws IjiamiApplicationException {
        aiTrainService.deleteImage(getCurrentUser(), isAdmin(), id);
        return ResponseHelper.successMessage("删除成功");
    }
}
