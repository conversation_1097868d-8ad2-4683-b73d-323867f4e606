---
description: 
globs: 
alwaysApply: false
---
## java文件路径
枚举类请放到这个`detection-service-api\src\main\java\cn\ijiami\detection\enums`目录
数据库表对象请放到这个`detection-service-api\src\main\java\cn\ijiami\detection\entity`目录
service的interface类请放到这个`detection-service-api\src\main\java\cn\ijiami\detection\api`目录
返回给前端的VO类请放到这个`detection-service-api\src\main\java\cn\ijiami\detection\VO`目录
Controller中的请求参数类请放到这个`detection-service-api\src\main\java\cn\ijiami\detection\VO`目录

## import规则
同一个目录下class不需要用import显式导入

## class规则
类注释请用下面模板
```
/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName <类目，例如：AIAssistantServiceImpl.java>
 * @Description <功能描述>
 * @createTime <创建时间，例如：2024年09月13日 18:39:00>
 */
```

## method规则
方法注释请用下面模板
```
/**
* <方法功能描述，例如：隐私政策文本辅助>
* @param user 用户
* @param file 文件
* @param url url
* @return FormattedPrivacyAnalysisResponseVO 返回封装后的对象
*/
```



